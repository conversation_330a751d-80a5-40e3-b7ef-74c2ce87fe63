<x-app-layout>
    <x-slot name="header">
        Dashboard
    </x-slot>
    <div class="row">
        @if (canPermission('Surat Masuk.List'))
        <div class="col-xxl-3 col-lg-6 col-md-6">
            <a href="{{ route('surat-masuk.index') }}" class="card stretch stretch-full position-relative">
                @if ($surat_masuk_unread_count > 0)
                <div class="position-absolute" style="
                        top: -10px;
                        right: -10px;">
                    <div
                        class="text-white fw-bolder fs-14"
                        style="
                        background: red;
                        width: 35px;
                        height: 35px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        padding: 10px;
                        ">
                        {{ $surat_masuk_unread_count }}
                    </div>
                </div>
                @endif
                <div class="card-body rounded-3 text-center">
                    <i class="
                    bi bi-envelope
                     text-primary" style="font-size: 80px"></i>
                    <div class="fs-2 fw-bolder mt-3 mb-1 text-primary">
                        {{$surat_masuk_count}}
                    </div>
                    <p class="fs-16 fw-medium text-primary text-spacing-1 mb-0 text-truncate-1-line">
                        Surat Masuk
                    </p>
                </div>
            </a>
        </div>
        @endif
        @if (canPermission('Surat Kadaluarsa.List'))
        <div class="col-xxl-3 col-lg-6 col-md-6">
            <a href="{{ route('surat-expired.index') }}"" class="card stretch stretch-full">
                <div class="card-body rounded-3 text-center">
                    <i class="
                    bi bi-envelope-open
                    text-danger" style="font-size: 80px"></i>
                    <div class="fs-2 fw-bolder mt-3 mb-1 text-danger">
                        {{$surat_kadaluarsa_count}}
                    </div>
                    <p class="fs-16 fw-medium text-danger text-spacing-1 mb-0 text-truncate-1-line">
                        Surat Kadaluarsa
                    </p>
                </div>
            </a>
        </div>
        @endif
        {{-- @if (canPermission('Disposisi.List')) --}}
        <div class="col-xxl-3 col-lg-6 col-md-6">
            <a href="{{ route('surat-masuk.index') }}" class="card stretch stretch-full">
                <div class="card-body rounded-3 text-center">
                    <i class="
                    bi bi-file-earmark-text
                    text-dark" style="font-size: 80px"></i>
                    <div class="fs-2 fw-bolder mt-3 mb-1 text-dark">
                        {{ $disposisi_count }}
                    </div>
                    <p class="fs-16 fw-medium text-dark text-spacing-1 mb-0 text-truncate-1-line">
                        Disposisi
                    </p>
                </div>
            </a>
        </div>
        {{-- @endif --}}
    </div>

</x-app-layout>
