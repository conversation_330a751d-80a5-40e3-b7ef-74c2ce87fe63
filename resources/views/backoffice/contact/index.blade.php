<x-app-layout>
    <x-slot name="header">
        Kontak
    </x-slot>
    <x-slot name="headerRight">
        <x-slot name="headerRight">

        </x-slot>

        <div class="row">
            <div class="col-lg-12">
                <div class="card stretch stretch-full">
                    <div class="card-body">
                        <div class="d-flex align-items-center flex-wrap">
                            <div class="" style="width: 300px">
                                <input type="text" class="form-control" placeholder="Cari Kontak"
                                    oninput="handleSearch(event)" style="width: 100%" />
                            </div>
                            <div class="ms-md-auto mt-md-0 mt-3">
                                @if (canPermission('Kontak.Create'))
                                <div>
                                    <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                                        data-bs-target="#modalInputUnitBisnis">
                                        <i class="feather-plus me-2"></i>
                                        <span>Tambah Kontak</span>
                                    </button>
                                </div>
                                @endif
                            </div>
                        </div>
                        <div class="table-responsive mt-4">
                            <table class="table table-hover" id="example">
                                <thead>
                                    <tr>
                                        <th style="width: 12px">No</th>
                                        <th>Nama</th>
                                        <th>Email</th>
                                        <th>Keterangan</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @push('modals')
            <div class="modal fade-scale" id="modalInputUnitBisnis" tabindex="-1" data-bs-backdrop="static"
                data-bs-keyboard="false" aria-hidden="true" data-bs-dismiss="ou">
                <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <!--! BEGIN: [modal-header] !-->
                        <div class="modal-header">
                            <h2 class="d-flex flex-column mb-0">
                                <span class="fs-18 fw-bold mb-1">Kontak</span>
                                <small class="d-block fs-11 fw-normal text-muted">
                                    Input data Kontak
                                </small>
                            </h2>
                            <a href="javascript:void(0)" class="avatar-text avatar-md bg-soft-danger close-icon"
                                data-bs-dismiss="modal">
                                <i class="feather-x text-danger"></i>
                            </a>
                        </div>
                        <form id="formInputUnitBisnis" action="{{ route('kontak.store') }}" method="POST">
                            <div class="modal-body" style="min-height: 300px">
                                @csrf
                                <input type="hidden" name="kontak_id" id="kontak_id">
                                <div class="form-group mb-4">
                                    <label for="nama" class="form-label">Nama</label>
                                    <input type="text" class="form-control" id="nama" name="nama"
                                        placeholder="Masukkan Nama">
                                </div>
                                <div class="form-group mb-4">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="text" class="form-control" id="email" name="email"
                                        placeholder="Masukkan Email">
                                </div>
                                <div class="form-group mb-4">
                                    <label for="keterangan" class="form-label">Keterangan</label>
                                    <textarea class="form-control" id="keterangan" name="keterangan"
                                        placeholder="Masukkan Keterangan"></textarea>
                                </div>
                            </div>
                            <div class="modal-footer d-flex align-items-center justify-content-between gap-3 w-100">
                                <button type="button" class="btn btn-outline-secondary"
                                    data-bs-dismiss="modal">Batal</button>
                                <button class="btn btn-primary">Simpan</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        @endpush

        @include('libs.datatable')
        @push('styles')
            <link rel="stylesheet" type="text/css" href="/assets/vendors/css/select2.min.css">
        @endpush
        @push('scripts')
            <script src="/assets/vendors/js/select2.min.js"></script>
            <script>
                $.ajaxSetup({
                    headers: {
                        "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr("content"),
                    },
                });

                let filters = {};
                const table = $("#example").DataTable({
                    lengthMenu: [
                        [10, 25, 50, 100, 500, -1],
                        [10, 25, 50, 100, 500, "All"],
                    ],
                    searching: false,
                    responsive: false,
                    lengthChange: true,
                    autoWidth: false,
                    order: [],
                    pagingType: "full_numbers",
                    dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                    language: {
                        search: "_INPUT_",
                        searchPlaceholder: "Cari...",
                        paginate: {
                            Search: '<i class="icon-search"></i>',
                            first: "<i class='fas fa-angle-double-left'></i>",
                            previous: "<i class='fas fa-angle-left'></i>",
                            next: "<i class='fas fa-angle-right'></i>",
                            last: "<i class='fas fa-angle-double-right'></i>",
                        },
                    },
                    oLanguage: {
                        sSearch: "",
                    },
                    processing: true,
                    serverSide: true,
                    ajax: {
                        url: `${base_url}/kontak/dataTable`,
                        method: "POST",
                        data: function(d) {
                            return {
                                ...d,
                                ...filters,
                            };
                        },
                    },
                    columns: [{
                            name: "created_at",
                            data: "DT_RowIndex",
                        },
                        {
                            name: "nama",
                            data: "nama",
                            orderable: false,
                        },
                        {
                            name: "email",
                            data: "email",
                            orderable: false,
                        },
                        {
                            name: "keterangan",
                            data: "keterangan",
                            orderable: false,
                        },
                        {
                            name: "action",
                            data: "action",
                            orderable: false,
                        },

                    ],
                });

                let debounceTimer;

                function handleSearch(e) {
                    clearTimeout(debounceTimer);
                    debounceTimer = setTimeout(() => {
                        filters.keyword = e.target.value;
                        table.draw();
                    }, 500);
                }

                const formInputUnitBisnis = $("#formInputUnitBisnis");
                const submitBtn = formInputUnitBisnis.find("button[type=submit]");
                const modalInputUnitBisnis = $("#modalInputUnitBisnis");
                const cancelInput = () => {
                    formInputUnitBisnis.find("kontak_id").val("");
                    formInputUnitBisnis.find("input").val("");
                    formInputUnitBisnis.find("textarea").val("");
                    formInputUnitBisnis.modal("hide");
                };

                formInputUnitBisnis.on("submit", async function(e) {
                    e.preventDefault();
                    const action = $(this).attr("action");
                    const formData = new FormData(this);
                    console.log(formData)
                    const result = await sendData(action, 'POST', formData);
                    if (result.status) {
                        submitBtn.attr('disabled', false).html('Simpan');
                        cancelInput();
                        table.draw();
                        Swal.fire(`Berhasil disimpan`, result.message, "success");
                        modalInputUnitBisnis.modal("hide");
                    } else {
                        submitBtn.attr('disabled', false).html('Simpan');
                        Swal.fire(`Gagal disimpan`, result.message, "error");
                    }
                });


                $(document).on("click", '.editInput', async function() {
                    const id = $(this).data("id");
                    const dataInput = $(this).data("input");

                    formInputUnitBisnis.find("#kontak_id").val(id);
                    formInputUnitBisnis.find("#nama").val(dataInput.nama);
                    formInputUnitBisnis.find("#email").val(dataInput.email);
                    formInputUnitBisnis.find("#keterangan").val(dataInput.keterangan);
                    modalInputUnitBisnis.modal("show");
                }).on('click', '.deleteData', async function() {
                    const id = $(this).data("id");
                    const dataInput = $(this).data("input");
                    const nama = dataInput.jenis_surat;
                    const urlTarget = `${base_url}/kontak/${id}`
                    await deleteDataTable(nama, urlTarget, table)
                });

                $(document).on('hide.bs.modal', '#modalInputUnitBisnis', function() {
                    cancelInput();
                });
            </script>
        @endpush
</x-app-layout>
