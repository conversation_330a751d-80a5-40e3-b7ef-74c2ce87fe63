<x-app-layout>
    <x-slot name="header">
        Surat Kadaluarsa
    </x-slot>
    <x-slot name="headerRight">

    </x-slot>

    <div class="row">
        <div class="col-lg-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <div class="d-flex align-items-center flex-wrap">
                        <div class="d-flex align-items-center gap-2" style="width: 350px">
                            <div class="w-100">
                                <input type="text" class="form-control" placeholder="Cari surat masuk"
                                    oninput="handleSearch(event)" style="width: 100%" />
                            </div>
                            <div role="button" title="Refresh" class="btn btn-primary btnRefresh"
                                data-bs-toggle="tooltip" data-bs-placement="top" title="Refresh"
                                data-bs-trigger="hover">
                                <i class="fas fa-refresh"></i>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive mt-4">
                        <table class="table table-hover" id="example">
                            <thead>
                                <tr>
                                    <th style="width: 12px">No</th>
                                    <th>Pengirim</th>
                                    <th>Perihal</th>
                                    <th>Tanggal</th>
                                    <th>No. Surat</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('modals')
    <div className="" id="loading"
    style="
        position: fixed;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        display: none;
    "
    >
    <div
        class="spinner-border text-primary"
        role="status"
        style="
        position: absolute;
        top: 50%;
        left: 50%;
        "
    >
        <span class="visually-hidden">Loading...</span>
    </div>
    </div>
    @endpush

    @include('libs.datatable')
    @push('scripts')
        <script>
            $.ajaxSetup({
                headers: {
                    "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr("content"),
                },
            });

            let filters = {
                surat_expired: 1
            };
            const table = $("#example").DataTable({
                lengthMenu: [
                    [10, 25, 50, 100, 500, -1],
                    [10, 25, 50, 100, 500, "All"],
                ],
                searching: false,
                responsive: false,
                lengthChange: true,
                autoWidth: false,
                order: [],
                pagingType: "full_numbers",
                dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                language: {
                    search: "_INPUT_",
                    searchPlaceholder: "Cari...",
                    paginate: {
                        Search: '<i class="icon-search"></i>',
                        first: "<i class='fas fa-angle-double-left'></i>",
                        previous: "<i class='fas fa-angle-left'></i>",
                        next: "<i class='fas fa-angle-right'></i>",
                        last: "<i class='fas fa-angle-double-right'></i>",
                    },
                },
                oLanguage: {
                    sSearch: "",
                },
                processing: true,
                serverSide: true,
                ajax: {
                    url: `${base_url}/surat-masuk/dataTable`,
                    method: "POST",
                    data: function(d) {
                        console.log(filters);
                        return {
                            ...d,
                            ...filters,
                        };
                    },
                },
                columns: [{
                        name: "created_at",
                        data: "DT_RowIndex",
                    },
                    {
                        name: "nama",
                        data: (row) => {
                            return `
                                    <a href="/surat-masuk/${row?.parent_surat_id ? row?.parent_surat_id : row?.id}/detail" class="d-flex align-items-center">
                                        ${
                                            row?.pengirim?.avatar ? `<div class="avatar avatar-sm me-3">
                                                                                            <img src="${row.avatar}" alt="Avatar" class="avatar-img rounded-circle">
                                                                                        </div>` : ''
                                        }
                                        <div class="d-block">
                                            <span class="fw-bold">${row?.pengirim?.nama}</span>
                                            <div class="text-muted fw-normal">${row?.unit_bisnis?.nama}</div>
                                        </div>
                                    </a>
                                `
                        },
                        orderable: false,
                    },
                    {
                        name: "perihal",
                        data: "perihal",
                        orderable: false,
                    },
                    {
                        name: "date",
                        data: "date",
                        orderable: false,
                    },
                    {
                        name: "content_surat",
                        data: (row) => {
                            return row?.no_surat ?? '-';
                        },
                        orderable: false,
                    },
                    {
                        name: "status_surat",
                        data: "status_surat",
                        orderable: false,
                    },
                    {
                        name: "action",
                        className: "position-sticky top-0 end-0 bg-white-smoke",
                        data: "action",
                        orderable: false,
                    },

                ],
            });

            let debounceTimer;

            function handleSearch(e) {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    filters.keyword = e.target.value;
                    table.draw();
                }, 500);
            }

            $(document).on('click', '.deleteData', async function() {
                const id = $(this).data("id");
                const dataInput = $(this).data("input");
                const perihal = dataInput.perihal;
                const urlTarget = `${base_url}/surat-masuk/${id}`
                await deleteDataTable(perihal, urlTarget, table)
            }).on('click', '.btnRefresh', function() {
                table.draw();
            }).on('click', '.completeSurat', async function() {
                const data_surat = $(this).data("input");
                const id = $(this).data("id");
                Swal.fire({
                    title: "Apakah Anda yakin?",
                    text: `Anda akan menyatakan surat No ${data_surat?.no_surat} sebagai selesai.`,
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Ya, saya yakin!",
                    cancelButtonText: "Batal",
                }).then(async (result) => {
                    console.log(result);
                    if (result.value) {
                        $('#loading').show()
                        try {
                            const {
                                data
                            } = await axios.post(
                                `/surat-masuk/${id}/complete`, {}, {
                                    headers: {
                                        "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr(
                                            "content"),
                                    },
                                }
                            );
                            if (data?.status) {
                                table.draw();
                                Swal.fire(
                                    "Berhasil!",
                                    `Surat No ${data_surat?.no_surat ?? ""} telah selesai.`,
                                    "success"
                                );
                            }
                        } catch (error) {
                            console.log(error);
                        }
                        $('#loading').hide()
                    }
                })
            }).on('click', '.reminderSurat', async function() {
                const data_surat = $(this).data("input");
                const id = $(this).data("id");
                Swal.fire({
                    title: "Apakah Anda yakin?",
                    text: `Anda akan mengirimkan reminder surat No ${data_surat?.no_surat ?? ''}.`,
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Ya, saya yakin!",
                    cancelButtonText: "Batal",
                }).then(async (result) => {
                    console.log(result);
                    if (result.value) {
                        $('#loading').show()
                        try {
                            const {
                                data
                            } = await axios.post(
                                `/surat-expired/${id}/reminder`, {}, {
                                    headers: {
                                        "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr(
                                            "content"),
                                    },
                                }
                            );
                            if (data?.status) {
                                table.draw();
                                Swal.fire(
                                    "Berhasil!",
                                    `Surat No ${data_surat?.no_surat ?? ""} berhasil diingatkan.`,
                                    "success"
                                );
                            }
                        } catch (error) {
                            console.log(error);
                        }
                        $('#loading').hide()
                    }
                })
            });
        </script>
    @endpush
</x-app-layout>
