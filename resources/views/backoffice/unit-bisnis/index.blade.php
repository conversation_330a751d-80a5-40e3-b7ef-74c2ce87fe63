<x-app-layout>
    <x-slot name="header">
        Unit Bisnis
    </x-slot>
    <x-slot name="headerRight">
        <x-slot name="headerRight">

        </x-slot>

        <div class="row">
            <div class="col-lg-12">
                <div class="card stretch stretch-full">
                    <div class="card-body">
                        <div class="d-flex align-items-center flex-wrap">
                            <div class="" style="width: 300px">
                                <input type="text" class="form-control" placeholder="Cari unit bisnis"
                                    oninput="handleSearch(event)" style="width: 100%" />
                            </div>
                            <div class="ms-md-auto mt-md-0 mt-3">
                                @if (canPermission('Unit Bisnis.Create'))
                                    <div>
                                        <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                                            data-bs-target="#modalInputUnitBisnis">
                                            <i class="feather-plus me-2"></i>
                                            <span>Tambah Unit Bisnis</span>
                                        </button>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="table-responsive mt-4">
                            <table class="table table-hover" id="example">
                                <thead>
                                    <tr>
                                        <th style="width: 12px">No</th>
                                        <th>Nama</th>
                                        <th>Setting No Surat</th>
                                        {{-- <th>Keterangan</th> --}}
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @push('modals')
            <div class="modal fade-scale" id="modalInputUnitBisnis" tabindex="-1" data-bs-backdrop="static"
                data-bs-keyboard="false" aria-hidden="true" data-bs-dismiss="ou">
                <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                    <div class="modal-content">
                        <!--! BEGIN: [modal-header] !-->
                        <div class="modal-header">
                            <h2 class="d-flex flex-column mb-0">
                                <span class="fs-18 fw-bold mb-1">Unit Binis</span>
                                <small class="d-block fs-11 fw-normal text-muted">
                                    Input data unit bisnis
                                </small>
                            </h2>
                            <a href="javascript:void(0)" class="avatar-text avatar-md bg-soft-danger close-icon"
                                data-bs-dismiss="modal">
                                <i class="feather-x text-danger"></i>
                            </a>
                        </div>
                        <form id="formInputUnitBisnis" action="{{ route('unit-bisnis.store') }}" method="POST">
                            <div class="modal-body" style="min-height: 300px">
                                @csrf
                                <input type="hidden" name="unit_bisnis_id" id="unit_bisnis_id">
                                <div class="row g-3">
                                    <div class="form-group col-md-6">
                                        <label for="nama" class="form-label">Nama</label>
                                        <input type="text" class="form-control" id="nama" name="nama"
                                            placeholder="Nama">
                                    </div>
                                    {{-- <div class="form-group col-md-6">
                                        <label for="nama" class="form-label">Kode Unit</label>
                                        <input type="text" class="form-control" id="kode_unit_surat" name="kode_unit_surat"
                                            placeholder="Kode Unit ">
                                        <div class="p-2 mt-3" style="color: #FDB528; background-color: #fdb6281e">
                                            <i class=" fas fa-warning pe-2"></i>
                                            Cukup isikan kode unit nomor surat
                                        </div>
                                    </div> --}}
                                    <div class="form-group col-md-6">
                                        <label for="nama" class="form-label">Urutan No Surat (mulai dari):</label>
                                        <input type="number" class="form-control" id="start_no_urut" name="start_no_urut"
                                            placeholder="Masukkan no urut surat dimulai dari">
                                    </div>
                                    <div class="form-group col-md-8">
                                        <label for="nama" class="form-label">Urutan Nomor Surat</label>
                                        <input type="text" class="form-control" id="setting_no_surat"
                                            name="setting_no_surat" placeholder="Urutan Nomor Surat">
                                        <div class="p-2 mt-2" style="color: #666C71; background-color: #F5F6F6">
                                            {nomor_urut} -> Nomor Surat <br />
                                            {{-- {kode_unit_surat} -> Kode Unit Surat <br /> --}}
                                            {kode_jenis_surat} -> Kode Jenis Surat <br />
                                            {kode_tipe_jenis_surat} -> Kode Tipe Jenis Surat <br />
                                            {tanggal} -> Tanggal Aktual <br />
                                            {bulan} -> Bulan Aktual <br />
                                            {tahun} -> Tahun Aktual
                                        </div>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label for="nama" class="form-label">Merupakan Holding</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="true"
                                                name="is_holding"
                                                style="height: 16px; width: 16px"
                                                id="isHolding">
                                            <label class="form-check-label fs-14" for="isHolding">
                                               Yes, Holding
                                            </label>
                                        </div>
                                    </div>
                                    {{-- <div class="form-group mt-3">
                                        <label for="keterangan" class="form-label">Keterangan</label>
                                        <textarea class="form-control" id="keterangan" name="keterangan" placeholder="Keterangan"></textarea>
                                    </div> --}}
                                </div>
                            </div>
                            <div class="modal-footer d-flex align-items-center justify-content-between gap-3 w-100">
                                <button type="button" class="btn btn-outline-secondary"
                                    data-bs-dismiss="modal">Batal</button>
                                <button class="btn btn-primary">Simpan</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        @endpush

        @include('libs.datatable')
        @push('scripts')
            <script>
                $.ajaxSetup({
                    headers: {
                        "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr("content"),
                    },
                });

                let filters = {};
                const table = $("#example").DataTable({
                    lengthMenu: [
                        [10, 25, 50, 100, 500, -1],
                        [10, 25, 50, 100, 500, "All"],
                    ],
                    searching: false,
                    responsive: false,
                    lengthChange: true,
                    autoWidth: false,
                    order: [],
                    pagingType: "full_numbers",
                    dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                    language: {
                        search: "_INPUT_",
                        searchPlaceholder: "Cari...",
                        paginate: {
                            Search: '<i class="icon-search"></i>',
                            first: "<i class='fas fa-angle-double-left'></i>",
                            previous: "<i class='fas fa-angle-left'></i>",
                            next: "<i class='fas fa-angle-right'></i>",
                            last: "<i class='fas fa-angle-double-right'></i>",
                        },
                    },
                    oLanguage: {
                        sSearch: "",
                    },
                    processing: true,
                    serverSide: true,
                    ajax: {
                        url: `${base_url}/unit-bisnis/dataTable`,
                        method: "POST",
                        data: function(d) {
                            return {
                                ...d,
                                ...filters,
                            };
                        },
                    },
                    columns: [{
                            name: "created_at",
                            data: "DT_RowIndex",
                        },
                        {
                            name: "nama",
                            data: "nama",
                            orderable: false,
                        },
                        {
                            name: "setting_no_surat",
                            data: "setting_no_surat",
                            orderable: false,
                        },
                        // {
                        //     name: "keterangan",
                        //     data: "keterangan",
                        //     orderable: false,
                        // },
                        {
                            name: "action",
                            data: "action",
                            orderable: false,
                        },

                    ],
                });

                let debounceTimer;

                function handleSearch(e) {
                    clearTimeout(debounceTimer);
                    debounceTimer = setTimeout(() => {
                        filters.keyword = e.target.value;
                        table.draw();
                    }, 500);
                }

                const formInputUnitBisnis = $("#formInputUnitBisnis");
                const submitBtn = formInputUnitBisnis.find("button[type=submit]");
                const modalInputUnitBisnis = $("#modalInputUnitBisnis");
                const cancelInput = () => {
                    formInputUnitBisnis.find("unit_bisnis_id").val("");
                    formInputUnitBisnis.find("input").val("");
                    formInputUnitBisnis.find("checkbox").prop('checked', false);
                    formInputUnitBisnis.find("textarea").val("");
                    formInputUnitBisnis.modal("hide");
                };

                formInputUnitBisnis.on("submit", async function(e) {
                    e.preventDefault();
                    const action = $(this).attr("action");
                    const formData = new FormData(this);
                    console.log(formData)
                    const result = await sendData(action, 'POST', formData);
                    if (result.status) {
                        submitBtn.attr('disabled', false).html('Simpan');
                        cancelInput();
                        table.draw();
                        Swal.fire(`Berhasil disimpan`, result.message, "success");
                        modalInputUnitBisnis.modal("hide");
                    } else {
                        submitBtn.attr('disabled', false).html('Simpan');
                        Swal.fire(`Gagal disimpan`, result.message, "error");
                    }
                });


                $(document).on("click", '.editInput', async function() {
                    const id = $(this).data("id");
                    const dataInput = $(this).data("input");
                    formInputUnitBisnis.find("#unit_bisnis_id").val(id);
                    formInputUnitBisnis.find("#nama").val(dataInput.nama);
                    formInputUnitBisnis.find("#keterangan").val(dataInput.keterangan);
                    formInputUnitBisnis.find('#kode_unit_surat').val(dataInput.kode_unit_surat);
                    formInputUnitBisnis.find('#setting_no_surat').val(dataInput.setting_no_surat);
                    formInputUnitBisnis.find('#start_no_urut').val(dataInput.start_no_urut);
                    formInputUnitBisnis.find('#isHolding').prop('checked', parseInt(dataInput.is_holding) == 1);
                    modalInputUnitBisnis.modal("show");
                }).on('click', '.deleteData', async function() {
                    const id = $(this).data("id");
                    const dataInput = $(this).data("input");
                    const nama = dataInput.nama;
                    const urlTarget = `${base_url}/unit-bisnis/${id}`
                    console.log(urlTarget);
                    await deleteDataTable(nama, urlTarget, table)
                });

                $(document).on('hide.bs.modal', '#modalInputUnitBisnis', function() {
                    cancelInput();
                });
            </script>
        @endpush
</x-app-layout>
