<x-app-layout>
    <x-slot name="header">
       Rekap Surat
    </x-slot>
    <x-slot name="headerRight">
        <a href="{{ route('rekap-surat.downloadRekapExcel') }}" class="btn btn-success" target="_blank" id="downloadExcel">
            <i class="fas fa-file-excel me-2"></i> Download Rekap
        </a>
    </x-slot>

    <div class="row">
        <div class="col-lg-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <div class="row align-items-center gap-0">
                        <div class="col-md-4">
                            <input type="text" class="form-control" placeholder="Cari surat"
                                oninput="handleSearch(event)" />
                        </div>
                        @php
                            $unit_bisnis = $unit_bisnis_ids = Auth::user()->user_unit_bisnis_data->pluck('nama', 'id')->toArray();
                        @endphp
                        <div class="col-md-3">
                            <select class="select2 form-select form-control" name="unit_bisnis_id" id="unit_bisnis_id"
                                data-placeholder="Pilih Unit Bisnis" data-allow-clear="true">
                                <option value="">Pilih Unit Bisnis</option>
                                @foreach ($unit_bisnis as $key => $value)
                                    <option value="{{ $key }}"> {{ $value }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <div id="reportrange" class="reportrange-picker d-flex align-items-center border p-3">
                                <span class="reportrange-picker-field"></span>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="d-flex gap-3">
                                <div role="button" title="Refresh" class="btn btn-outline-dark btnClear">
                                    <i class="fas fa-times"></i>
                                </div>
                                <div role="button" title="Refresh" class="btn btn-primary btnRefresh"
                                    data-bs-toggle="tooltip" data-bs-placement="top" title="Refresh"
                                    data-bs-trigger="hover">
                                    <i class="fas fa-refresh"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive mt-4">
                        <table class="table table-hover" id="example">
                            <thead>
                                <tr>
                                    <th style="width: 12px">No</th>
                                    <th>Tanggal</th>
                                    <th>No. Surat</th>
                                    <th>Perihal</th>
                                    <th>Tujuan</th>
                                    <th>
                                        Penandatangan
                                    </th>
                                    <th>Unit Bisnis</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>


    @include('libs.datatable')
    @push('scripts')
        <script>
             $(function() {
                var start = moment(),
                    end = moment();

                function updateDateRange(start, end) {
                    $("#reportrange span").html(start.format("MMM D, YY") + " - " + end.format("MMM D, YY"));

                    filters.start_date = start.format("YYYY-MM-DD");
                    filters.end_date = end.format("YYYY-MM-DD");
                    downloadExcel.attr('href', `${base_url}/rekap-surat/download-excel?${$.param(filters)}`);
                    table.draw();
                }

                $("#reportrange").daterangepicker({
                    startDate: start,
                    endDate: end,
                    ranges: {
                        'Today': [moment(), moment()],
                        'Yesterday': [moment().subtract(1, "days"), moment().subtract(1, "days")],
                        'Last 7 Days': [moment().subtract(6, "days"), moment()],
                        'Last 30 Days': [moment().subtract(29, "days"), moment()],
                        'This Month': [moment().startOf("month"), moment().endOf("month")],
                        'Last Month': [moment().subtract(1, "month").startOf("month"), moment().subtract(1, "month").endOf("month")]
                    },
                    locale: {
                        cancelLabel: 'Clear',
                        placeholder: 'Select a date range'
                    }
                }, updateDateRange).on('cancel.daterangepicker', function(ev, picker) {
                    $("#reportrange span").html("Pilih Tanggal");
                    filters.start_date = null;
                    filters.end_date = null;
                    downloadExcel.attr('href', `${base_url}/rekap-surat/download-excel?${$.param(filters)}`);
                    table.draw();
                });

                $("#reportrange span").html("Pilih Tanggal");
            });
            
            $.ajaxSetup({
                headers: {
                    "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr("content"),
                },
            });

            let downloadExcel = $("#downloadExcel");

            let filters = {};
            const table = $("#example").DataTable({
                lengthMenu: [
                    [10, 25, 50, 100, 500, -1],
                    [10, 25, 50, 100, 500, "All"],
                ],
                searching: false,
                responsive: false,
                lengthChange: true,
                autoWidth: false,
                order: [],
                pagingType: "full_numbers",
                dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                language: {
                    search: "_INPUT_",
                    searchPlaceholder: "Cari...",
                    paginate: {
                        Search: '<i class="icon-search"></i>',
                        first: "<i class='fas fa-angle-double-left'></i>",
                        previous: "<i class='fas fa-angle-left'></i>",
                        next: "<i class='fas fa-angle-right'></i>",
                        last: "<i class='fas fa-angle-double-right'></i>",
                    },
                },
                oLanguage: {
                    sSearch: "",
                },
                processing: true,
                serverSide: true,
                ajax: {
                    url: `${base_url}/rekap-surat/dataTable`,
                    method: "POST",
                    data: function(d) {
                        return {
                            ...d,
                            ...filters,
                        };
                    },
                },
                columns: [
                    {
                        name: "created_at",
                        data: "DT_RowIndex",
                    },
                    {
                        name: "date",
                        data: (row) => {
                            return moment(row.created_at).format("DD MMMM YYYY HH:mm");
                        },
                        orderable: false,
                    },
                    {
                        name: "no_surat",
                        data: (row) => {
                            return row?.no_surat ?? row?.parent_surat?.no_surat ?? '-';
                        },
                        orderable: false,
                    },
                    {
                        name: "perihal",
                        data: "perihal",
                        orderable: false,
                    },
                    {
                        name: "tujuan",
                        data: "tujuan",
                        orderable: false,
                    },
                    {
                        name: "signer",
                        data: "signer",
                        orderable: false,
                    },
                    {
                        name: "unit_bisnis.nama",
                        data: "unit_bisnis.nama",
                        orderable: false,
                    },
                    {
                        name: "action",
                        className: "position-sticky top-0 end-0 bg-white-smoke",
                        data: "action",
                        orderable: false,
                    },

                ],
            });

            $('#unit_bisnis_id').on('change', function() {
                filters.unit_bisnis_id = $(this).val();
                downloadExcel.attr('href', `${base_url}/rekap-surat/download-excel?${$.param(filters)}`);
                table.draw();
            });

            $('.btnClear').on('click', function() {
                $('#unit_bisnis_id').val(null).trigger('change');
                $('#reportrange span').html('Pilih Tanggal');
                filters = {};
                downloadExcel.attr('href', `${base_url}/rekap-surat/download-excel`);
                table.draw();
            });

            let debounceTimer;

            function handleSearch(e) {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    filters.keyword = e.target.value;
                    downloadExcel.attr('href', `${base_url}/rekap-surat/download-excel?${$.param(filters)}`);
                    table.draw();
                }, 500);
            }

            $(document).on('click', '.deleteData', async function() {
                const id = $(this).data("id");
                const dataInput = $(this).data("input");
                const perihal = dataInput.perihal;
                const urlTarget = `${base_url}/surat-keluar/${id}`
                await deleteDataTable(perihal, urlTarget, table)
            }).on('click', '.btnRefresh', function() {
                table.draw();
            })
        </script>
    @endpush
</x-app-layout>
