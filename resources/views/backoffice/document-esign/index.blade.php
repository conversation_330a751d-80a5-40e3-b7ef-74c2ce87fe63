<x-app-layout>
    <x-slot name="header">
        Dokumen Esign
    </x-slot>
    <x-slot name="headerRight">

    </x-slot>

    <div class="row">
        <div class="col-lg-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <div class="d-flex align-items-center flex-wrap">
                        <div class="d-flex align-items-center gap-2" style="width: 350px">
                            <div class="w-100">
                                <input type="text" class="form-control" placeholder="Cari surat keluar"
                                    oninput="handleSearch(event)" style="width: 100%" />
                            </div>
                            <div role="button" title="Refresh" class="btn btn-primary btnRefresh"
                                data-bs-toggle="tooltip" data-bs-placement="top" title="Refresh"
                                data-bs-trigger="hover">
                                <i class="fas fa-refresh"></i>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive mt-4">
                        <table class="table table-hover" id="example">
                            <thead>
                                <tr>
                                    <th style="width: 12px">No</th>
                                    <th>No Surat</th>
                                    <th>Dokumen</th>
                                    <th>Status</th>
                                    <th>Dibuat Pada</th>
                                    <th>Terakhir Diubah</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>


    @include('libs.datatable')
    @push('scripts')
        <script>
            $.ajaxSetup({
                headers: {
                    "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr("content"),
                },
            });

            let filters = {};
            const table = $("#example").DataTable({
                lengthMenu: [
                    [10, 25, 50, 100, 500],
                    [10, 25, 50, 100, 500],
                ],
                searching: false,
                responsive: false,
                lengthChange: true,
                autoWidth: false,
                order: [],
                pagingType: "full_numbers",
                dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                language: {
                    search: "_INPUT_",
                    searchPlaceholder: "Cari...",
                    paginate: {
                        Search: '<i class="icon-search"></i>',
                        first: "<i class='fas fa-angle-double-left'></i>",
                        previous: "<i class='fas fa-angle-left'></i>",
                        next: "<i class='fas fa-angle-right'></i>",
                        last: "<i class='fas fa-angle-double-right'></i>",
                    },
                },
                oLanguage: {
                    sSearch: "",
                },
                processing: true,
                serverSide: true,
                ajax: {
                    url: `${base_url}/document-esign/list`,
                    method: "POST",
                    data: function(d) {
                        return {
                            ...d,
                            ...filters,
                        };
                    },
                },
                columns: [{
                        name: "created_at",
                        data: "DT_RowIndex",
                    },
                    {
                        name: "no_surat",
                        data: "no_surat",
                        orderable: false,
                    },
                    {
                        name: "document",
                        data: 'document',
                        orderable: false,
                    },
                    {
                        name: "status",
                        data: "status",
                        orderable: false,
                    },
                    {
                        name: "date",
                        data: (row) => {
                            return moment(row?.attributes?.created_at).format("DD MMMM YYYY HH:mm");
                        },
                        orderable: false,
                    },
                    {
                        name: "date",
                        data: (row) => {
                            return moment(row?.attributes?.updated_at).format("DD MMMM YYYY HH:mm");
                        },
                        orderable: false,
                    },
                    {
                        name: "action",
                        className: "position-sticky top-0 end-0 bg-white-smoke",
                        data: "action",
                        orderable: false,
                    },

                ],
            });

            let debounceTimer;

            function handleSearch(e) {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    filters.subject = e.target.value;
                    table.draw();
                }, 500);
            }

            $(document).on('click', '.deleteData', async function() {
                const id = $(this).data("id");
                const dataInput = $(this).data("input");
                const perihal = dataInput.perihal;
                const urlTarget = `${base_url}/document-esign/${id}`
                await deleteDataTable(perihal, urlTarget, table)
            }).on('click', '.btnRefresh', function() {
                table.draw();
            }).on('click', '.resendDocument', function() {
                const id = $(this).data("id");
                const dataInput = $(this).data("input");
                const perihal = dataInput.perihal;
                const urlTarget = `${base_url}/document-esign/${id}/resend`

                Swal.fire({
                    title: 'Kirim Ulang Dokumen',
                    text: `Apakah anda yakin ingin mengirim ulang dokumen ${perihal}?`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Ya, Kirim Ulang',
                    cancelButtonText: 'Batal',
                }).then(async (result) => {
                    console.log(result)
                    if (result.value) {
                        const res = await axios.post(urlTarget)
                        if (res?.data?.status) {
                            Swal.fire(
                                'Berhasil!',
                                'Dokumen berhasil dikirim ulang',
                                'success'
                            )
                        } else {
                            Swal.fire(
                                'Gagal!',
                                'Dokumen gagal dikirim ulang',
                                'error'
                            )
                        }
                    }
                })
            })
        </script>
    @endpush
</x-app-layout>
