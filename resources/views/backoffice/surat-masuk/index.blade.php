<x-app-layout>
    <x-slot name="header">
        Surat Masuk
    </x-slot>
    <x-slot name="headerRight">

    </x-slot>

    <div class="row">
        <div class="col-lg-12">
            <div class="card stretch stretch-full p-0">
                <div class=" d-md-flex align-items-center justify-content-between gap-4 p-4">
                    <ul class="nav nav-pills border-0 bg-body rounded" id="filterTab" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link fs-15 fw-bold active" data-filter="not_process" href="#">
                                Belum Diproses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link fs-15 fw-bold" data-filter="done_process" href="#">
                                Sedang Diproses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link fs-15 fw-bold" data-filter="done" href="#">
                                <PERSON><PERSON><PERSON>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link fs-15 fw-bold" data-filter="all" href="#">
                                Semua
                            </a>
                        </li>
                    </ul>
                    <div class="d-sm-flex align-items-center gap-3">
                        <div class="">
                            <input type="text" class="form-control" placeholder="Cari surat masuk"
                                oninput="handleSearch(event)" style="width: 250px" />
                        </div>
                        <button class="btn btn-primary btn-md" data-bs-toggle="modal"
                            data-bs-target="#modalFilterSuratMasuk">
                            <i class="fas fa-filter me-2"></i>
                            Filter
                        </button>
                        <div role="button" title="Refresh" class="btn btn-primary btnRefresh btn-md" data-bs-toggle="tooltip"
                            data-bs-placement="top" title="Refresh" data-bs-trigger="hover">
                            <i class="fas fa-refresh"></i>
                        </div>
                    </div>
                </div>
                <div class="card-body pt-0">
                    <div class="table-responsive">
                        <table class="table table-hover" id="example">
                            <thead>
                                <tr>
                                    <th style="width: 12px">No</th>
                                    <th>Pengirim</th>
                                    <th>Perihal</th>
                                    <th>Tanggal</th>
                                    <th>No. Surat</th>
                                    <th>Status</th>
                                    <th>Perubahan Terbaru</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('modals')
        <div class="modal fade-scale" id="modalFilterSuratMasuk" tabindex="-1" data-bs-backdrop="static"
            data-bs-keyboard="false" aria-hidden="true" data-bs-dismiss="ou">
            <div class="modal-dialog modal-dialog-centered modal-md" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="d-flex flex-column mb-0">
                            <span class="fs-18 fw-bold mb-1">Filter Surat Masuk</span>
                        </h2>
                        <a href="javascript:void(0)" class="avatar-text avatar-md bg-soft-danger close-icon"
                            data-bs-dismiss="modal">
                            <i class="feather-x text-danger"></i>
                        </a>
                    </div>
                    <form id="formFilterSuratMasuk">
                        <div class="modal-body" style="min-height: 300px">
                            <div class="">
                                @php
                                    $unit_bisnis = App\Models\UnitBisnis::pluck('nama', 'id');
                                @endphp
                                <div class="form-group mb-3">
                                    <label class="form-label">Unit Bisnis</label>
                                    <select class="select2 form-select form-control" name="unit_bisnis_id"
                                        id="unit_bisnis_id" data-placeholder="Pilih Unit Bisnis" data-allow-clear="true">
                                        <option value="">Pilih Unit Bisnis</option>
                                        @foreach ($unit_bisnis as $key => $value)
                                            <option value="{{ $key }}"> {{ $value }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-group mb-3">
                                    <label class="form-label">Tanggal</label>
                                    <div id="reportrange" class="reportrange-picker d-flex align-items-center border p-3">
                                        <span class="reportrange-picker-field"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer d-flex align-items-center justify-content-between gap-3 w-100">
                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Batal</button>
                            <div class="d-flex align-items-center gap-3">
                                <button class="btn btn-outline-danger" id="resetFilter">Reset</button>
                                <button class="btn btn-primary">Simpan</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endpush

    @include('libs.datatable')
    @push('scripts')
        <script>
            $(function() {
                var start = moment(),
                    end = moment();

                function updateDateRange(start, end) {
                    $("#reportrange span").html(start.format("MMM D, YY") + " - " + end.format("MMM D, YY"));

                    filters.start_date = start.format("YYYY-MM-DD");
                    filters.end_date = end.format("YYYY-MM-DD");
                    // table.draw();
                }

                $("#reportrange").daterangepicker({
                    startDate: start,
                    endDate: end,
                    ranges: {
                        'Today': [moment(), moment()],
                        'Yesterday': [moment().subtract(1, "days"), moment().subtract(1, "days")],
                        'Last 7 Days': [moment().subtract(6, "days"), moment()],
                        'Last 30 Days': [moment().subtract(29, "days"), moment()],
                        'This Month': [moment().startOf("month"), moment().endOf("month")],
                        'Last Month': [moment().subtract(1, "month").startOf("month"), moment().subtract(1,
                            "month").endOf("month")]
                    },
                    locale: {
                        cancelLabel: 'Clear',
                        placeholder: 'Select a date range'
                    }
                }, updateDateRange).on('cancel.daterangepicker', function(ev, picker) {
                    // Handle clear action
                    $("#reportrange span").html("Pilih Tanggal");
                    filters.start_date = null;
                    filters.end_date = null;
                    // table.draw();
                });

                $("#reportrange span").html("Pilih Tanggal");
            });


            $.ajaxSetup({
                headers: {
                    "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr("content"),
                },
            });

            let filters = {};
            const table = $("#example").DataTable({
                lengthMenu: [
                    [10, 25, 50, 100, 500, -1],
                    [10, 25, 50, 100, 500, "All"],
                ],
                searching: false,
                responsive: false,
                lengthChange: true,
                autoWidth: false,
                order: [],
                pagingType: "full_numbers",
                dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                language: {
                    search: "_INPUT_",
                    searchPlaceholder: "Cari...",
                    paginate: {
                        Search: '<i class="icon-search"></i>',
                        first: "<i class='fas fa-angle-double-left'></i>",
                        previous: "<i class='fas fa-angle-left'></i>",
                        next: "<i class='fas fa-angle-right'></i>",
                        last: "<i class='fas fa-angle-double-right'></i>",
                    },
                },
                oLanguage: {
                    sSearch: "",
                },
                processing: true,
                serverSide: true,
                ajax: {
                    url: `${base_url}/surat-masuk/dataTable`,
                    method: "POST",
                    data: function(d) {
                        return {
                            ...d,
                            ...filters,
                        };
                    },
                },
                columns: [{
                        name: "created_at",
                        data: "DT_RowIndex",
                        createdCell: function(td, cellData, rowData) {
                            if (!rowData.read_surat) {
                                $(td).css('background-color', '#eaebef');
                            }
                        },
                    },
                    {
                        name: "nama",
                        data: (row) => {
                            return `
                                    <a href="/surat-masuk/${row?.parent_surat_id ? row?.parent_surat_id : row?.id}/detail" class="d-flex align-items-center">
                                        ${
                                            row?.pengirim?.avatar ? `<div class="avatar avatar-sm me-3">
                                                                                                                                            <img src="${row.avatar}" alt="Avatar" class="avatar-img rounded-circle">
                                                                                                                                        </div>` : ''
                                        }
                                        <div class="d-block">
                                            <span class="fw-bold">${row?.pengirim?.nama}</span>
                                            <div class="text-muted fw-normal">
                                            ${row?.unit_bisnis?.nama}
                                            </div>
                                        </div>
                                    </a>
                                `
                        },
                        orderable: false,
                        createdCell: function(td, cellData, rowData) {
                            if (!rowData.read_surat) {
                                $(td).css('background-color', '#eaebef');
                            }
                        },
                    },
                    {
                        name: "perihal",
                        data: "perihal",
                        orderable: false,
                        createdCell: function(td, cellData, rowData) {
                            if (!rowData.read_surat) {
                                $(td).css('background-color', '#eaebef');
                            }
                        },
                    },
                    {
                        name: "date",
                        data: "date",
                        orderable: false,
                        createdCell: function(td, cellData, rowData) {
                            if (!rowData.read_surat) {
                                $(td).css('background-color', '#eaebef');
                            }
                        },
                    },
                    {
                        name: "no_surat",
                        data: (row) => {
                            return row?.no_surat ?? row?.parent_surat?.no_surat ?? '-';
                        },
                        orderable: false,
                        createdCell: function(td, cellData, rowData) {
                            if (!rowData.read_surat) {
                                $(td).css('background-color', '#eaebef');
                            }
                        },
                    },
                    {
                        name: "status_surat",
                        data: "status_surat",
                        orderable: false,
                        createdCell: function(td, cellData, rowData) {
                            if (!rowData.read_surat) {
                                $(td).css('background-color', '#eaebef');
                            }
                        },
                    },
                    {
                        name: "last_updated",
                        data: "last_updated",
                        orderable: false,
                        createdCell: function(td, cellData, rowData) {
                            if (!rowData.read_surat) {
                                $(td).css('background-color', '#eaebef');
                            }
                        },
                    },
                    {
                        name: "action",
                        className: "position-sticky top-0 end-0 bg-white-smoke",
                        data: "action",
                        orderable: false,
                        createdCell: function(td, cellData, rowData) {
                            if (!rowData.read_surat) {
                                $(td).css('background-color', '#eaebef');
                            }
                        },
                    },

                ],
            });

            // $('#unit_bisnis_id').on('change', function() {
            //     filters.unit_bisnis_id = $(this).val();
            //     table.draw();
            // });

            $('.btnClear').on('click', function() {
                $('#unit_bisnis_id').val(null).trigger('change');
                $('#reportrange span').html('Pilih Tanggal');
                filters = {};
                table.draw();
            });

            $('#filterTab').on('click', '.nav-link', function(e) {
                e.preventDefault();
                const filter = $(this).data('filter');
                setActiveFilterStatus(filter);
            });

            setActiveFilterStatus('all');

            function setActiveFilterStatus(filter) {
                filters.status = filter;
                $('#filterTab .nav-link').removeClass('active');
                $(`[data-filter="${filter}"]`).addClass('active');
                table.draw();
            }

            $('#formFilterSuratMasuk').on('submit', function(e) {
                e.preventDefault();
                const unit_bisnis_id = $('#unit_bisnis_id').val();
                filters.unit_bisnis_id = unit_bisnis_id;
                table.draw();
                $('#modalFilterSuratMasuk').modal('hide');
            });

            $('#resetFilter').on('click', function(e) {
                e.preventDefault();
                $('#unit_bisnis_id').val(null).trigger('change');
                $('#reportrange span').html('Pilih Tanggal');
                filters.unit_bisnis_id = null;
                filters.start_date = null;
                filters.end_date = null;
                table.draw();
            });

            let debounceTimer;

            function handleSearch(e) {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    filters.keyword = e.target.value;
                    table.draw();
                }, 500);
            }

            $(document).on('click', '.deleteData', async function() {
                const id = $(this).data("id");
                const dataInput = $(this).data("input");
                const perihal = dataInput.perihal;
                const urlTarget = `${base_url}/surat-masuk/${id}`
                await deleteDataTable(perihal, urlTarget, table)
            }).on('click', '.btnRefresh', function() {
                table.draw();
            })
        </script>
    @endpush
</x-app-layout>
