<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preview Surat</title>
    <style>
        @php
            $setting = getSettingGroup('default');
            $paperSize = $data['ukuran_kertas'] ?? 'A4';
            $paperWidth = $paperSize === 'F4' ? '216mm' : '210mm';
            $paperHeight = $paperSize === 'F4' ? '330mm' : '297mm';
        @endphp

        @page {
            size: {{ $paperSize }};
            margin: {{ $setting['surat_margin_top'] ?? 25 }}mm {{ $setting['surat_margin_right'] ?? 25 }}mm {{ $setting['surat_margin_bottom'] ?? 25 }}mm {{ $setting['surat_margin_left'] ?? 25 }}mm;
        }

        body {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 12pt;
            width: {{ $paperWidth }};
            max-width: {{ $paperWidth }};
            margin: 0 auto;
            padding: 20px;
            box-sizing: border-box;
            background: white;
            min-height: {{ $paperHeight }};
            line-height: 1.5;
        }

        p {
            margin: 0 0 12pt 0;
            font-size: 12pt;
            line-height: 1.5;
            font-family: Arial, Helvetica, sans-serif;
        }

        table {
            border-collapse: collapse;
            width: 100%;
            font-size: 12pt;
            margin: 12pt 0;
            font-family: Arial, Helvetica, sans-serif;
        }

        table td, table th {
            border: 1px solid #000;
            padding: 8pt;
            font-size: 12pt;
            vertical-align: top;
            font-family: Arial, Helvetica, sans-serif;
            line-height: 1.5;
        }

        table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        table.no-border, table.no-border td, table.no-border th {
            border: none;
        }

        h1, h2, h3, h4, h5, h6 {
            font-size: 12pt;
            font-weight: bold;
            margin: 12pt 0 6pt 0;
            font-family: Arial, Helvetica, sans-serif;
            line-height: 1.5;
        }

        ul, ol {
            margin: 12pt 0;
            padding-left: 24pt;
            font-size: 12pt;
            font-family: Arial, Helvetica, sans-serif;
        }

        li {
            font-size: 12pt;
            margin: 6pt 0;
            font-family: Arial, Helvetica, sans-serif;
            line-height: 1.5;
        }

        div, span {
            font-size: 12pt;
            font-family: Arial, Helvetica, sans-serif;
        }

        strong, b {
            font-size: 12pt;
            font-family: Arial, Helvetica, sans-serif;
        }

        em, i {
            font-size: 12pt;
            font-family: Arial, Helvetica, sans-serif;
        }
    </style>
</head>
<body>
    @php
        $setting = getSettingGroup('default');
    @endphp

    {{--
    @if ($kop_surat ?? false)
        <div style="height: {{ $setting['height_kop_surat'] ?? '150' }}px">
            {!! $kop_surat->kop_surat_replaced ?? 'Kop surat tidak tersedia' !!}
        </div>
    @endif

    @if (!$not_show_tanggal_surat)
        <div style="padding: 5px 2px">
            {{ date('d F Y', strtotime($data->created_at ?? now())) }}
        </div>
    @endif --}}

    {{-- <table>
        <tr>
            <td style="width: 80px">Perihal</td>
            <td style="width: 6px">:</td>
            <td>{{ $data->perihal ?? 'Tidak ada perihal' }}</td>
        </tr>
        <tr>
            <td style="width: 80px">No Surat</td>
            <td style="width: 6px">:</td>
            <td>{{ $data->no_surat ?? 'Tidak ada nomor surat' }}</td>
        </tr>
    </table> --}}

    <div class="content-wrapper">
        {!! $data['content_replace_pdf'] ?? $data['content_surat'] ?? '<p>Konten surat tidak tersedia.</p>' !!}
    </div>
</body>
</html>
