<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="x-ua-compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="description" content="Halaman Preview Keterangan Surat yang Ditandatangani" />
    <meta name="keyword" content="Preview, Keterangan, Tanda Tangan, Surat Masuk" />
    <meta name="author" content="Anna Holding" />
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Preview Keterangan Surat Ditandatangani | Anna Holding</title>
    <link rel="shortcut icon" type="image/x-icon" href="/assets/images/favicon.ico" />
    <link rel="stylesheet" type="text/css" href="/assets/css/bootstrap.min.css" />
    <link rel="stylesheet" type="text/css" href="/assets/vendors/css/vendors.min.css" />
    <link rel="stylesheet" type="text/css" href="/assets/css/theme.min.css" />
    <link rel="stylesheet" type="text/css" href="/assets/css/custom.css" />
</head>
<style>
.label_detail_signature {
    width: 230px;
}
@media (max-width: 768px) {
    .label_detail_signature {
       width: 120px;
       font-size: 12px !important;
    }
    .value_detail_signature {
       font-size: 12px !important;
    }
}
</style>

<body>
    <main style="min-height: 100vh; height: 100%">
        <div class="container" style="min-height: 100vh; display: flex; justify-content: center; margin-top: 70px">
            <div class="col-xl-6 mx-auto">
                <div class="fs-24 fw-bold text-dark mb-3 text-center">
                    Tanda Tangan Terverifikasi
                </div>
                <div class="card overflow-hidden">
                    <div class="d-flex align-items-start gap-4 bg-dark py-3 px-4">
                        <div>
                            <i class="feather-check-circle text-success fw-bold" style="font-size: 40px"></i>
                        </div>
                        <div>
                            <div class="text-white fs-13 fw-medium">
                                Tanda Tangan ini tercatat dalam sistem kami
                            </div>
                            <div class="text-white fw-bold fs-18">
                                No Surat: {{ $data->surat->no_surat ?? '' }}
                            </div>
                            @if($data->document_type === "lampiran")
                            <div class="text-white fs-14">
                                Dokumen : {{ $data->referable->nama_original ?? '' }}
                            </div>
                            @endif
                        </div>
                        <div class="ms-auto">
                            <div class="bg-white p-1 rounded">
                                <img src="{{ $data->barcode }}" alt="signature" class="img-fluid" style="max-width: 38px">
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-start mb-2">
                            <div class="fs-15 fw-medium text-dark label_detail_signature">
                                Perihal Surat
                            </div>
                            <div class="fs-15 fw-medium text-dark fw-bold value_detail_signature">
                                {{ $data->surat->perihal ?? '' }}
                            </div>
                        </div>
                        <div class="d-flex align-items-start mb-2">
                            <div class="fs-15 fw-medium text-dark label_detail_signature">
                                Jenis Surat
                            </div>
                            <div class="fs-15 fw-medium text-dark fw-bold value_detail_signature">
                                {{ $data->surat->jenisSurat->jenis_surat ?? '' }}
                            </div>
                        </div>
                        <div class="d-flex align-items-start mb-2">
                            <div class="fs-15 fw-medium text-dark label_detail_signature"">
                                Tanggal Surat
                            </div>
                            <div class="fs-15 fw-medium text-dark fw-bold value_detail_signature">
                                {{$Carbon::parse($data->surat->created_at ?? '')->translatedFormat('d F Y')}}
                            </div>
                        </div>
                        <div class="d-flex align-items-start mb-2">
                            <div class="fs-15 fw-medium text-dark label_detail_signature">
                                Unit Bisnis
                            </div>
                            <div class="fs-15 fw-medium text-dark fw-bold value_detail_signature">
                                {{ $data->surat->unitBisnis->nama ?? '' }}
                            </div>
                        </div>
                        @php
                            $verifications = $data->surat->history->where('type', 3);
                            // dd($verifications);
                        @endphp
                        <div class="d-flex align-items-start mb-2">
                            <div class="fs-15 fw-medium text-dark label_detail_signature">
                               Diverifikasi Oleh
                            </div>
                            @if($verifications->isEmpty())
                                <div class="fs-15 fw-medium text-danger fw-bold value_detail_signature">
                                    Belum ada verifikasi
                                </div>
                            @endif
                            <ol class="ps-3 value_detail_signature fs-15 fw-semibold text-dark">
                                @foreach ($verifications as $item)
                                    <li>
                                        {{$item->user->nama ?? ""}},  {{$Carbon::parse($item->created_at ?? '')->translatedFormat('d F Y H:i:s')}} 
                                        @php
                                            $badge = '';
                                            $icon = '';
                                            switch (intval($item->approval_status)) {
                                                case 1:
                                                    $badge = 'success';
                                                    $icon = 'feather-check-circle';
                                                    break;
                                                case 2:
                                                    $badge = 'warning';
                                                    $icon = 'fas fa-info-circle';
                                                    break;
                                                case 3:
                                                    $badge = 'danger';
                                                    $icon = 'fas fa-times-circle';
                                                    break;
                                            }
                                            $approve_status_name = $item->approve_status_name;
                                        @endphp
                                        <span class='d-flex align-items-center gap-2 text-{{$badge}}'>
                                            ({{str_replace('_', ' ', ucwords($approve_status_name, '_'))}})
                                            <i class='{{$icon}} text-{{$badge}} fs-16'></i>
                                        </span>
                                    </li>
                                @endforeach
                            </ol>
                        </div>
                        <div class="fs-15 fw-medium text-dark">
                            Detail Penandatangan
                        </div>
                        <div class="bg-body p-3 rounded-2 mt-2">
                            <div class="d-flex flex-wrap align-items-start justify-content-between gap-2">
                                <div>
                                    <div class="fs-14 text-dark mb-2">
                                        Penandatangan
                                    </div>
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="">
                                            <i class="feather-user fs-20" style="font-size: 20px"></i>
                                        </div>
                                        <div>
                                            <div class="text-dark fw-bold fs-14">
                                                {{ $data->user->nama ?? '' }} ({{ $data->user->email ?? '' }})
                                            </div>
                                            <div class="text-dark fs-13">
                                                Jabatan : {{ $data->user->role->name ?? '' }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="fs-14 text-dark mb-2">
                                        Waktu
                                    </div>
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="">
                                            <i class="feather-clock fs-20" style="font-size: 20px"></i>
                                        </div>
                                        <div>
                                            <div class="text-dark fw-bold fs-14">
                                                {{$Carbon::parse($data->created_at ?? '')->translatedFormat('d F Y H:i:s')}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            Copyright &copy;{{ date('Y') }} Anna Holding
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="/assets/vendors/js/vendors.min.js"></script>
    <script src="/assets/js/common-init.min.js"></script>
    <script src="/assets/vendors/js/sweetalert2.min.js"></script>
    <script src="/assets/js/helper.js"></script>
    <script src="/assets/js/signature-preview.js"></script>
    <script>
        const base_url = '{{ url('/') }}';
    </script>
    @stack('scripts')
</body>

</html>
