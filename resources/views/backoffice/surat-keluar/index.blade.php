<x-app-layout>
    <x-slot name="header">
        Surat Keluar
    </x-slot>
    <x-slot name="headerRight">

    </x-slot>

    <div class="row">
        <div class="col-lg-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <div class="d-flex align-items-center flex-wrap">
                        <div class="d-flex align-items-center gap-2" style="width: 350px">
                            <div class="w-100">
                                <input type="text" class="form-control" placeholder="Cari surat keluar"
                                    oninput="handleSearch(event)" style="width: 100%" />
                            </div>
                            <div role="button" title="Refresh" class="btn btn-primary btnRefresh"
                                data-bs-toggle="tooltip" data-bs-placement="top" title="Refresh"
                                data-bs-trigger="hover">
                                <i class="fas fa-refresh"></i>
                            </div>
                        </div>
                        <div class="ms-md-auto mt-md-0 mt-3">
                            @if (canPermission('Surat Keluar.Create'))
                                <div>
                                    <a href="{{ route('surat-keluar.create') }}" class="btn btn-primary">
                                        <i class="feather-plus me-2"></i>
                                        <span>Tambah Surat Keluar</span>
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                    <div class="table-responsive mt-4">
                        <table class="table table-hover" id="example">
                            <thead>
                                <tr>
                                    <th style="width: 12px">No</th>
                                    <th>Pengirim</th>
                                    <th>Perihal</th>
                                    <th>Tanggal</th>
                                    <th>No. Surat</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>


    @include('libs.datatable')
    @push('scripts')
        <script>
            $.ajaxSetup({
                headers: {
                    "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr("content"),
                },
            });

            let filters = {};
            const table = $("#example").DataTable({
                lengthMenu: [
                    [10, 25, 50, 100, 500, -1],
                    [10, 25, 50, 100, 500, "All"],
                ],
                searching: false,
                responsive: false,
                lengthChange: true,
                autoWidth: false,
                order: [],
                pagingType: "full_numbers",
                dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                language: {
                    search: "_INPUT_",
                    searchPlaceholder: "Cari...",
                    paginate: {
                        Search: '<i class="icon-search"></i>',
                        first: "<i class='fas fa-angle-double-left'></i>",
                        previous: "<i class='fas fa-angle-left'></i>",
                        next: "<i class='fas fa-angle-right'></i>",
                        last: "<i class='fas fa-angle-double-right'></i>",
                    },
                },
                oLanguage: {
                    sSearch: "",
                },
                processing: true,
                serverSide: true,
                ajax: {
                    url: `${base_url}/surat-keluar/dataTable`,
                    method: "POST",
                    data: function(d) {
                        return {
                            ...d,
                            ...filters,
                        };
                    },
                },
                columns: [{
                        name: "created_at",
                        data: "DT_RowIndex",
                    },
                    {
                        name: "nama",
                        data: (row) => {
                            const user_unit_bisnis = row?.pengirim?.user_unit_bisnis
                            return `
                                    <a href="/surat-keluar/${row?.parent_surat_id ? row?.parent_surat_id : row?.id}/detail" class="d-flex align-items-center">
                                        ${
                                            row?.pengirim?.avatar ? `<div class="avatar avatar-sm me-3">
                                                                                    <img src="${row.avatar}" alt="Avatar" class="avatar-img rounded-circle">
                                                                                </div>` : ''
                                        }
                                        <div class="d-block">
                                            <span class="fw-bold">${row?.pengirim?.nama}</span>
                                            <div class="text-muted fw-normal">
                                                ${row?.unit_bisnis?.nama}
                                            </div>
                                        </div>
                                    </a>
                                `
                        },
                        orderable: false,
                    },
                    {
                        name: "perihal",
                        data: "perihal",
                        orderable: false,
                    },
                    {
                        name: "date",
                        data: "date",
                        orderable: false,
                    },
                    {
                        name: "no_surat",
                        data: (row) => {
                            return row?.no_surat ?? row?.parent_surat?.no_surat ?? '-';
                        },
                        orderable: false,
                    },
                    {
                        name: "status_surat",
                        data: "status_surat",
                        orderable: false,
                    },
                    {
                        name: "action",
                        className: "position-sticky top-0 end-0 bg-white-smoke",
                        data: "action",
                        orderable: false,
                    },

                ],
            });

            let debounceTimer;

            function handleSearch(e) {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    filters.keyword = e.target.value;
                    table.draw();
                }, 500);
            }

            $(document).on('click', '.deleteData', async function() {
                const id = $(this).data("id");
                const dataInput = $(this).data("input");
                const perihal = dataInput.perihal;
                const urlTarget = `${base_url}/surat-keluar/${id}`
                await deleteDataTable(perihal, urlTarget, table)
            }).on('click', '.btnRefresh', function() {
                table.draw();
            })
        </script>
    @endpush
</x-app-layout>
