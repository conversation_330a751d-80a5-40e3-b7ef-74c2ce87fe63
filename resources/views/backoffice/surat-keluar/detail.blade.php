<x-app-layout class_container="apps-container apps-email" class_content="without-header nxl-full-content"
    :show_footer="false">

    @push('push_data_script')
        <script>
            let data_detail = @json($data);
            const app_url = '{{ url('/') }}';
            const auth_user = @json(auth()->user());
            const permissions = @json(auth()->user()->getAllPermissions());
            const api_url_mekari = '{{ getSettingValue('api_url_mekari') }}';

            const filterReplyForward = data_detail.child_surat.filter((item) => item.created_by_id !== auth_user?.id)
            const lastReplyForward = filterReplyForward[filterReplyForward.length - 1] ?? data_detail
        </script>
    @endpush
    @push('styles')
    <style>
        @media (min-width: 768px) {
           .boxEsign--stepThree {
               width: 860px !important;
           }
       }

       @media (min-width: 0px) {
           .boxEsign--stepThree {
               width: 100%;
           }
       }
   </style>
    @endpush
    @push('scripts')
        <script></script>
    @endpush
</x-app-layout>
