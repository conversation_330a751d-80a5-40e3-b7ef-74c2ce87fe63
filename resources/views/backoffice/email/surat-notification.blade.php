<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #f4f4f4;
      font-family: Arial, sans-serif;
    }

    table {
      border-spacing: 0;
      width: 100%;
      border: none
    }

    td {
      padding: 8px;
      /* border: 1px solid #ddd; */
    }

    .email-container {
      width: 100%;
      max-width: 600px;
      margin: 0 auto;
      background-color: #ffffff;
      border-radius: 10px;
      box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .email-header {
      background-color: #14848F;
      padding: 20px;
      text-align: center;
      color: white;
      font-size: 24px;
      font-weight: bold;
    }

    .email-body {
      padding: 20px;
      color: #333333;
      line-height: 1.6;
    }

    .email-body h2 {
      color: #14848F;
      font-size: 20px;
      margin-bottom: 20px;
    }

    .email-body p {
      margin: 10px 0;
    }

    .email-footer {
      background-color: #f4f4f4;
      padding: 20px;
      text-align: center;
      font-size: 12px;
      color: #888888;
    }

    .button {
      display: inline-block;
      background-color: #14848F;
      color: white;
      padding: 10px 20px;
      border-radius: 5px;
      text-decoration: none;
      margin-top: 20px;
    }

    .button:hover {
      background-color: #357ABD;
    }

    .email-footer a {
      color: #14848F;
      text-decoration: none;
    }

    @media only screen and (max-width: 600px) {
      .email-body h2 {
        font-size: 18px;
      }
    }
  </style>
</head>

<body>
  <table role="presentation" width="100%" border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td align="center" bgcolor="#f4f4f4">
        <table role="presentation" class="email-container" cellpadding="0" cellspacing="0" border="0">
          <!-- Header -->
          <tr>
            <td class="email-header">
              {{ env('MAIL_FROM_NAME') }}
            </td>
          </tr>

          <!-- Body -->
          <tr>
            <td class="email-body">
              <h2>
                {{ $subject }}
              </h2>
              <p>Halo, {{ $user->nama }}</p>
              <p>Surat dengan nomor referensi <strong>{{ $data->no_surat }}</strong> sedang dalam {!! status_surat($data->status) !!}. Berikut adalah detailnya:</p>

              <table>
                <tr>
                  <td style="width: 100px"><strong>No Surat</strong></td>
                  <td style="width: 1px">:</td>
                  <td>
                    {{ $data->no_surat }}
                  </td>
                </tr>
                <tr>
                    <td style="width: 100px"><strong>Perihal</strong></td>
                    <td style="width: 1px">:</td>
                    <td>
                      {{ $data->perihal }}
                    </td>
                  </tr>
                <tr>
                  <td style="width: 100px"><strong>Status</strong></td>
                  <td style="width: 1px">:</td>
                  <td>
                    {!! status_surat($data->status) !!}
                  </td>
                </tr>
                <tr>
                  <td style="width: 100px"><strong>Tanggal</strong></td>
                  <td style="width: 1px">:</td>
                  <td>
                    {{$Carbon::parse($data->created_at)->translatedFormat('d F Y')}}
                  </td>
                </tr>
              </table>

              <p>Untuk informasi lebih lanjut mengenai status surat ini, Anda dapat memeriksanya melalui sistem.</p>
{{--
              <a href="https://example.com" class="button">Cek Status</a> --}}

              <p>Terima kasih</p>
            </td>
          </tr>

          <!-- Footer -->
          <tr>
            <td class="email-footer">
              <p>&copy; {{ date('Y') }} Anna Holding System</p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>

</html>
