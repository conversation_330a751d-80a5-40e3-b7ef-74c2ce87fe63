<x-app-layout class_container="apps-container apps-email" class_content="without-header nxl-full-content"
    :show_footer="false" :hide_header="true" :hide_sidebar="true">

    @push('styles')
    <style>
        .sign-tabs li::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #f1f1f1;
            transition: all 0.3s;
        }

        @media (min-width: 768px) {
            .boxEsign {
                width: 768px !important;
            }
        }

        @media (min-width: 0px) {
            .boxEsign {
                width: 100%;
            }
        }

    </style>
    @endpush
    @push('push_data_script')
        <script>
            
        </script>
    @endpush
    @push('scripts')
        <script>
            const dataDocument = @json($documentEsign);
            const dataAnnotations = @json($annotations);
            const dataSigner = @json($signer);
        </script>
    @endpush
</x-app-layout>
