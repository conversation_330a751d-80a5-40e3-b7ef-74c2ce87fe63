<x-app-layout>
    <x-slot name="header">
        {{ $data ?? null ? 'Edit' : 'Tambah' }} Pengguna
    </x-slot>
    <x-slot name="headerRight">
        <div class="page-header-right-items">
            <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                <a href="{{ route('pengguna.index') }}" class="btn btn-light-brand">
                    <i class="feather-x me-2"></i>
                    <span>
                        Batal
                    </span>
                </a>
                <button class="btn btn-primary btnSubmit" style="width: 150px">
                    <i class="feather-save me-2"></i>
                    <span>Simpan</span>
                </button>
            </div>
        </div>
        <div class="d-md-none d-flex align-items-center">
            <a href="javascript:void(0)" class="page-header-right-open-toggle">
                <i class="feather-align-right fs-20"></i>
            </a>
        </div>
    </x-slot>
    <div class="row">
        <div class="col-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <x-validation-errors />
                    @if (session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif
                    <form
                        class="needs-validation"
                        novalidate
                        action="{{ route('pengguna.store') }}"
                        id="formSubmit" method="POST">
                        @csrf
                        @if ($data ?? false)
                            <input type="hidden" name="id" value="{{ $data->id }}">
                        @endif
                        <div class="row g-4">
                            <div class="col-md-4">
                                <label class="form-label">Nama</label>
                                <input type="text" class="form-control" name="nama" placeholder="Nama" required value="{{ old('nama', ($data->nama ?? null)) }}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="email" placeholder="Email" required value="{{ old('email', ($data->email ?? null)) }}">
                            </div>
                            @php
                                $unit_bisnis = App\Models\UnitBisnis::pluck('nama', 'id');
                                $unit_bisnis_ids = isset($data) ? ($data->userUnitBisnis->plucK('unit_bisnis_id')->toArray() ?? []) : [];
                                $unit_bisnis_ids = old('unit_bisnis_ids', (count($unit_bisnis_ids) > 0 ? $unit_bisnis_ids :  [$data->unit_bisnis_id ?? []] ));
                            @endphp
                            <div class="col-md-4">
                                <label class="form-label">Unit Bisnis</label>
                                <select class="form-select select2" name="unit_bisnis_ids[]" data-select2-selector="user" multiple>
                                    @foreach ($unit_bisnis as $key => $value)
                                        <option value="{{ $key }}" {{ in_array($key, $unit_bisnis_ids) ? 'selected' : '' }}>{{ $value }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Role</label>
                                @php
                                    $role = App\Models\Role::pluck('name', 'id');
                                @endphp
                                <select class="form-control select2" name="role_id" data-select2-selector="icon">
                                    <option value="">Pilih Role</option>
                                    @foreach ($role as $key => $value)
                                        <option value="{{ $key }}" {{ old('role_id', ($data->role_id ?? null)) == $key ? 'selected' : '' }}>{{ $value }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Password</label>
                                <input type="password" class="form-control" name="password"
                                    placeholder="Masukan Password">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Konfirmasi Password</label>
                                <input type="password" class="form-control" name="confirm_password"
                                    placeholder="Masukan Konfirmasi Password">
                            </div>
                            {{-- <div class="col-md-8">
                                <label class="form-label
                                ">Tanda Tangan</label>
                                <div id="signature-left" class="signature border overflow-hidden">
                                    <canvas width="700" height="250" style="touch-action: none;"
                                        id="signatureCanvas"></canvas><br />
                                    <textarea id="signatureLeft64" name="ttd_pihak_pemohon_penggugat" style="display:none"></textarea>
                                </div>
                                <div>
                                    <button type="button" class="btn signature-clear  float-end mt-2"
                                        style="font-weight: 400; cursor:pointer; background: #ffc107; padding: 0; font-size: 12px; vertical-align: middle"><i
                                            class="fas fa-eraser"></i>&nbsp;Ulangi
                                        tanda tangan</button>
                                </div>
                            </div> --}}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @push('scripts')
        <script>
            $('.btnSubmit').on('click', function() {
                $('#formSubmit').submit();
            });
        </script>
    @endpush
</x-app-layout>
