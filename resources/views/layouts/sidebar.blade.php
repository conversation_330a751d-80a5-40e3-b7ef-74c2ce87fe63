<nav class="nxl-navigation">
    <div class="navbar-wrapper">
        <div class="m-header justify-content-center">
            <a href="{{ route('dashboard') }}" class="b-brand">
                <img src="/assets/images/logo.png" alt="" class="logo logo-lg" />
                <img src="/assets/images/logo.png" alt="" class="logo logo-sm" />
            </a>
        </div>
        <div class="navbar-content">
            <ul class="nxl-navbar">
                <div class="px-4 my-2">
                    <a href="/surat-keluar/create" class="btn btn-primary text-white">
                        <div class="d-flex align-items-center gap-2">
                            <i class="feather-plus"></i>
                            <div class="text_create_surat">
                                Buat Surat
                            </div>
                        </div>
                    </a>
                </div>
                @php
                    $menus = [
                        [
                            'icon' => 'feather-airplay',
                            'name' => 'Dashboard',
                            'url' => route('dashboard'),
                            'active' => ['dashboard.index'],
                            'child' => [],
                        ],
                        [
                            'icon' => 'feather-mail',
                            'name' => 'Surat Masuk',
                            'url' => route('surat-masuk.index'),
                            'permission' => 'Surat Masuk.List',
                            'active' => ['surat-masuk.index', 'surat-masuk.show'],
                            'child' => [],
                        ],
                        [
                            'icon' => 'feather-mail',
                            'name' => 'Surat Keluar',
                            'url' => route('surat-keluar.index'),
                            'permission' => 'Surat Keluar.List',
                            'active' => [
                                'surat-keluar.index',
                                'surat-keluar.show',
                                'surat-keluar.create',
                                'surat-keluar.edit',
                            ],
                            'child' => [],
                        ],
                        [
                            'icon' => 'feather-mail',
                            'name' => 'Surat Kadaluarsa',
                            'url' => route('surat-expired.index'),
                            'permission' => 'Surat Kadaluarsa.List',
                            'active' => [
                                'surat-expired.index',
                                'surat-expired.show',
                                'surat-expired.create',
                                'surat-expired.edit',
                            ],
                            'child' => [],
                        ],
                        [
                            'icon' => 'fas fa-file-signature',
                            'name' => 'Dokumen Esign',
                            'url' => route('document-esign.documentEsignIndex'),
                            'permission' => 'Dokumen Esign.List',
                            'active' => [
                                'document-esign.documentEsignIndex',
                            ],
                            'child' => [],
                        ],
                        [
                            'icon' => 'feather-mail',
                            'name' => 'Rekap Surat',
                            'url' => route('rekap-surat.index'),
                            'permission' => 'Rekap Surat.List',
                            'active' => [
                                'rekap-surat.index',
                            ],
                            'child' => [],
                        ],
                        [
                            'icon' => 'fas fa-file-upload',
                            'name' => 'Upload Dokumen Esign',
                            'url' => route('upload-document-esign.index'),
                            'permission' => 'Upload Document Esign.List',
                            'active' => [
                                'upload-document-esign.index',
                            ],
                            'child' => [],
                        ],
                        [
                            'icon' => 'feather-user',
                            'name' => 'Management',
                            'url' => '#',
                            'active' => [
                                'pengguna.index',
                                'pengguna.create',
                                'pengguna.edit',
                                'kop-surat.index',
                                'kop-surat.create',
                                'kop-surat.edit',
                                'role-permission.index',
                                'role-permission.create',
                                'role-permission.edit',
                            ],
                            'child' => [
                                [
                                    'icon' => 'feather-user',
                                    'name' => 'Pengguna',
                                    'url' => route('pengguna.index'),
                                    'permission' => 'Pengguna.List',
                                    'active' => ['pengguna.index', 'pengguna.create', 'pengguna.edit'],
                                    'child' => [],
                                ],
                                [
                                    'icon' => 'feather-user',
                                    'name' => 'Kontak',
                                    'url' => route('kontak.index'),
                                    'permission' => 'Kontak.List',
                                    'active' => ['kontak.index', 'kontak.create', 'kontak.edit'],
                                    'child' => [],
                                ],
                                [
                                    'icon' => 'feather-user',
                                    'name' => 'Unit Bisnis',
                                    'permission' => 'Unit Bisnis.List',
                                    'url' => route('unit-bisnis.index'),
                                    'active' => ['unit-bisnis.index'],
                                    'child' => [],
                                ],
                                [
                                    'icon' => 'feather-user',
                                    'name' => 'Jenis Surat',
                                    'permission' => 'Jenis Surat.List',
                                    'url' => route('jenis-surat.index'),
                                    'active' => ['jenis-surat.index'],
                                    'child' => [],
                                ],
                                [
                                    'icon' => 'feather-user',
                                    'name' => 'Kop Surat',
                                    'url' => route('kop-surat.index'),
                                    'active' => ['kop-surat.index', 'kop-surat.create', 'kop-surat.edit'],
                                    'permission' => 'Kop Surat.List',
                                    'child' => [],
                                ],
                                [
                                    'icon' => 'feather-user',
                                    'name' => 'Peran & Hak Akses',
                                    'url' => route('role-permission.index'),
                                    'active' => [
                                        'role-permission.index',
                                        'role-permission.create',
                                        'role-permission.edit',
                                    ],
                                    'permission' => 'Role & Permission.List',
                                    'child' => [],
                                ],
                            ],
                        ],
                        [
                            'icon' => 'feather-settings',
                            'name' => 'Pengaturan',
                            'url' => '#',
                            'active' => ['pengaturan.e-sign.index'],
                            'child' => [
                                [
                                    'icon' => '',
                                    'name' => 'Default',
                                    'url' => route('pengaturan.default.index'),
                                    'permission' => 'Pengaturan.Default',
                                    'active' => ['pengaturan.default.index'],
                                    'child' => [],
                                ],
                                [
                                    'icon' => '',
                                    'name' => 'eSign Mekari',
                                    'url' => route('pengaturan.e-sign.index'),
                                    'permission' => 'Pengaturan.eSign Mekari',
                                    'active' => ['pengaturan.e-sign.index'],
                                    'child' => [],
                                ],
                            ],
                        ],
                    ];
                @endphp
                <li class="nxl-item nxl-caption">
                    <label>Navigation</label>
                </li>
                @foreach ($menus as $item)
                    @if (count($item['child']) > 0)
                        @if (canPermissionMultiple(array_column($item['child'], 'permission')) ||
                                count(array_column($item['child'], 'permission')) === 0)
                            <li
                                class="nxl-item nxl-hasmenu {{ set_active($item['active'] ?? []) }} {{ set_active($item['active'] ?? [], 'nxl-trigger') }}">
                                <a href="{{ $item['url'] ?? '' }}" class="nxl-link">
                                    <span class="nxl-micon"><i class="{{ $item['icon'] }}"></i></span>
                                    <span class="nxl-mtext">{{ $item['name'] }}</span>
                                    <span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                                </a>
                                <ul class="nxl-submenu">
                                    @foreach ($item['child'] as $child)
                                        @if ((isset($child['permission']) && canPermission($child['permission'])) || !isset($child['permission']))
                                            <li class="nxl-item {{ set_active($child['active'] ?? []) }}"><a
                                                    class="nxl-link"
                                                    href="{{ $child['url'] ?? '' }}">{{ $child['name'] }}</a>
                                            </li>
                                        @endif
                                    @endforeach
                                </ul>
                            </li>
                        @endif
                    @else
                        @if ((isset($item['permission']) && canPermission($item['permission'])) || !isset($item['permission']))
                            <li class="nxl-item {{ set_active($item['active'] ?? []) }}">
                                <a href="{{ $item['url'] }}" class="nxl-link">
                                    <span class="nxl-micon"><i class="{{ $item['icon'] }}"></i></span>
                                    <span class="nxl-mtext">{{ $item['name'] }}</span>
                                </a>
                            </li>
                        @endif
                    @endif
                @endforeach
            </ul>
        </div>
    </div>
</nav>
