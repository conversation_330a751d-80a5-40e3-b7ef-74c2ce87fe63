function hapus(id) {
    data = $(`#hapus${id}`).attr("data");
    Swal.fire({
        title: "Apakah yakin?",
        text: `Data ${data} akan Dihapus`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#6492b8da",
        cancelButtonColor: "#d33",
        confirmButtonText: "Hapus",
        cancelButtonText: "Batal",
    }).then((result) => {
        if (result.isConfirmed) {
            $(`#form-hapus${id}`).submit();
        }
    });
}

function deleteDataTable(nama, urlTarget, table) {
    console.log('nansnsdnd');
    Swal.fire({
        title: "Apakah yakin?",
        text: `Data ${nama} akan dihapus`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#6492b8da",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yakin, hapus",
        cancelButtonText: "Bat<PERSON>",
    }).then((result) => {
        console.log(result, 'result');
        if (result.isConfirmed) {
            $.ajax({
                url: urlTarget,
                method: "post",
                data: [{ name: "_method", value: "DELETE" }],
                success: function (res) {
                    table.draw();
                    Swal.fire(`Berhasil dihapus`, res.message, "success");
                },
                error: function (res) {
                    console.log(res);
                    Swal.fire(`Gagal`, `${res.responseJSON.message}`, "error");
                },
            });
        }
    });
}

async function sendData(url, type, data) {
    const config = {
        method: type,
        url: url,
        data: data,
    };
    const result = await axios(config)
                    .then((res) => res.data)
                    .then(async (res) => {
                        return res;
                    }).catch(async (err) => {
                        const message = err.response?.data.message ?? err.responseJSON?.message
                        Swal.fire(`Gagal`, message, "error");
                        return {
                            status: false,
                            message: message
                        }
                    });

    return result;
}


async function sendDataFile(url, type, data) {
    const header = {
        "Content-Type": "multipart/form-data",
    };

    const config = {
        method: type,
        url: url,
        header: header,
        data: data
    };

    const result = await axios(config)
                    .then((res) => res.data)
                    .then(async (res) => {
                        return res;
                    }).catch(async (err) => {
                        const message = err.response.data.message ?? err.responseJSON?.message
                        Swal.fire(`Gagal`, message, "error");
                        return err.response;
                    });

    return result;
}


$('#fm').on('click', function() {
    const fmModal = $('.fm-modal');
    if(fmModal.length > 0) {
        fmModal.removeClass('modal');
    }
});

$('.deleteData').on('click', function() {
    let name = $(this).data('name');
    let id = $(this).data('id');
    Swal.fire({
        title: "Apakah yakin?",
        text: `Data ${name} akan Dihapus`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#6492b8da",
        cancelButtonColor: "#d33",
        confirmButtonText: "Hapus",
        cancelButtonText: "Batal",
    }).then((result) => {
        if (result.isConfirmed) {
            $(`#form-hapus${id}`).submit();
        }
    });
})


function deleteArray(optArray, index) {
    let tempArray = optArray;
    tempArray.splice(index, 1);
    return tempArray;
}


export const handleRender = () => {
    $(".reply_message").each(function(index) {
        const buttonId = `show_hide_reply_${index}`;
        if (!$(this).prev().hasClass("show_hide_reply")) {
            $(this).addClass("d-none");
            $(this).before(`<button id="${buttonId}" class="btn bg-secondary text-white show_hide_reply px-2 py-0 mb-3 mt-4  rounded-3"><i class="fa-solid fa-ellipsis"></i></button>`);
            $(`#${buttonId}`).on("click", function() {
                $(this).next(".reply_message").toggleClass("d-none");
                const isHidden = $(this).next(".reply_message").hasClass("d-none");
            });
        }
    });
};


export const canPermission = (name) => {
    const permission_name = permissions.map((permission) => permission.name);
    if (permission_name.includes(name)) {
        return true;
    }
    return false;
}
