import { RouterProvider, createBrowserRouter } from "react-router-dom";
// import SuratMasuk from "../pages/surat-masuk";
// import DetailSuratMasuk from "../pages/surat-masuk/detail";
// import CreateEditSuratMasuk from "../pages/surat-masuk/create-edit";
import CreateEditSuraKeluar from "../pages/surat-keluar/create-edit";
import DetailSuratMasuk from "../pages/surat-masuk/detail";
import HistorySurat from "../pages/surat-keluar/history";
import PreviewPDF from "../pages/surat-masuk/preview-pdf";
import CreateUpdateUploadFileEsign from "../pages/upload-document-esign/index";
import DocumentEsignIndex from "../pages/document-esign";

const router = createBrowserRouter([
    {
        // Component: () => <AppLayout />,
        children: [
            // {
            //     path: "/surat-masuk",
            //     element: <SuratMasuk />,
            // },
            {
                path: "/surat-keluar/:id/detail",
                element: <DetailSuratMasuk />,
            },
            {
                path: "/surat-keluar/create",
                element: <CreateEditSuraKeluar />,
            },
            {
                path: "/surat-keluar/:id/revision",
                element: <CreateEditSuraKeluar />,
            },
            {
                path: "/surat-masuk/:id/detail",
                element: <DetailSuratMasuk />,
            },
            {
                path: "/surat-keluar/history/:id",
                element: <HistorySurat />,
            },
            {
                path: "/upload-document-esign/create",
                element: <CreateUpdateUploadFileEsign />,
            },
            {
                path: "/document/:id",
                element: <DocumentEsignIndex />,
            },
            // {
            //     path: "/surat-masuk/render-pdf/:id",
            //     element: <PreviewPDF />,
            // },
        ],
    },
]);

export default function RouteApps() {
    return <RouterProvider router={router} />;
}
