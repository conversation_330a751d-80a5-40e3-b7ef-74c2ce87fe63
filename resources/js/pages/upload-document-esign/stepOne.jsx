import axios from "axios";
import React, { useEffect, useRef, useState } from "react";
import { Rnd } from "react-rnd";

export default function StepOneEsign({ step, collectData, handleCollectData }) {
    const uploadFileRef = useRef(null);
    return (
        <div className="px-4 py-5">
            <div className="row justify-content-center">
                <div className="col-md-8 max-auto">
                    <label
                        htmlFor="file-upload"
                        className=" d-flex align-items-center justify-content-center p-5 rounded cursor-pointer"
                        style={{
                            border: "2px dashed #e2e8f0",
                            flexDirection: "column",
                        }}
                    >
                        <div>
                            <i
                                className="fas fa-file-upload"
                                style={{ fontSize: 30 }}
                            ></i>
                        </div>
                        <p className="mt-2 text-gray-600">
                            Silahkan unggah file PDF
                        </p>
                        <input
                            id="file-upload"
                            type="file"
                            accept="application/pdf"
                            ref={uploadFileRef}
                            onChange={(e) => {
                                handleCollectData("file", e.target.files?.[0]);
                            }}
                            className="hidden"
                        />
                    </label>
                    {collectData.file && (
                        <div className="card border p-4 mt-3">
                            <div className="d-flex align-items-center justify-content-between">
                                <h6 className="fw-bold">
                                    {collectData.file && (
                                        <p>{collectData.file.name}</p>
                                    )}
                                </h6>
                                <button
                                    className="btn btn-outline-danger p-2"
                                    onClick={() => {
                                        uploadFileRef.current.value = ""
                                        handleCollectData("file", null)
                                    }}
                                >
                                    <i className="fas fa-trash-alt"></i>
                                </button>
                            </div>
                            <div className="mt-4">
                                <iframe
                                    src={
                                        collectData.file &&
                                        URL.createObjectURL(collectData.file)
                                    }
                                    width="100%"
                                    height="800px"
                                ></iframe>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
