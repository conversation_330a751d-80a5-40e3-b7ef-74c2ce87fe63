import React, { useState } from "react";
import Swal from "sweetalert2";
import StepThreeEsign from "./stepThree";
import StepTwoEsign from "./stepTwo";
import StepOneEsign from "./stepOne";

export default function CreateUpdateUploadFileEsign() {
    const [step, setStep] = useState(1);
    const [stepActive, setStepActive] = useState([1]);
    const [loadingSubmit, setLoadingSubmit] = useState(false);
    const [collectData, setCollectData] = useState({
        file: "",
        signers: [
            {
                id: "",
                nama: "",
                email: "",
            },
        ],
        annotations: [],
    });

    const stepList = [
        {
            step: 1,
            title: "Upload Document",
            icon: "feather-upload"
        },
        {
            step: 2,
            title: "Add Signers",
            icon: "feather-user-plus",
        },
        {
            step: 3,
            title: "Set Posisi",
            icon: "feather-map-pin",
        },
        {
            step: 4,
            title: "Review & Send",
            icon: "feather-send",
        },
    ];

    const handleChangeStep = (type, toStep) => {
        if (toStep) {
            setStep(toStep);
            setStepActive(stepActive.filter((item) => item === toStep));
            return;
        }

        if (type === "next") {
            const validation = validationStep();
            if (!validation.status) {
                Swal.fire({
                    icon: "error",
                    title: "Gagal",
                    text: validation.message,
                });
                return;
            }

            if (step === 1) {
                const signerFilter = collectData?.annotations?.filter((item) =>
                    collectData?.signers?.find(
                        (signer) => signer.value === item.signer?.value
                    )
                );
                collectData.annotations = signerFilter;
            }

            const newStep = step + 1;
            setStep(newStep);
            setStepActive([...stepActive, newStep]);
        } else {
            const newStep = step - 1;
            setStep(newStep);
            setStepActive(stepActive.filter((item) => item !== step));
        }
    };

    const validationStep = () => {
        if (step === 1) {
            if (!collectData.file) return {
                status: false,
                message: "File tidak boleh kosong",
            }
        }

        if (step === 2) {
            if (
                collectData?.signers?.filter((item) => item.id !== "")
                    ?.length === 0
            )
            return {
                status: false,
                message: "Signer tidak boleh kosong",
            }
        }

        if (step === 3) {
            const uniqueSigners = [
                ...new Set(
                    collectData.annotations.map((annotation) => annotation.signer.value)
                ),
            ];

            const signersIds = collectData.signers.map((item) => item.value)

            if (uniqueSigners.filter((item) => signersIds.includes(item)).length !== signersIds.length) {
                return {
                    status: false,
                    message: "Setiap signer harus memiliki setidaknya satu tanda tangan.",
                };
            }
        }

        return {
            status: true,
            message: "",
        }
    };

    const handleCollectData = (key, data) => {
        setCollectData({
            ...collectData,
            [key]: data,
        });
    };

    const handleSubmit = async () => {
        Swal.fire({
            title: "Apakah anda yakin?",
            text: "Data yang sudah dikirim tidak dapat diubah, pastikan data yang anda masukkan sudah benar",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Ya, Simpan!",
            cancelButtonText: "Batal",
        }).then((result) => {
            if (result.isConfirmed) {
                submitData();
            }
        });
    };

    const submitData = async () => {
        const csrf_token = document.querySelector('meta[name="csrf-token"]');
        setLoadingSubmit(true);
        try {
            const annotationsFilter = collectData?.annotations?.filter((item) =>
                collectData?.signers?.find(
                    (signer) => signer.value === item.signer?.value
                )
            );

            collectData.annotations = annotationsFilter;
            const { data } = await axios.post(
                `/upload-document-esign`,
                collectData,
                {
                    headers: {
                        "X-CSRF-TOKEN": csrf_token.content,
                        "Content-Type": "multipart/form-data",
                    },
                }
            );

            if (data.status) {
                Swal.fire({
                    icon: "success",
                    title: "Berhasil",
                    text: data.message,
                });

                setCollectData({})
                setTimeout(() => {
                    window.location.href = "/upload-document-esign";
                }, 1000);
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Tidak dapat memproses permintaan, silahkan coba lagi",
                    text: data.message,
                });
            }
        } catch (error) {
            Swal.fire({
                icon: "error",
                title: "Gagal",
                text: error?.response?.data?.message,
            });
        }
        setLoadingSubmit(false);
    };

    return (
        <div className="content-area items-details-active">
            <div className="items-details">
                <div className="items-details-footer mail-action-editor mx-4 mt-3 mb-5 border border-top-0 rounded-3">
                    <div
                        className="px-4 border-bottom d-flex aligment-items-center"
                        style={{ paddingTop: "20px", paddingBottom: "20px" }}
                    >
                        <div className="d-flex align-items-center">
                            <a
                                href="/upload-document-esign"
                                className="avatar-text avatar-md item-info-close"
                                data-bs-toggle="tooltip"
                                data-bs-trigger="hover"
                                title="Back"
                            >
                                <i className="feather-arrow-left"></i>
                            </a>
                            <h4 className="mb-0 ms-3">Kembali</h4>
                        </div>
                        <div className="d-flex justify-content-center align-items-center gap-3 mx-auto">
                            {stepList.map((item, index) => (
                                <div
                                    key={index}
                                    className="d-flex justify-content-center align-items-center gap-3"
                                >
                                    <div
                                        key={index}
                                        className="d-flex align-items-center gap-2"
                                    >
                                        <div className={``}>
                                            <i
                                                className={`feather ${
                                                    item.icon
                                                } fs-18
                                                ${
                                                    stepActive.includes(
                                                        item.step
                                                    )
                                                        ? "text-primary"
                                                        : "text-muted"
                                                }
                                                `}
                                            ></i>
                                        </div>
                                        <div
                                            className={`
                                        fs-16
                                        ${
                                            stepActive.includes(item.step)
                                                ? "text-primary fw-bold"
                                                : "text-muted fw-normal"
                                        }
                                        `}
                                        >
                                            {item.title}
                                        </div>
                                    </div>
                                    {index !== stepList.length - 1 && (
                                        <div>
                                            <i className="feather-arrow-right fs-20"></i>
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>
                    <div
                        className=""
                        style={{
                            maxHeight: "calc(100vh - 160px)",
                            height: "calc(100vh - 160px)",
                            overflowY: "auto",
                        }}
                    >
                        {(() => {
                            switch (step) {
                                case 1:
                                    return (
                                        <StepOneEsign
                                            step={step}
                                            collectData={collectData}
                                            handleCollectData={
                                                handleCollectData
                                            }
                                        />
                                    )
                                case 2:
                                    return (
                                        <StepTwoEsign
                                            step={step}
                                            collectData={collectData}
                                            handleCollectData={
                                                handleCollectData
                                            }
                                        />
                                    );
                                case 3:
                                    return (
                                        <StepThreeEsign
                                            step={step}
                                            collectData={collectData}
                                            handleCollectData={
                                                handleCollectData
                                            }
                                        />
                                    );
                                case 4:
                                    return (
                                        <div className="px-4 py-5">
                                            <div className="row justify-content-center">
                                                <div className="col-md-6 max-auto">
                                                    <div className="mb-4">
                                                        <h4 className="">
                                                            Review & Send
                                                        </h4>
                                                        <div className="mt-5">
                                                            <div className="d-flex align-items-center justify-content-between">
                                                                <h5 className="">
                                                                    Signers
                                                                </h5>
                                                                <div>
                                                                    <button
                                                                        className="btn btn-outline-dark rounded"
                                                                        onClick={() => {
                                                                            handleChangeStep(
                                                                                "prev",
                                                                                1
                                                                            );
                                                                        }}
                                                                    >
                                                                        Edit
                                                                    </button>
                                                                </div>
                                                            </div>
                                                            <div className="mt-3">
                                                                <div
                                                                    className="d-flex"
                                                                    style={{
                                                                        flexDirection:
                                                                            "column",
                                                                    }}
                                                                >
                                                                    {collectData?.signers?.map(
                                                                        (
                                                                            item,
                                                                            index
                                                                        ) => (
                                                                            <div
                                                                                key={
                                                                                    index
                                                                                }
                                                                                className="d-flex align-items-start gap-3 mb-3"
                                                                            >
                                                                                <div>
                                                                                    {index +
                                                                                        1}{" "}
                                                                                    .
                                                                                </div>
                                                                                <div>
                                                                                    <div>
                                                                                        <div className="fw-bold text-dark">
                                                                                            {
                                                                                                item.nama
                                                                                            }
                                                                                        </div>
                                                                                        <div>
                                                                                            {
                                                                                                item.email
                                                                                            }
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        )
                                                                    )}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    );
                            }
                        })()}
                    </div>
                    <div className="border-top w-full px-4 pt-3 pb-4 d-flex align-items-center justify-content-end">
                        {step > 1 && (
                            <button
                                className="btn btn-outline-dark rounded me-3"
                                onClick={() => {
                                    handleChangeStep("prev");
                                }}
                            >
                                Kembali
                            </button>
                        )}
                        {step < stepList.length ? (
                            <button
                                className="btn btn-primary rounded"
                                onClick={() => {
                                    handleChangeStep("next");
                                }}
                            >
                                Selanjutnya
                                <i className="feather-arrow-right fs-20 ms-2"></i>
                            </button>
                        ) : (
                            <button
                                onClick={() => {
                                    if (loadingSubmit) return;
                                    handleSubmit();
                                }}
                                className="btn btn-primary rounded"
                                disabled={loadingSubmit}
                            >
                                {loadingSubmit ? (
                                    <>
                                        <span
                                            className="spinner-border spinner-border-sm"
                                            role="status"
                                            aria-hidden="true"
                                        ></span>
                                        <span className="visually-hidden">
                                            Loading...
                                        </span>
                                    </>
                                ) : (
                                    <>
                                        Simpan
                                        <i className="feather-save fs-20 ms-2"></i>
                                    </>
                                )}
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
