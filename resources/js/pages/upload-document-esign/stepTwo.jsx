import axios from "axios";
import React, { useEffect, useState } from "react";
import Select from "react-select";

const selectStyles = {
    control: (baseStyles) => ({
        ...baseStyles,
        borderColor: "#e5e7eb",
        boxShadow: "none",
        padding: "4px 6px",
        borderRadius: "5px",
    }),
    option: (baseStyles, state) => ({
        ...baseStyles,
        backgroundColor: state.isSelected ? "#EDF2F7" : "#fff",
        color: "#2D3748",
        "&:hover": {
            backgroundColor: "#EDF2F7",
        },
        "&:active": {
            backgroundColor: "#EDF2F7",
        },
        '&[data-selected="true"]': {
            backgroundColor: "#EDF2F7",
        },
    }),
    multiValue: (baseStyles) => ({
        ...baseStyles,
        backgroundColor: "#fff",
        borderRadius: "12px",
        border: "1px solid #D5D7D8",
    }),
    menu: (baseStyles) => ({
        ...baseStyles,
        border: "1px solid #e5e7eb",
        boxShadow: "0 1px 2px 0 rgba(0, 0, 0, 0.10)",
        zIndex: 9999,
    }),
};

export default function StepTwoEsign({
    collectData,
    setCollectData,
    handleCollectData,
    step,
}) {
    const signers = collectData.signers ?? [];
    const [userData, setUserData] = useState([]);
    const [selectedUsers, setSelectedUsers] = useState([]);
    const getDataUser = async () => {
        const csrf_token = document.querySelector('meta[name="csrf-token"]');
        try {
            const { data } = await axios.get(`/api/pengguna/list`, {
                headers: {
                    "X-CSRF-TOKEN": csrf_token.content,
                },
            });

            setUserData((prevData) => [
                ...prevData,
                ...data.map((item) => {
                    return {
                        value: item.id,
                        nama: item.nama,
                        email: item.email,
                    };
                }),
            ]);
        } catch (error) {
            console.log(error);
        }
    };

    const getDataContact = async () => {
        const csrf_token = document.querySelector('meta[name="csrf-token"]');
        try {
            const { data } = await axios.get(`/api/contact/list`, {
                headers: {
                    "X-CSRF-TOKEN": csrf_token.content,
                },
            });

            setUserData((prevData) => [
                ...prevData,
                ...data.map((item) => {
                    return {
                        value: item.id,
                        nama: item.nama,
                        email: item.email,
                    };
                }),
            ]);
        } catch (error) {
            console.log(error);
        }
    };

    const getData = () => {
        getDataUser();
        getDataContact();
    }

    useEffect(() => {
        getData();
    }, []);

    const handleChangeSigners = (index, value) => {
        const newSigners = [...signers];
        newSigners[index] = value;
        handleCollectData('signers',newSigners);
    };

    const addNewSigner = () => {
        handleCollectData('signers', [...signers, { id: "", nama: "", email: "" }]);
    };

    const removeSigner = (index) => {
        if (signers.length === 1) return;
        const newSigners = [...signers];
        newSigners.splice(index, 1);
        handleCollectData('signers',newSigners);
    };

    const getFilteredOptions = () => {
        return userData.filter(
            (option) => !selectedUsers.includes(option.value)
        );
    };

    const handleRefresh = () => {
        getData();
    };

    const handleOpenNewContact = () => {
        
    };

    useEffect(() => {
        const selectedValues = signers.flatMap((item) => item?.value);
        setSelectedUsers(selectedValues);
    }, [signers]);

    return (
        <div className="px-4 py-5">
            <div className="row justify-content-center">
                <div className="col-md-8 max-auto">
                    <div className="mb-4">
                        <h4 className="">Tambahkan Penandatangan</h4>
                    </div>
                    <div
                        style={{
                            display: "flex",
                            flexDirection: "column",
                            gap: "16px",
                        }}
                    >
                        {signers.map((signer, index) => (
                            <div className="row align-items-center" key={index}>
                                <div
                                    className="p-4 col-md-11"
                                    style={{
                                        backgroundColor: "rgb(241, 245, 249)",
                                        borderRadius: "8px",
                                    }}
                                >
                                    <div className="row align-items-center g-4">
                                        <div className="col-md-5">
                                            <label className="form-label">
                                                Nama
                                            </label>
                                            <Select
                                                options={getFilteredOptions(
                                                    index
                                                )}
                                                placeholder="Pilih tujuan email"
                                                value={signer}
                                                filterOption={(
                                                    option,
                                                    inputValue
                                                ) => {
                                                    const inputValueLower =
                                                        inputValue.toLowerCase();
                                                    if (!inputValue)
                                                        return option;
                                                    return (
                                                        option?.data?.nama
                                                            ?.toLowerCase()
                                                            .includes(
                                                                inputValueLower
                                                            ) ||
                                                        option?.data?.email
                                                            ?.toLowerCase()
                                                            .includes(
                                                                inputValueLower
                                                            )
                                                    );
                                                }}
                                                formatOptionLabel={(
                                                    option,
                                                    { context }
                                                ) => {
                                                    if (context === "value") {
                                                        return (
                                                            <div className="d-flex align-items-center gap-3">
                                                                <div className="fs-12">
                                                                    {
                                                                        option.nama
                                                                    }
                                                                </div>
                                                            </div>
                                                        );
                                                    } else {
                                                        return (
                                                            <div className="d-flex align-items-center gap-3">
                                                                <div>
                                                                    <div className="fs-14 fw-bold">
                                                                        {
                                                                            option.nama
                                                                        }
                                                                    </div>
                                                                    <div className="fs-12 text-muted">
                                                                        {
                                                                            option?.email
                                                                        }
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        );
                                                    }
                                                }}
                                                onChange={(value) => {
                                                    handleChangeSigners(
                                                        index,
                                                        value
                                                    );
                                                }}
                                                styles={selectStyles}
                                            />
                                        </div>
                                        <div className="col-md-5">
                                            <label className="form-label">
                                                Email
                                            </label>
                                            <Select
                                                options={getFilteredOptions(
                                                    index
                                                )}
                                                placeholder="Pilih tujuan email"
                                                value={signer}
                                                filterOption={(
                                                    option,
                                                    inputValue
                                                ) => {
                                                    const inputValueLower =
                                                        inputValue.toLowerCase();
                                                    if (!inputValue)
                                                        return option;
                                                    return (
                                                        option?.data?.nama
                                                            ?.toLowerCase()
                                                            .includes(
                                                                inputValueLower
                                                            ) ||
                                                        option?.data?.email
                                                            ?.toLowerCase()
                                                            .includes(
                                                                inputValueLower
                                                            )
                                                    );
                                                }}
                                                formatOptionLabel={(
                                                    option,
                                                    { context }
                                                ) => {
                                                    if (context === "value") {
                                                        return (
                                                            <div className="d-flex align-items-center gap-3">
                                                                <div className="fs-12">
                                                                    {
                                                                        option.email
                                                                    }
                                                                </div>
                                                            </div>
                                                        );
                                                    } else {
                                                        return (
                                                            <div className="d-flex align-items-center gap-3">
                                                                <div>
                                                                    <div className="fs-14 fw-bold">
                                                                        {
                                                                            option.nama
                                                                        }
                                                                    </div>
                                                                    <div className="fs-12 text-muted">
                                                                        {
                                                                            option?.email
                                                                        }
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        );
                                                    }
                                                }}
                                                onChange={(value) => {
                                                    handleChangeSigners(
                                                        index,
                                                        value
                                                    );
                                                }}
                                                styles={selectStyles}
                                            />
                                        </div>
                                    </div>
                                    <div className="mt-3">
                                        <div
                                            onClick={() => {
                                                handleOpenNewContact();
                                            }}
                                            className="text-primary fw-medium d-inline"
                                            style={{
                                                borderBottom:
                                                    "1px solid #2B6CB0",
                                            }}
                                            role="button"
                                        >
                                            <i className="feather-plus me-1"></i>
                                            Tambahkan kontak baru
                                        </div>
                                    </div>
                                </div>
                                <div className="col-md-1">
                                    {signers.length > 1 && (
                                        <button
                                            className="btn px-2"
                                            onClick={() => {
                                                removeSigner(index);
                                            }}
                                        >
                                            <i
                                                className="feather-x text-danger"
                                                style={{
                                                    fontSize: "25px",
                                                }}
                                            ></i>
                                        </button>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                    <div className="mt-4">
                        <button
                            type="button"
                            onClick={() => {
                                addNewSigner();
                            }}
                            className="btn btn-primary fs-14 p-2"
                        >
                            <i className="feather-plus"></i>
                            Tambahkan
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}
