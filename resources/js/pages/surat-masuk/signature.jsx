import React, { useState } from "react";
import Swal from "sweetalert2";
import StepOneEsignManual from "./set-esign-manual/stepOne";
import StepTwoEsignManual from "./set-esign-manual/stepTwo";
import StepThreeEsignManual from "./set-esign-manual/stepThree";
import StepDisposisi from "./set-esign-manual/stepDisposisi";
import { canPermission } from "../../helper";
import StepFinish from "./set-esign-manual/stepFinish";
import RenderReactDom from "react-dom";
import Signer from "../document-esign/signer";
import html2canvas from "html2canvas";

export default function Signature() {
    const csrf_token = document.querySelector('meta[name="csrf-token"]');
    const [formData, setFormData] = useState({
        kepada: [],
        content_surat: "",
        type_message: "",
        approve_status: "",
        original_content: "",
    });
    const [errors, setErrors] = useState({});
    const [step, setStep] = useState(1);
    const [stepActive, setStepActive] = useState([1]);
    const [loadingSubmit, setLoadingSubmit] = useState(false);
    const [collectData, setCollectData] = useState({
        annotations: {},
        documentComplete: [],
        signatures: {},
        documentActive: {
            file_path: "",
            type: "",
            id: "",
        },
    });

    const stepList = [
        {
            step: 1,
            title: "Pilih Penerima",
            icon: "fas fa-user",
        },
        {
            step: 2,
            title: "Pilih Dokumen",
            icon: "fas fa-file-alt",
        },
        {
            step: 3,
            title: "Set Posisi",
            icon: "feather-map-pin",
        },
        {
            step: 4,
            title: "Tanda Tangan",
            icon: "fas fa-signature",
        },
        {
            step: 5,
            title: "Review & Kirim",
            icon: "fas fa-paper-plane",
        },
    ];

    const handleChangeStep = (
        type,
        toStep,
        dataDocument = {
            type: "",
            id: "",
        }
    ) => {
        if (dataDocument.type !== "" && dataDocument.id !== "") {
            handleCollectData("documentActive", dataDocument);
        }

        if (toStep) {
            setStep(toStep);
            setStepActive(stepActive.filter((item) => item === toStep));
            return;
        }

        if (type === "next") {
            const validation = validationStep();
            if (!validation.status) {
                Swal.fire({
                    icon: "error",
                    title: "Gagal",
                    text: validation.message,
                });
                return;
            }

            const newStep = step + 1;
            setStep(newStep);
            setStepActive([...stepActive, newStep]);
        } else {
            const newStep = step - 1;
            setStep(newStep);
            setStepActive(stepActive.filter((item) => item !== step));
        }
    };

    const validationStep = () => {
        const annotation =
            collectData?.annotations[collectData.documentActive?.id];
        if (step === 3) {
            if (
                !annotation ||
                !annotation?.filter((item) => item.id !== "")?.length
            )
                return {
                    status: false,
                    message: "Diperlukan setidaknya satu posisi tanda tangan.",
                };
        }

        if (step === 4) {
            const documentAnnotations =
                collectData?.annotations[collectData?.documentActive?.id];
            const countDocumentAnnotations = documentAnnotations?.length;
            const documentSignatures =
                collectData?.signatures[collectData?.documentActive?.id];
            const countDocumentSignatures = Object.keys(
                documentSignatures ?? {}
            ).length;

            const countNotSigned =
                countDocumentAnnotations - countDocumentSignatures;

            if (countNotSigned > 0)
                return {
                    status: false,
                    message: `${countNotSigned} tanda tangan belum ditempatkan, silakan periksa kembali posisi tanda tangan.`,
                };
        }

        return {
            status: true,
            message: "",
        };
    };

    const handleCollectData = (key, data) => {
        setCollectData({
            ...collectData,
            [key]: data,
        });
    };

    const handleSubmitFinish = async () => {
        let documentAnnotations = collectData?.annotations;

        const validation = validationFinish();
        if (!validation) return;

        const documentComplete = Object.keys(documentAnnotations);

        collectData.documentComplete = documentComplete;
        handleCollectData("documentComplete", documentComplete);

        Swal.fire({
            title: "Apakah anda yakin?",
            text: "Data disposisi dan tanda tangan akan disimpan. Pastikan semua data sudah benar.",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Ya, Kirim Sekarang!",
            cancelButtonText: "Batal",
        }).then((result) => {
            if (result.isConfirmed) {
                handleSubmitDisposisi();
            }
        });
    };

    const resetForm = () => {
        setFormData({
            kepada: [],
            content_surat: "",
            attachment: [],
            approve_status: "",
        });
        setErrors({});
    };

    const handleSubmitDisposisi = async () => {
        formData.attachment = [];
        formData.type_message = "forward";
        formData.original_content = data_detail?.content_surat?.content;
        formData.surat_id = data_detail?.id;
        formData.perihal = data_detail?.perihal;
        if (collectData?.documentComplete.length > 0) {
            formData.signature = collectData;
        }

        try {
            if (
                formData?.forwardList?.filter(
                    (item) => item.kepada.length === 0
                ).length > 0
            ) {
                Swal.fire({
                    title: "Ada kesalahan",
                    text: "Tujuan email harus diisi",
                    icon: "info",
                    showConfirmButton: false,
                    timer: 3000,
                });
                setLoadingSubmit(false);
                return;
            }

            setLoadingSubmit(true);

            const { data } = await axios.post(
                "/surat-keluar/disposisi",
                formData,
                {
                    headers: {
                        "X-CSRF-TOKEN": csrf_token.content,
                    },
                }
            );

            if (data?.status) {
                if (collectData?.documentComplete.length > 0) {
                    const resEsign = await submitEsignManual(data?.signatures);
                    if (!resEsign.status) {
                        Swal.fire({
                            title: "Gagal",
                            text: resEsign.message,
                            icon: "error",
                            showConfirmButton: false,
                        });
                        setLoadingSubmit(false);
                        return;
                    }
                }

                resetForm();
                // data_detail = data?.data;
                $('#refresh-detail').click();
                $("#modalPreview").modal("hide");
                Swal.fire({
                    title: "Berhasil",
                    text: data?.message,
                    icon: "success",
                    showConfirmButton: false,
                });
            } else {
                Swal.fire({
                    title: "Gagal",
                    text: resEsign.message,
                    icon: "error",
                    showConfirmButton: false,
                });
            }

            if (data?.error) {
                setErrors(data?.errors ?? {});
            }
        } catch (error) {
            Swal.fire({
                title: "Gagal",
                text: error?.response?.data?.message,
                icon: "error",
                showConfirmButton: false,
            });
        }
        setLoadingSubmit(false);
    };

    const submitEsignManual = async (dataSignatures) => {
        let collectSignatures = [];
        for (const item of dataSignatures) {
            const container = document.createElement("div");
            document.body.appendChild(container);

            item.type = parseInt(item.type_esign);
            item.fullName = item.type === 2 ? item.value_type_esign : "";
            item.signature =
                item.type === 1 ? item.value_type_esign : item.barcode;
            RenderReactDom.render(<Signer data={item} />, container);

            const canvas = await html2canvas(container, {
                backgroundColor: null,
            });
            const base64Image = canvas.toDataURL("image/png");

            collectSignatures.push({
                id: item.id,
                base_64_image: base64Image,
            });
            document.body.removeChild(container);
        }

        try {
            const { data: dataEsign } = await axios.post(
                `/surat-keluar/disposisi/esign-manual`,
                {
                    signatures: collectSignatures,
                    surat_id: data_detail?.id,
                },
                {
                    headers: {
                        "X-CSRF-TOKEN": csrf_token.content,
                    },
                }
            );

            return dataEsign;
        } catch (error) {
            Swal.fire({
                title: "Gagal",
                text: error?.response?.data?.message,
                icon: "error",
                showConfirmButton: false,
            });
            console.log(error);
        }
    };

    const validationFinish = () => {
        let documentAnnotations = collectData?.annotations;
        documentAnnotations = Object.values(documentAnnotations)
            .map((item) => Object.values(item))
            ?.flat();
        let documentSignatures = collectData?.signatures;
        documentSignatures = Object.values(documentSignatures)
            .map((item) => Object.values(item))
            ?.flat();

        const countNotSigned =
            (documentAnnotations.length ?? 0) -
            (documentSignatures.length ?? 0);
        if (countNotSigned !== 0) {
            Swal.fire({
                icon: "error",
                title: "Gagal",
                text: `${countNotSigned} tanda tangan belum ditempatkan, silakan periksa kembali posisi tanda tangan.`,
            });
            return false;
        }

        return true;
    };

    const validateForm = (data) => {
        const errors = {};
        const rules = {
            kepada: {
                required: "Tujuan email harus diisi",
            },
            content_surat: {
                required: "Isi surat harus diisi",
            },
        };

        Object.keys(rules).forEach((field) => {
            const value = data[field];
            const rule = rules[field];
            if (rule.required) {
                if (Array.isArray(value)) {
                    if (!value.length) errors[field] = rule.required;
                } else if (typeof value === "object" && value !== null) {
                    if (Object.keys(value).length === 0)
                        errors[field] = rule.required;
                } else if (!value) {
                    errors[field] = rule.required;
                }
            }
        });

        setErrors(errors);

        return errors;
    };

    const attachmentRequiredSign = data_detail?.attachment_file_surat?.filter(
        (item) => parseInt(item.required_signature) === 1
    );
    const documentComplete = collectData?.documentComplete?.length;
    const countDocument = attachmentRequiredSign.length + 1;

    return (
        <>
            <div className="modal-header">
                <h2 className="d-flex flex-column mb-0">
                    <span className="fs-md-18 fs-8 fw-bold mb-1">
                        Disposisi
                    </span>
                </h2>
                <div className="d-flex justify-content-center align-items-center gap-3 mx-auto">
                    {stepList.map((item, index) => (
                        <div
                            key={index}
                            className="d-flex justify-content-center align-items-center gap-md-3"
                        >
                            <div
                                key={index}
                                className="d-flex align-items-center gap-2"
                            >
                                <div className={``}>
                                    <i
                                        className={`feather ${
                                            item.icon
                                        } fs-md-18 fs-7
                                                ${
                                                    stepActive.includes(
                                                        item.step
                                                    )
                                                        ? "text-primary"
                                                        : "text-muted"
                                                }
                                                `}
                                    ></i>
                                </div>
                                <div
                                    className={`
                                        fs-md-16 fs-8
                                        ${
                                            stepActive.includes(item.step)
                                                ? "text-primary fw-bold"
                                                : "text-muted fw-normal"
                                        }
                                        `}
                                >
                                    {item.title}
                                </div>
                            </div>
                            {index !== stepList.length - 1 && (
                                <div>
                                    <i className="feather-arrow-right fs-md-20 fs-12 d-md-block d-none"></i>
                                </div>
                            )}
                        </div>
                    ))}
                </div>
                <div className="d-flex align-items-center gap-3">
                    <a
                        href="javascript:void(0)"
                        class="avatar-text avatar-md bg-soft-danger close-icon"
                        data-bs-dismiss="modal"
                    >
                        <i class="feather-x text-danger"></i>
                    </a>
                </div>
            </div>
            <div class="modal-body p-0">
                <div
                    className=""
                    style={{
                        maxHeight: `calc(100vh - ${
                            step === 1 ? "240px" : "160px"
                        })`,
                        height: `calc(100vh - ${
                            step === 1 ? "240px" : "160px"
                        })`,
                        overflowY: "auto",
                    }}
                >
                    {(() => {
                        switch (step) {
                            case 1:
                                return (
                                    <StepDisposisi
                                        step={step}
                                        formData={formData}
                                        setFormData={setFormData}
                                        setLoadingSubmit={setLoadingSubmit}
                                        loadingSubmit={loadingSubmit}
                                        handleSubmitDisposisi={
                                            handleSubmitDisposisi
                                        }
                                        handleChangeStep={handleChangeStep}
                                    />
                                );
                            case 2:
                                return (
                                    <StepOneEsignManual
                                        step={step}
                                        handleChangeStep={handleChangeStep}
                                        collectData={collectData}
                                        handleCollectData={handleCollectData}
                                        handleSubmit={handleSubmitFinish}
                                        validationFinish={validationFinish}
                                    />
                                );
                            case 3:
                                return (
                                    <StepTwoEsignManual
                                        step={step}
                                        collectData={collectData}
                                        handleCollectData={handleCollectData}
                                    />
                                );
                            case 4:
                                return (
                                    <StepThreeEsignManual
                                        step={step}
                                        collectData={collectData}
                                        handleCollectData={handleCollectData}
                                    />
                                );
                            case 5:
                                return (
                                    <StepFinish
                                        formData={formData}
                                        collectData={collectData}
                                    />
                                );
                        }
                    })()}
                </div>
                <div
                    className={`border-top w-full px-4 pt-3 pb-4 d-flex align-items-center ${
                        step > 1 && "justify-content-end"
                    }`}
                >
                    {step === 1 && (
                        <div className="col-md-8 mx-auto">
                            <div
                                className="pb-3 text-dark"
                                style={{ fontStyle: "italic" }}
                            >
                                <i className="fas fa-info-circle pe-2"></i>
                                Anda dapat langsung mengirim surat, atau
                                menandatanganinya terlebih dahulu sebelum
                                dikirim.
                            </div>
                            <div className="d-flex align-items-start gap-3">
                                {canPermission("Surat Masuk.Tanda Tangan") && (
                                    <div>
                                        <div
                                            role="button"
                                            onClick={() => {
                                                if (loadingSubmit) return;
                                                if (
                                                    formData?.forwardList?.filter(
                                                        (item) =>
                                                            item.kepada
                                                                .length === 0
                                                    ).length > 0
                                                ) {
                                                    Swal.fire({
                                                        title: "Ada kesalahan",
                                                        text: "Tujuan email harus diisi",
                                                        icon: "info",
                                                        showConfirmButton: false,
                                                        timer: 3000,
                                                    });
                                                    return;
                                                }
                                                handleChangeStep(null, 2);
                                            }}
                                            style={{ fontSize: "12px" }}
                                            className="btn btn-primary"
                                            data-bs-toggle="tooltip"
                                            data-bs-trigger="hover"
                                            title="Tanda Tangani dan Kirim"
                                        >
                                            <i className="fas fa-signature pe-2"></i>{" "}
                                            Tanda Tangani dan Kirim
                                        </div>
                                        {documentComplete > 0 && (
                                            <div
                                                className={`${
                                                    countDocument -
                                                    documentComplete
                                                        ? "text-dark"
                                                        : "text-success"
                                                } text-sm fw-bold mt-2`}
                                            >
                                                {countDocument -
                                                documentComplete ? (
                                                    <>
                                                        <span className="text-danger">
                                                            {documentComplete}
                                                        </span>
                                                        /{countDocument}
                                                    </>
                                                ) : (
                                                    <>
                                                        <i className="fas fa-check-circle pe-1"></i>
                                                        Semua
                                                    </>
                                                )}{" "}
                                                Dokumen telah ditandatangani
                                            </div>
                                        )}
                                        {!documentComplete > 0 && (
                                            <div className="text-danger text-sm fw-bold mt-2">
                                                *Terdapat {countDocument}{" "}
                                                Dokumen yang perlu
                                                ditandatangani
                                            </div>
                                        )}
                                    </div>
                                )}
                                <button
                                    disabled={loadingSubmit}
                                    type="button"
                                    onClick={() => {
                                        if (loadingSubmit) return;
                                        handleSubmitDisposisi();
                                    }}
                                    style={{ fontSize: "14px" }}
                                    className="btn btn-outline-primary"
                                    data-bs-toggle="tooltip"
                                    data-bs-trigger="hover"
                                    title="Kirim"
                                >
                                    {loadingSubmit ? (
                                        <i className="fas fa-spinner fa-spin me-2"></i>
                                    ) : (
                                        <i className="fas fa-paper-plane pe-2"></i>
                                    )}
                                    Kirim{" "}
                                </button>
                            </div>
                        </div>
                    )}
                    {step > 2 && (
                        <button
                            className="btn btn-outline-dark rounded me-3"
                            onClick={() => {
                                handleChangeStep("prev");
                            }}
                        >
                            Kembali
                        </button>
                    )}
                    {step < stepList.length - 1 && step > 2 && (
                        <button
                            className="btn btn-primary rounded"
                            onClick={() => {
                                handleChangeStep("next");
                            }}
                        >
                            Selanjutnya
                            <i className="feather-arrow-right fs-20 ms-2"></i>
                        </button>
                    )}
                    {step === 4 && (
                        <>
                            <button
                                onClick={() => {
                                    const validation = validationStep();
                                    if (!validation.status) {
                                        Swal.fire({
                                            icon: "error",
                                            title: "Gagal",
                                            text: validation.message,
                                        });
                                        return;
                                    }
                                    handleChangeStep(null, 2);
                                }}
                                className="btn btn-success rounded me-2"
                            >
                                <i className="fas fa-signature fs-20"></i>
                                Tanda Tangan Dokumen Lainnya
                            </button>
                            <button
                                onClick={() => {
                                    // if (loadingSubmit) return;
                                    // handleSubmitFinish();
                                    const validation = validationFinish();
                                    if (!validation) return;
                                    handleChangeStep(null, 5);
                                }}
                                className="btn btn-primary rounded"
                                disabled={loadingSubmit}
                            >
                                {loadingSubmit ? (
                                    <>
                                        <span
                                            className="spinner-border spinner-border-sm"
                                            role="status"
                                            aria-hidden="true"
                                        ></span>
                                        <span className="visually-hidden">
                                            Loading...
                                        </span>
                                    </>
                                ) : (
                                    <>
                                        Simpan & Review
                                        <i className="feather-save fs-20 ms-2"></i>
                                    </>
                                )}
                            </button>
                        </>
                    )}
                    {step === 5 && (
                        <>
                            <button
                                onClick={() => {
                                    if (loadingSubmit) return;
                                    handleSubmitFinish();
                                }}
                                className="btn btn-primary rounded"
                                disabled={loadingSubmit}
                            >
                                {loadingSubmit ? (
                                    <>
                                        <span
                                            className="spinner-border spinner-border-sm"
                                            role="status"
                                            aria-hidden="true"
                                        ></span>
                                        <span className="visually-hidden">
                                            Loading...
                                        </span>
                                    </>
                                ) : (
                                    <>
                                        Kirim Sekarang
                                        <i className="fas fa-paper-plane fs-20 ms-2"></i>
                                    </>
                                )}
                            </button>
                        </>
                    )}
                </div>
            </div>
        </>
    );
}
