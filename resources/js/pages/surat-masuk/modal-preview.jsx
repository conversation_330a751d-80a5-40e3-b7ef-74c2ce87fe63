import React, { useState, useRef, useEffect } from "react";
import Draggable from "react-draggable";
import { Resizable, ResizableBox } from "react-resizable";

import "pdfjs-dist/build/pdf.min.mjs";
import "pdfjs-dist/build/pdf.worker.min.mjs";

export default function ModalPreview({ data }) {
    const [pdfFile, setPdfFile] = useState("");
    const [sizePdf, setSizePdf] = useState({
        width: 0,
        height: 0,
    });
    const canvasRef = useRef(null);
    const [pagePdf, setPagePdf] = useState(1);
    const [totalPage, setTotalPage] = useState(1);

    const fetchPdfFile = async () => {
        try {
            const response = await axios.get(
                `/surat-masuk/render-pdf/${data_detail?.id}?download=true`,
                {
                    responseType: "blob",
                }
            );
            setPdfFile(response.data);
        } catch (error) {
            console.error("Gagal mengambil file PDF:", error);
        }
    };

    const renderPDFToCanvas = async (pageInject) => {
        if (!pdfFile) return;

        const fileReader = new FileReader();
        fileReader.onload = async function () {
            const typedArray = new Uint8Array(this.result);
            const pdf = await pdfjsLib.getDocument(typedArray).promise;
            setTotalPage(pdf.numPages);
            const page = await pdf.getPage(pageInject ?? pagePdf);

            const scale = 1.5;
            const viewport = page.getViewport({ scale: scale });
            const canvas = canvasRef.current;
            const context = canvas.getContext("2d");

            canvas.height = viewport.height * scale;
            canvas.width = viewport.width * scale;
            context.scale(scale, scale);

            setSizePdf({
                width: viewport.width,
                height: viewport.height,
            });

            const renderContext = {
                canvasContext: context,
                viewport: viewport,
            };

            await page.render(renderContext).promise.then(function () {
                data.tanda_tangan_surat
                    .filter((item) => item.referable_id === data_detail?.id)
                    .forEach((item) => {
                        if (parseInt(item.page) === (pageInject ?? 1)) {
                            const img = new Image();
                            img.src = item?.image;
                            img.onload = async () => {
                                const canvas = canvasRef.current;
                                if (canvas) {
                                    const scaleX = canvas.clientWidth / item.canvas_width;
                                    const scaleY = canvas.clientHeight / item.canvas_height;
                                    const autoWidth = item.height * (img.width / img.height) * scaleY;
                                    context.drawImage(
                                        img,
                                        item.posisi_x * scaleX,
                                        item.posisi_y * scaleY,
                                        autoWidth,
                                        item.height * scaleY
                                    );
                                }
                            };
                        }
                    });
            });
        };

        fileReader.readAsArrayBuffer(pdfFile);
    };

    useEffect(() => {
        renderPDFToCanvas();
    }, [pdfFile]);

    useEffect(() => {
        fetchPdfFile();
    }, []);

    const handleGetPage = (type) => {
        console.log("type", type);
        if (totalPage === 1) return;
        let page = pagePdf;
        if (type === "prev") {
            if (pagePdf === 1) return;
            page = pagePdf - 1;
            setPagePdf(page);
        } else {
            if (pagePdf === totalPage) return;
            page = pagePdf + 1;
            setPagePdf(page);
        }

        renderPDFToCanvas(page);
    };

    return (
        <>
            <div className="modal-header">
                <h2 className="d-flex flex-column mb-0">
                    <span className="fs-18 fw-bold mb-1">Preview Surat</span>
                </h2>
                <div className="d-flex align-items-center gap-3">
                    <a
                        href="javascript:void(0)"
                        class="avatar-text avatar-md bg-soft-danger close-icon"
                        data-bs-dismiss="modal"
                    >
                        <i class="feather-x text-danger"></i>
                    </a>
                </div>
            </div>
            <div
                class="modal-body py-0"
                style={{
                    margin: "0 auto",
                }}
            >
                <div className="p-4 position-relative">
                    <div
                        className="mb-3"
                        style={{
                            width: "100%",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                        }}
                    >
                        <div
                            className="d-flex align-items-center gap-3 justify-content-center py-2"
                            style={{
                                background: "white",
                                width: "200px",
                                borderRadius: "5px",
                                // boxShadow: "0 30px 40px 0 rgba(16, 36, 94, .2)",
                                border: "1px solid #e5e5e5",
                            }}
                        >
                            <div
                                role="button"
                                className="py-1 px-3"
                                disabled={pagePdf === 1}
                                onClick={() => handleGetPage("prev")}
                            >
                                <i class="fas fa-angle-left"></i>
                            </div>
                            <div style={{ fontSize: "18px", color: "black" }}>
                                {pagePdf} of {totalPage}
                            </div>
                            <div
                                role="button"
                                className="py-1 px-3"
                                disabled={pagePdf === totalPage}
                                onClick={() => handleGetPage("next")}
                            >
                                <i class="fas fa-angle-right"></i>
                            </div>
                        </div>
                    </div>
                    <div
                        className="parent_preview"
                        style={{
                            height: sizePdf.height,
                            width: sizePdf.width,
                            // border: "1px solid black",
                            position: "relative",
                        }}
                    >
                        <canvas
                            ref={canvasRef}
                            style={{
                                width: "100%",
                                height: "100%",
                                border: "1px solid black",
                            }}
                        ></canvas>
                    </div>
                </div>
            </div>
        </>
    );
}
