import React, { useState, useEffect } from "react";
import Select from "react-select";
import { Editor } from "@tinymce/tinymce-react";
import Swal from "sweetalert2";

export default function FormForward({
    step,
    userData,
    setFormData,
    formData,
    getDataFor,
}) {
    const [inputList, setInputList] = useState(
        formData?.forwardList ?? [
            { kepada: [], catatan: "", unit_bisnis_id: null },
        ]
    );
    const [catatan, setCatatan] = useState([]);
    const [userBisnisData, setUserUnitBisnisData] = useState([]);
    const [sendToList, setSendToList] = useState([]);
    const [dataFirstChange, setDataFirstChange] = useState({
        index: 0,
        unit_bisnis_id: null,
    });

    useEffect(() => {
        let selectedOptions = {};
        inputList.forEach((input, index) => {
            if (!input?.unit_bisnis_id) return;
            const unit_bisnis_id = input?.unit_bisnis_id?.value;
            if (!selectedOptions[unit_bisnis_id])
                selectedOptions[unit_bisnis_id] = [];
            const kepadaValues = input?.kepada?.map((opt) => opt.value) ?? [];
            selectedOptions[unit_bisnis_id] = [
                ...new Set([
                    ...selectedOptions[unit_bisnis_id],
                    ...kepadaValues,
                ]),
            ];
        });

        getFilteredOptions(
            dataFirstChange?.unit_bisnis_id,
            dataFirstChange?.index,
            selectedOptions
        );
    }, [dataFirstChange]);

    const handleInputChange = (
        name,
        selectedOption,
        index,
        unit_bisnis_id = null
    ) => {
        const list = [...inputList];
        list[index][`${name}`] = selectedOption;

        setInputList(list);
        setDataFirstChange({
            index: index,
            unit_bisnis_id: list[index]?.unit_bisnis_id?.value,
        });
        setFormData({ ...formData, forwardList: list });
    };

    const handleRemoveClick = (index) => {
        const list = [...inputList];
        list.splice(index, 1);
        setInputList(list);
        setFormData({ ...formData, forwardList: list });

        if (list.length === 0) {
        }
    };

    const handleAddClick = () => {
        setInputList([...inputList, { kepada: [], catatan: "" }]);
    };

    const getFilteredOptions = (unit_bisnis_id, index, selectedOptions) => {
        console.log("selectedOptions", selectedOptions);
        if (!inputList[index]?.unit_bisnis_id) return;

        const dataFiltered =
            userData?.[unit_bisnis_id]?.filter(
                (option) =>
                    !selectedOptions?.[unit_bisnis_id]?.includes(
                        option.value
                    ) ||
                    inputList?.[index]?.kepada?.some(
                        (selected) => selected.value === option.value
                    )
            ) ?? [];

        setSendToList((prevSendToList) => {
            const updatedSendToList = [...prevSendToList];
            updatedSendToList[index] = dataFiltered;
            return updatedSendToList;
        });
    };

    useEffect(() => {
        const user_unit_bisnis_data = auth_user?.user_unit_bisnis_data ?? [];
        user_unit_bisnis_data.map((item) => {
            item.value = item.id;
            item.label = item.nama;
            return item;
        });
        setUserUnitBisnisData(user_unit_bisnis_data);
        if (formData?.forwardList?.length > 0) {
            formData?.forwardList?.map((item, index) => {
                setCatatan((prevCatatan) => {
                    const updatedCatatan = [...prevCatatan];
                    updatedCatatan[index] = item?.catatan;
                    return updatedCatatan;
                });
            });
        }
    }, []);

    return (
        <div className="disposisi_form mt-4">
            {inputList.map((x, i) => {
                return (
                    <div className="d-flex align-items-start gap-3" key={i}>
                        <div className={`${i > 0 ? "mt-4" : ""} w-100`} key={i}>
                            <div className="row">
                                <div className="col-md-4">
                                    <Select
                                        options={userBisnisData}
                                        placeholder="Pilih unit bisnis"
                                        isClearable
                                        value={x.unit_bisnis_id}
                                        styles={{
                                            control: (baseStyles) => ({
                                                ...baseStyles,
                                                borderColor: "#e5e7eb",
                                                boxShadow: "none",
                                                padding: "4px 6px",
                                                borderRadius: "5px",
                                            }),
                                            option: (baseStyles, state) => ({
                                                ...baseStyles,
                                                backgroundColor:
                                                    state.isSelected
                                                        ? "#EDF2F7"
                                                        : "#fff",
                                                color: "#2D3748",
                                                "&:hover": {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                                "&:active": {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                                '&[data-selected="true"]': {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                            }),
                                            menu: (baseStyles) => ({
                                                ...baseStyles,
                                                border: "1px solid #e5e7eb",
                                                boxShadow:
                                                    "0 1px 2px 0 rgba(0, 0, 0, 0.10)",
                                                zIndex: 9999,
                                            }),
                                        }}
                                        onChange={async (value) => {
                                            if (!value) {
                                                handleInputChange(
                                                    "unit_bisnis_id",
                                                    null,
                                                    i
                                                );
                                                handleInputChange(
                                                    "kepada",
                                                    value,
                                                    i
                                                );
                                                return;
                                            }
                                            await getDataFor({
                                                unit_bisnis_id: value?.value,
                                                type: "user",
                                            });
                                            handleInputChange(
                                                "unit_bisnis_id",
                                                value,
                                                i
                                            );
                                        }}
                                    />
                                </div>
                                <div className="col-md-8">
                                    <Select
                                        isMulti
                                        className="mb-3"
                                        options={sendToList[i]}
                                        placeholder="Kepada"
                                        isDisabled={!x.unit_bisnis_id}
                                        value={x.kepada}
                                        filterOption={(option, inputValue) => {
                                            const inputValueLower =
                                                inputValue.toLowerCase();
                                            if (!inputValue) return option;
                                            return option?.data?.name
                                                ?.toLowerCase()
                                                .includes(inputValueLower);
                                        }}
                                        onChange={(value) =>
                                            handleInputChange(
                                                "kepada",
                                                value,
                                                i,
                                                x.unit_bisnis_id?.value
                                            )
                                        }
                                        formatOptionLabel={(
                                            option,
                                            { context }
                                        ) => {
                                            if (context === "value") {
                                                return (
                                                    <div className="d-flex align-items-center gap-3">
                                                        <div className="fs-12">
                                                            {option.name}
                                                        </div>
                                                    </div>
                                                );
                                            } else {
                                                return (
                                                    <div className="d-flex align-items-center gap-3">
                                                        <div>
                                                            <div className="fs-14 fw-bold">
                                                                {option.name}
                                                            </div>
                                                            <div className="fs-12 d-flex flex-wrap gap-3 mt-2">
                                                                {option?.user_roles?.map(
                                                                    (
                                                                        user,
                                                                        index
                                                                    ) => (
                                                                        <div
                                                                            key={
                                                                                index
                                                                            }
                                                                            className="badge bg-light text-secondary text-start"
                                                                        >
                                                                            <div>
                                                                                {
                                                                                    user?.nama
                                                                                }
                                                                            </div>
                                                                            <div className="mt-1">
                                                                                {
                                                                                    user?.email
                                                                                }
                                                                            </div>
                                                                        </div>
                                                                    )
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                );
                                            }
                                        }}
                                        styles={{
                                            control: (baseStyles) => ({
                                                ...baseStyles,
                                                borderColor: "#e5e7eb",
                                                boxShadow: "none",
                                                padding: "4px 6px",
                                                borderRadius: "5px",
                                            }),
                                            option: (baseStyles, state) => ({
                                                ...baseStyles,
                                                backgroundColor:
                                                    state.isSelected
                                                        ? "#EDF2F7"
                                                        : "#fff",
                                                color: "#2D3748",
                                                "&:hover": {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                                "&:active": {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                                '&[data-selected="true"]': {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                            }),
                                            multiValue: (baseStyles) => ({
                                                ...baseStyles,
                                                backgroundColor: "#fff",
                                                borderRadius: "12px",
                                                border: "1px solid #D5D7D8",
                                            }),
                                            menu: (baseStyles) => ({
                                                ...baseStyles,
                                                border: "1px solid #e5e7eb",
                                                boxShadow:
                                                    "0 1px 2px 0 rgba(0, 0, 0, 0.10)",
                                                zIndex: 9999,
                                            }),
                                        }}
                                        name="kepada"
                                    />
                                </div>
                            </div>
                            <Editor
                                apiKey="5391u6bfka4eor1n9a5d1r66gknipnlrbo7m37uxsb5nyye3"
                                initialValue={
                                    catatan[i] ??
                                    `
                                    <p></p>
                                    <p>
                                        <div>-- Disposisi Surat --</div>
                                        <div>Dari : ${
                                            data_detail?.pengirim?.nama
                                        }  < ${
                                        data_detail?.pengirim?.email
                                    } ></div>
                                        <div>Tanggal : ${
                                            data_detail?.date_formatted
                                        }</div>
                                        <div>Perihal : ${
                                            data_detail?.perihal
                                        }</div>
                                        <div>No Surat : ${
                                            data_detail?.no_surat ?? "-"
                                        }</div>

                                    </p>`
                                }
                                init={{
                                    height: 200,
                                    menubar: false,
                                    plugins:
                                        "visualblocks advlist autolink lists link image charmap preview anchor searchreplace visualblocks code fullscreen insertdatetime media table code help wordcount media",
                                    toolbar:
                                        "undo redo | bold italic underline strikethrough | link image | align | numlist bullist indent outdent",
                                    content_style:
                                        "body { font-family:Helvetica,Arial,sans-serif; font-size:14px }",
                                    images_upload_url: "/file-manager/upload",
                                    automatic_uploads: true,
                                    file_picker_types: "image",
                                    file_picker_callback: function (
                                        cb,
                                        value,
                                        meta
                                    ) {
                                        var input =
                                            document.createElement("input");
                                        input.setAttribute("type", "file");
                                        input.setAttribute("accept", "image/*");
                                        input.onchange = async function () {
                                            var file = this.files[0];
                                            var formData = new FormData();
                                            formData.append("file", file);

                                            const { data } = await axios.post(
                                                `/file-manager/upload`,
                                                formData,
                                                {
                                                    headers: {
                                                        "Content-Type":
                                                            "multipart/form-data",
                                                    },
                                                }
                                            );
                                            if (data.status) {
                                                cb(data.url, {
                                                    title: file.name,
                                                });
                                            }
                                        };
                                        input.click();
                                    },
                                }}
                                onEditorChange={(content, editor) => {
                                    const list = [...inputList];
                                    list[i].catatan = content;
                                    setInputList(list);
                                    setFormData({
                                        ...formData,
                                        forwardList: list,
                                    });
                                }}
                                name="catatan"
                            />
                            <div className="d-flex align-items-center gap-2">
                                {inputList.length - 1 === i && (
                                    <button
                                        className="btn btn-primary mt-3 p-2"
                                        onClick={handleAddClick}
                                    >
                                        <i className="fa fa-plus me-2"></i>
                                        Tambah Disposisi
                                    </button>
                                )}
                            </div>
                        </div>
                        <div>
                            <div className="p-3"></div>
                            <button
                                className="avatar-text avatar-sm p-3 bg-danger"
                                onClick={() => {
                                    handleRemoveClick(i);
                                }}
                            >
                                <i className="fas fa-trash-alt fs-14 text-white"></i>
                            </button>
                        </div>
                    </div>
                );
            })}
        </div>
    );
}
