import axios from "axios";
import React, { useEffect, useRef, useState } from "react";
import { Rnd } from "react-rnd";

export default function StepTwoEsign({ step, collectData, handleCollectData }) {
    const annotations = collectData.annotations ?? [];
    const [pdfFile, setPdfFile] = useState("");
    const [sizePdf, setSizePdf] = useState({
        width: 0,
        height: 0,
    });
    const canvasRef = useRef(null);
    const [pagePdf, setPagePdf] = useState(1);
    const [totalPage, setTotalPage] = useState(1);
    const boxSignerRef = useRef(null);
    const [focusedAnnotation, setFocusedAnnotation] = useState(null);

    const generateBackgroundColor = (email) => {
        let hash = 0;
        for (let i = 0; i < email.length; i++) {
            hash = email.charCodeAt(i) + ((hash << 5) - hash);
        }
        const color = (hash & 0x00ffffff).toString(16).toUpperCase();
        return {
            background: `#${"00000".substring(0, 6 - color.length) + color}80`,
            border: `#${color}`,
        };
    };

    const fetchPdfFile = async () => {
        try {
            const response = await axios.get(
                `/surat-masuk/render-pdf/${data_detail?.id}?download=true`,
                {
                    responseType: "blob",
                }
            );
            setPdfFile(response.data);
        } catch (error) {
            console.error("Gagal mengambil file PDF:", error);
        }
    };

    const renderPDFToCanvas = async (pageInject) => {
        if (!pdfFile) return;

        const fileReader = new FileReader();
        fileReader.onload = async function () {
            const typedArray = new Uint8Array(this.result);
            const pdf = await pdfjsLib.getDocument(typedArray).promise;
            setTotalPage(pdf.numPages);
            const page = await pdf.getPage(pageInject ?? pagePdf); // Render halaman pertama

            const scale = 1.5;
            const viewport = page.getViewport({ scale: scale });
            const canvas = canvasRef.current;
            const context = canvas.getContext("2d");

            canvas.height = viewport.height * scale;
            canvas.width = viewport.width * scale;
            context.scale(scale, scale);

            setSizePdf({
                width: viewport.width,
                height: viewport.height,
            });

            const renderContext = {
                canvasContext: context,
                viewport: viewport,
            };

            await page.render(renderContext).promise.then(function () {
                // data.tanda_tangan_surat.forEach((item) => {
                //     if (parseInt(item.page) === (pageInject ?? 1)) {
                //         const img = new Image();
                //         img.src = "/assets/images/barcode.png";
                //         img.onload = async () => {
                //             // Pastikan posisi tetap konsisten setiap kali dibuka dengan menggunakan posisi yang telah disimpan
                //             const posisiX = item?.posisi_x ?? 0; // Tambahkan posisi x yang disimpan ke posisi x item
                //             const posisiY = item?.posisi_y ?? 0; // Tambahkan posisi y yang disimpan ke posisi y item
                //             const ukuran = sizePdf.width
                //                 ? (60 * sizePdf.width) / viewport.width
                //                 : 60; // Sesuaikan ukuran berdasarkan lebar PDF saat ini dibandingkan dengan viewport default
                //             await context.drawImage(
                //                 img,
                //                 posisiX,
                //                 posisiY,
                //                 ukuran,
                //                 ukuran
                //             );
                //         };
                //     }
                // });
            });
        };

        fileReader.readAsArrayBuffer(pdfFile);
    };

    useEffect(() => {
        fetchPdfFile();
    }, []);

    useEffect(() => {
        renderPDFToCanvas();
    }, [pdfFile]);

    const handleGetPage = (type) => {
        if (totalPage === 1) return;
        let page = pagePdf;
        if (type === "prev") {
            if (pagePdf === 1) return;
            page = pagePdf - 1;
            setPagePdf(page);
        } else {
            if (pagePdf === totalPage) return;
            page = pagePdf + 1;
            setPagePdf(page);
        }

        renderPDFToCanvas(page);
    };

    const addSignatureToCanvas = (signer) => {
        setFocusedAnnotation(signer.value);
        if (annotations.some((annotation) => annotation.signer === signer))
            return;

        handleCollectData("annotations", [
            ...annotations,
            {
                signer: signer,
                position_x: 0,
                position_y: 0,
                element_width: 120,
                element_height: 60,
                canvas_width: canvasRef?.current?.clientWidth ?? 0,
                canvas_height: canvasRef?.current?.clientHeight ?? 0,
                page: pagePdf,
            },
        ]);
    };

    const handleFocusAnnotation = (id) => {
        setFocusedAnnotation(id);
    };

    const handleRemoveAnnotation = () => {
        const newAnnotations = annotations.filter(
            (annotation) => annotation.signer?.value !== focusedAnnotation
        );
        handleCollectData("annotations", newAnnotations);
        setFocusedAnnotation(null);
    };

    const handleClickOutside = (event) => {
        if (
            event.target.closest(".boxEsign") &&
            !event.target.closest(".annotation__signature")
        ) {
            setFocusedAnnotation(null);
        }
    };

    useEffect(() => {
        document.addEventListener("click", handleClickOutside, true);
        return () => {
            document.removeEventListener("click", handleClickOutside, true);
        };
    }, []);

    return (
        <div className="d-flex">
            <div
                className="p-4"
                style={{
                    height: "calc(100vh - 165px)",
                    width: "240px",
                    position: "fixed",
                    bottom: "auto",
                    zIndex: 9999,
                    background: "white",
                }}
            >
                <div className="fs-16 fw-bold mb-3 text-dark">Signers</div>
                <div
                    className="mt-4 d-flex gap-1"
                    style={{ flexDirection: "column" }}
                >
                    {collectData.signers.map((signer, index) => {
                        const annotationSigner =
                            annotations.find(
                                (annotation) =>
                                    annotation.signer.value === signer.value
                            ) ?? null;
                        return (
                            <div
                                role="button"
                                className={`d-flex align-items-center gap-3 p-2 rounded-3 cursor-pointer
                                    text-truncate
                                ${
                                    focusedAnnotation === signer.value
                                        ? "bg-body"
                                        : ""
                                }
                                `}
                                key={index}
                                onClick={() => {
                                    addSignatureToCanvas(signer);
                                }}
                            >
                                <div>
                                    <div
                                        className="avatar avatar-md"
                                        style={{
                                            background: generateBackgroundColor(
                                                signer.email
                                            )?.background,
                                        }}
                                    ></div>
                                </div>
                                <div>
                                    <div className="fs-14 fw-bold text-dark">
                                        {signer.nama}
                                    </div>
                                    <div className="fs-12 text-muted">
                                        {signer.email}
                                    </div>
                                    {annotationSigner && (
                                        <div className="fs-12 text-dark">
                                            Page: {annotationSigner?.page ?? 1}
                                        </div>
                                    )}
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
            <div
                className="boxEsign"
                style={{
                    background: "#f2f4f7",
                    display: "flex",
                    flexDirection: "column",
                    width: "100%",
                    position: "relative",
                }}
            >
                <div
                    style={{
                        paddingLeft: "312px",
                        paddingRight: "77px",
                        marginTop: "10px",
                        marginBottom: "50px",
                        paddingTop: "20px",
                        width: "100%",
                        height: "auto",
                        overflowX: "auto",
                        overflowY: "auto",
                    }}
                >
                    <div
                        className="mb-3"
                        style={{
                            width: "100%",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                        }}
                    >
                        <div
                            className="d-flex align-items-center gap-3 justify-content-center py-2"
                            style={{
                                background: "white",
                                width: "200px",
                                borderRadius: "5px",
                                // boxShadow: "0 30px 40px 0 rgba(16, 36, 94, .2)",
                                border: "1px solid #e5e5e5",
                            }}
                        >
                            <div
                                role="button"
                                className="py-1 px-3"
                                disabled={pagePdf === 1}
                                onClick={() => handleGetPage("prev")}
                            >
                                <i class="fas fa-angle-left"></i>
                            </div>
                            <div style={{ fontSize: "14px", color: "black" }}>
                                <b>{pagePdf}</b> of {totalPage}
                            </div>
                            <div
                                role="button"
                                className="py-1 px-3"
                                disabled={pagePdf === totalPage}
                                onClick={() => handleGetPage("next")}
                            >
                                <i class="fas fa-angle-right"></i>
                            </div>
                        </div>
                    </div>
                    <div
                        ref={boxSignerRef}
                        style={{
                            position: "relative",
                            overflowY: "hidden",
                            overflowX: "hidden",
                            boxShadow: "0 0 10px 0 rgba(0, 0, 0, 0.1)",
                        }}
                    >
                        {annotations.map((annotation, index) => {
                            const backgroundColor = generateBackgroundColor(
                                annotation.signer.email
                            )?.background;
                            const borderColor = generateBackgroundColor(
                                annotation.signer.email
                            )?.border;
                            return (
                                annotation.page === pagePdf && (
                                    <Rnd
                                        resizeHandleStyles={{
                                            bottomRight: {
                                                backgroundColor: "white",
                                                border: `2px solid ${borderColor}`,
                                                cursor: "se-resize",
                                                width: "10px",
                                                height: "10px",
                                                borderRadius: "50%",
                                                bottom: "-6px",
                                                right: "-6px",
                                                zIndex: 9999,
                                                display:
                                                    focusedAnnotation ===
                                                    annotation?.signer?.value
                                                        ? "block"
                                                        : "none",
                                            },
                                            bottomLeft: {
                                                border: `2px solid ${borderColor}`,
                                                backgroundColor: "white",
                                                cursor: "sw-resize",
                                                width: "10px",
                                                height: "10px",
                                                borderRadius: "50%",
                                                bottom: "-6px",
                                                left: "-6px",
                                                zIndex: 9999,
                                                display:
                                                    focusedAnnotation ===
                                                    annotation?.signer?.value
                                                        ? "block"
                                                        : "none",
                                            },
                                            topRight: {
                                                border: `2px solid ${borderColor}`,
                                                backgroundColor: "white",
                                                cursor: "nw-resize",
                                                width: "10px",
                                                height: "10px",
                                                borderRadius: "50%",
                                                top: "-6px",
                                                right: "-6px",
                                                zIndex: 9999,
                                                display:
                                                    focusedAnnotation ===
                                                    annotation?.signer?.value
                                                        ? "block"
                                                        : "none",
                                            },
                                            topLeft: {
                                                border: `2px solid ${borderColor}`,
                                                backgroundColor: "white",
                                                cursor: "nw-resize",
                                                width: "10px",
                                                height: "10px",
                                                borderRadius: "50%",
                                                top: "-6px",
                                                left: "-6px",
                                                zIndex: 9999,
                                                display:
                                                    focusedAnnotation ===
                                                    annotation?.signer?.value
                                                        ? "block"
                                                        : "none",
                                            },
                                        }}
                                        key={index}
                                        size={{
                                            width: annotation.element_width,
                                            height: annotation.element_height,
                                        }}
                                        position={{
                                            x: annotation.position_x,
                                            y: annotation.position_y,
                                        }}
                                        onDragStop={(e, d) => {
                                            const newAnnotations = [
                                                ...annotations,
                                            ];
                                            newAnnotations[index].position_x =
                                                d.x;
                                            newAnnotations[index].position_y =
                                                d.y;
                                            handleCollectData(
                                                "annotations",
                                                newAnnotations
                                            );
                                        }}
                                        onResizeStop={(
                                            e,
                                            direction,
                                            ref,
                                            delta,
                                            position
                                        ) => {
                                            const newAnnotations = [
                                                ...annotations,
                                            ];
                                            newAnnotations[
                                                index
                                            ].element_width = parseInt(
                                                ref.style.width,
                                                10
                                            );
                                            newAnnotations[
                                                index
                                            ].element_height = parseInt(
                                                ref.style.height,
                                                10
                                            );

                                            handleCollectData(
                                                "annotations",
                                                newAnnotations
                                            );
                                        }}
                                        className="annotation__signature"
                                        style={{
                                            background: backgroundColor,
                                            position: "absolute",
                                            display: "flex",
                                            alignItems: "center",
                                            border: `2px solid ${borderColor}`,
                                            cursor:
                                                focusedAnnotation ===
                                                annotation?.signer?.value
                                                    ? "move"
                                                    : "pointer",
                                        }}
                                        onMouseDown={(e) => {
                                            handleFocusAnnotation(
                                                annotation?.signer?.value
                                            );
                                        }}
                                    >
                                        <i
                                            class="fas fa-signature text-dark"
                                            style={{ fontSize: "25px" }}
                                        ></i>
                                    </Rnd>
                                )
                            );
                        })}
                        <canvas
                            ref={canvasRef}
                            style={{
                                // border: "1px solid black",
                                height: "100%",
                                width: "100%",
                            }}
                        ></canvas>
                    </div>
                </div>
            </div>
            <div
                className="p-4 signature__detail"
                style={{
                    height: "calc(100vh - 165px)",
                    maxWidth: "290px",
                    width: "100%",
                    position: "static",
                    bottom: "auto",
                }}
            >
                {focusedAnnotation && (
                    <>
                        <div className="d-flex align-items-start justify-content-between">
                            <div className="fs-15 fw-bold mb-3 text-dark">
                                Signature
                            </div>
                            <div
                                onClick={() => {
                                    handleRemoveAnnotation(focusedAnnotation);
                                }}
                                role="button"
                            >
                                <i class="feather-trash-2 text-danger fw-medium fs-26"></i>
                            </div>
                        </div>
                        <div className="mt-4">
                            <div className="fs-16 fw-medium text-dark">
                                Assigned to
                            </div>
                            <div className="mt-2">
                                {(() => {
                                    const signer = collectData.signers.find(
                                        (signer) =>
                                            signer.value === focusedAnnotation
                                    );
                                    return (
                                        <div
                                            role="button"
                                            className="d-flex align-items-center gap-3"
                                        >
                                            <div>
                                                <div
                                                    className="avatar avatar-md"
                                                    style={{
                                                        background:
                                                            signer?.email
                                                                ? generateBackgroundColor(
                                                                      signer?.email
                                                                  )?.background
                                                                : "",
                                                    }}
                                                ></div>
                                            </div>
                                            <div>
                                                <div className="fs-14 fw-bold text-dark">
                                                    {signer?.nama}
                                                </div>
                                                <div className="fs-12 text-muted">
                                                    {signer?.email}
                                                </div>
                                            </div>
                                        </div>
                                    );
                                })()}
                            </div>
                            <div className="mt-3">
                                <div className="fs-16 fw-medium text-dark mb-2">
                                    Posisi
                                </div>
                                <div className="mb-3">
                                    <label
                                        htmlFor="posisi_x"
                                        className="form-label"
                                    >
                                        Posisi X
                                    </label>
                                    <div class="input-group">
                                        <input
                                            type="number"
                                            className="form-control"
                                            id="posisi_x"
                                            name="posisi_x"
                                            value={
                                                annotations.find(
                                                    (annotation) =>
                                                        annotation?.signer
                                                            ?.value ===
                                                        focusedAnnotation
                                                )?.position_x
                                            }
                                            onChange={(e) => {
                                                const newAnnotations = [
                                                    ...annotations,
                                                ];
                                                newAnnotations.find(
                                                    (annotation) =>
                                                        annotation.signer
                                                            .value ===
                                                        focusedAnnotation
                                                ).position_x = e.target.value;
                                                handleCollectData(
                                                    "annotations",
                                                    newAnnotations
                                                );
                                            }}
                                        />
                                        <span class="input-group-text">px</span>
                                    </div>
                                </div>
                                <div className="mb-3">
                                    <label
                                        htmlFor="posisi_x"
                                        className="form-label"
                                    >
                                        Posisi Y
                                    </label>
                                    <div class="input-group">
                                        <input
                                            type="number"
                                            className="form-control"
                                            id="posisi_x"
                                            name="posisi_x"
                                            value={
                                                annotations.find(
                                                    (annotation) =>
                                                        annotation?.signer
                                                            ?.value ===
                                                        focusedAnnotation
                                                )?.position_y
                                            }
                                            onChange={(e) => {
                                                const newAnnotations = [
                                                    ...annotations,
                                                ];
                                                newAnnotations.find(
                                                    (annotation) =>
                                                        annotation.signer
                                                            .value ===
                                                        focusedAnnotation
                                                ).position_y = e.target.value;
                                                handleCollectData(
                                                    "annotations",
                                                    newAnnotations
                                                );
                                            }}
                                        />
                                        <span class="input-group-text">px</span>
                                    </div>
                                </div>
                                <div className="mb-3">
                                    <label
                                        htmlFor="element_height"
                                        className="form-label"
                                    >
                                        Tinggi
                                    </label>
                                    <div class="input-group">
                                        <input
                                            type="number"
                                            className="form-control"
                                            id="element_height"
                                            name="element_height"
                                            value={
                                                annotations.find(
                                                    (annotation) =>
                                                        annotation?.signer
                                                            ?.value ===
                                                        focusedAnnotation
                                                )?.element_height
                                            }
                                            onChange={(e) => {
                                                const newAnnotations = [
                                                    ...annotations,
                                                ];
                                                newAnnotations.find(
                                                    (annotation) =>
                                                        annotation.signer
                                                            .value ===
                                                        focusedAnnotation
                                                ).element_height =
                                                    e.target.value;
                                                handleCollectData(
                                                    "annotations",
                                                    newAnnotations
                                                );
                                            }}
                                        />
                                        <span class="input-group-text">px</span>
                                    </div>
                                </div>
                                <div className="mb-3">
                                    <label
                                        htmlFor="element_width"
                                        className="form-label"
                                    >
                                        Lebar
                                    </label>
                                    <div class="input-group">
                                        <input
                                            type="number"
                                            className="form-control"
                                            id="element_width"
                                            name="element_width"
                                            value={
                                                annotations.find(
                                                    (annotation) =>
                                                        annotation?.signer
                                                            ?.value ===
                                                        focusedAnnotation
                                                )?.element_width
                                            }
                                            onChange={(e) => {
                                                const newAnnotations = [
                                                    ...annotations,
                                                ];
                                                newAnnotations.find(
                                                    (annotation) =>
                                                        annotation.signer
                                                            .value ===
                                                        focusedAnnotation
                                                ).element_width =
                                                    e.target.value;
                                                handleCollectData(
                                                    "annotations",
                                                    newAnnotations
                                                );
                                            }}
                                        />
                                        <span class="input-group-text">px</span>
                                    </div>
                                </div>
                                <div className="mb-3">
                                    <label
                                        htmlFor="element_width"
                                        className="form-label"
                                    >
                                        Halaman
                                    </label>
                                    <div class="input-group">
                                        <input
                                            type="number"
                                            className="form-control"
                                            id="element_width"
                                            name="element_width"
                                            value={
                                                annotations.find(
                                                    (annotation) =>
                                                        annotation?.signer
                                                            ?.value ===
                                                        focusedAnnotation
                                                )?.page ?? 1
                                            }
                                            onChange={(e) => {
                                                const newAnnotations = [
                                                    ...annotations,
                                                ];

                                                const page =
                                                    (newAnnotations.find(
                                                        (annotation) =>
                                                            annotation.signer
                                                                .value ===
                                                            focusedAnnotation
                                                    ).page = e.target.value
                                                        ? parseInt(
                                                              e.target.value
                                                          )
                                                        : 1);

                                                if (page < 1) {
                                                    newAnnotations.find(
                                                        (annotation) =>
                                                            annotation.signer
                                                                .value ===
                                                            focusedAnnotation
                                                    ).page = 1;
                                                }

                                                if (page > totalPage) {
                                                    newAnnotations.find(
                                                        (annotation) =>
                                                            annotation.signer
                                                                .value ===
                                                            focusedAnnotation
                                                    ).page = totalPage;
                                                }

                                                handleCollectData(
                                                    "annotations",
                                                    newAnnotations
                                                );
                                            }}
                                        />
                                        <span class="input-group-text">px</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </>
                )}
            </div>
        </div>
    );
}
