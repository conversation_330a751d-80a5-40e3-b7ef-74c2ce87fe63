import React, { useState } from "react";
import StepOneEsign from "./stepOne";
import StepTwoEsign from "./stepTwo";
import Swal from "sweetalert2";

export default function IndexEsign({
    setEsignData,
    esignData
}) {
    const [step, setStep] = useState(1);
    const [stepActive, setStepActive] = useState([1]);
    const [loadingSubmit, setLoadingSubmit] = useState(false);
    const [collectData, setCollectData] = useState(
        Object.keys(esignData).length > 0 ? esignData :
        {
        signers: [
            {
                id: "",
                nama: "",
                email: "",
            },
        ],
        annotations: [],
    });

    const stepList = [
        {
            step: 1,
            title: "Add Signers",
            icon: "feather-user-plus",
        },
        {
            step: 2,
            title: "Set Posisi",
            icon: "feather-map-pin",
        },
        {
            step: 3,
            title: "Review & Send",
            icon: "feather-send",
        },
    ];

    const handleChangeStep = (type, toStep) => {
        if (toStep) {
            setStep(toStep);
            setStepActive(stepActive.filter((item) => item === toStep));
            return;
        }

        if (type === "next") {
            if (!validationStep()) {
                Swal.fire({
                    icon: "error",
                    title: "Gagal",
                    text: "Signer tidak boleh kosong",
                });
                return;
            }

            if (step === 1) {
                const signerFilter = collectData?.annotations?.filter((item) =>
                    collectData?.signers?.find(
                        (signer) => signer.value === item.signer?.value
                    )
                );
                collectData.annotations = signerFilter;
            }

            const newStep = step + 1;
            setStep(newStep);
            setStepActive([...stepActive, newStep]);
        } else {
            const newStep = step - 1;
            setStep(newStep);
            setStepActive(stepActive.filter((item) => item !== step));
        }
    };

    const validationStep = () => {
        if (step === 1) {
            if (
                collectData?.signers?.filter((item) => item.id !== "")
                    ?.length === 0
            )
                return false;
        }

        if (step === 2) {
            if (collectData.annotations.length !== collectData?.signers.length)
                return false;
        }

        return true;
    };

    const handleCollectData = (key, data) => {
        setCollectData({
            ...collectData,
            [key]: data,
        });
    };


    const handleSubmit = async () => {
        // "Data yang sudah dikirim tidak dapat diubah, pastikan data yang anda masukkan sudah benar"
        Swal.fire({
            title: "Apakah anda yakin?",
            text: "Akan menyimpan data yang sudah diinputkan",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Ya, Simpan!",
            cancelButtonText: "Batal",
        }).then((result) => {
            if (result.isConfirmed) {
                submitData();
            }
        });
    };

    const submitData = async () => {
        setLoadingSubmit(true);
        try {
            const annotationsFilter = collectData?.annotations?.filter((item) =>
                collectData?.signers?.find(
                    (signer) => signer.value === item.signer?.value
                )
            );
            collectData.annotations = annotationsFilter;
            setEsignData(collectData);

            Swal.fire({
                icon: "success",
                title: "Berhasil",
                text: 'Data request eSign berhasil disimpan',
            });
            $("#modalPreview").modal("hide");

        } catch (error) {
            Swal.fire({
                icon: "error",
                title: "Gagal",
                text: error?.response?.data?.message,
            });
        }
        setLoadingSubmit(false);
    };


    // const handleSubmit = async () => {
    //     Swal.fire({
    //         title: "Apakah anda yakin?",
    //         text: "Data yang sudah dikirim tidak dapat diubah, pastikan data yang anda masukkan sudah benar",
    //         icon: "warning",
    //         showCancelButton: true,
    //         confirmButtonColor: "#3085d6",
    //         cancelButtonColor: "#d33",
    //         confirmButtonText: "Ya, Kirim!",
    //         cancelButtonText: "Batal",
    //     }).then((result) => {
    //         if (result.isConfirmed) {
    //             submitData();
    //         }
    //     });
    // };

    // const submitData = async () => {
    //     setLoadingSubmit(true);
    //     try {
    //         const annotationsFilter = collectData?.annotations?.filter((item) =>
    //             collectData?.signers?.find(
    //                 (signer) => signer.value === item.signer?.value
    //             )
    //         );
    //         collectData.annotations = annotationsFilter;
    //         const { data } = await axios.post(
    //             `/surat-masuk/${data_detail?.id}/e-sign`,
    //             collectData,
    //             {
    //                 headers: {
    //                     "X-CSRF-TOKEN": csrf_token.content,
    //                 },
    //             }
    //         );

    //         if (data.status) {
    //             Swal.fire({
    //                 icon: "success",
    //                 title: "Berhasil",
    //                 text: data.message,
    //             });
    //             $("#modalPreview").modal("hide");
    //             $('#refresh-detail').click();
    //         } else {
    //             Swal.fire({
    //                 icon: "error",
    //                 title: "Tidak dapat memproses permintaan, silahkan coba lagi",
    //                 text: data.message,
    //             });
    //         }
    //     } catch (error) {
    //         Swal.fire({
    //             icon: "error",
    //             title: "Gagal",
    //             text: error?.response?.data?.message,
    //         });
    //     }
    //     setLoadingSubmit(false);
    // };

    return (
        <>
            <div className="modal-header aligm-items-center">
                <h2 className="d-flex flex-column mb-0">
                    <span className="fs-18 fw-bold mb-1">Set eSign Mekari</span>
                </h2>
                <div>
                    <div className="d-flex justify-content-center align-items-center gap-3 mb-0">
                        {stepList.map((item, index) => (
                            <div
                                key={index}
                                className="d-flex justify-content-center align-items-center gap-3"
                            >
                                <div
                                    key={index}
                                    className="d-flex align-items-center gap-2"
                                >
                                    <div className={``}>
                                        <i
                                            className={`feather ${
                                                item.icon
                                            } fs-18
                                                ${
                                                    stepActive.includes(
                                                        item.step
                                                    )
                                                        ? "text-primary"
                                                        : "text-muted"
                                                }
                                                `}
                                        ></i>
                                    </div>
                                    <div
                                        className={`
                                        fs-16
                                        ${
                                            stepActive.includes(item.step)
                                                ? "text-primary fw-bold"
                                                : "text-muted fw-normal"
                                        }
                                        `}
                                    >
                                        {item.title}
                                    </div>
                                </div>
                                {index !== stepList.length - 1 && (
                                    <div>
                                        <i className="feather-arrow-right fs-20"></i>
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                </div>
                <div className="d-flex align-items-center gap-3">
                    <a
                        href="javascript:void(0)"
                        class="avatar-text avatar-md bg-soft-danger close-icon"
                        data-bs-dismiss="modal"
                    >
                        <i class="feather-x text-danger"></i>
                    </a>
                </div>
            </div>
            <div
                class="modal-body p-0 w-100"
                style={{
                    margin: "0",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "space-between",
                }}
            >
                <div
                    className=""
                    style={{
                        maxHeight: "calc(100vh - 100px)",
                        height: "100%",
                        overflowY: "auto",
                    }}
                >
                    {(() => {
                        switch (step) {
                            case 1:
                                return (
                                    <StepOneEsign
                                        step={step}
                                        collectData={collectData}
                                        handleCollectData={handleCollectData}
                                    />
                                );
                            case 2:
                                return (
                                    <StepTwoEsign
                                        step={step}
                                        collectData={collectData}
                                        handleCollectData={handleCollectData}
                                    />
                                );
                            case 3:
                                return (
                                    <div className="px-4 py-5">
                                        <div className="row justify-content-center">
                                            <div className="col-md-6 max-auto">
                                                <div className="mb-4">
                                                    <h4 className="">
                                                        Review & Send
                                                    </h4>
                                                    <div className="mt-5">
                                                        <div className="d-flex align-items-center justify-content-between">
                                                            <h5 className="">
                                                                Signers
                                                            </h5>
                                                            <div>
                                                                <button
                                                                    className="btn btn-outline-dark rounded"
                                                                    onClick={() => {
                                                                        handleChangeStep(
                                                                            "prev",
                                                                            1
                                                                        );
                                                                    }}
                                                                >
                                                                    Edit
                                                                </button>
                                                            </div>
                                                        </div>
                                                        <div className="mt-3">
                                                            <div
                                                                className="d-flex"
                                                                style={{
                                                                    flexDirection:
                                                                        "column",
                                                                }}
                                                            >
                                                                {collectData?.signers?.map(
                                                                    (
                                                                        item,
                                                                        index
                                                                    ) => (
                                                                        <div
                                                                            key={
                                                                                index
                                                                            }
                                                                            className="d-flex align-items-start gap-3 mb-3"
                                                                        >
                                                                            <div>
                                                                                {index +
                                                                                    1}{" "}
                                                                                .
                                                                            </div>
                                                                            <div>
                                                                                <div>
                                                                                    <div className="fw-bold text-dark">
                                                                                        {
                                                                                            item.nama
                                                                                        }
                                                                                    </div>
                                                                                    <div>
                                                                                        {
                                                                                            item.email
                                                                                        }
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    )
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                );
                        }
                    })()}
                </div>
                <div className="border-top w-full px-4 pt-3 pb-4 d-flex align-items-center justify-content-end">
                    {step > 1 && (
                        <button
                            className="btn btn-outline-dark rounded me-3"
                            onClick={() => {
                                handleChangeStep("prev");
                            }}
                        >
                            Kembali
                        </button>
                    )}
                    {step < stepList.length ? (
                        <button
                            className="btn btn-primary rounded"
                            onClick={() => {
                                handleChangeStep("next");
                            }}
                        >
                            Selanjutnya
                            <i className="feather-arrow-right fs-20 ms-2"></i>
                        </button>
                    ) : (
                        <button
                            onClick={() => {
                                if (loadingSubmit) return;
                                handleSubmit();
                            }}
                            className="btn btn-primary rounded"
                            disabled={loadingSubmit}
                        >
                            {loadingSubmit ? (
                                <>
                                    <span
                                        className="spinner-border spinner-border-sm"
                                        role="status"
                                        aria-hidden="true"
                                    ></span>
                                    <span className="visually-hidden">
                                        Loading...
                                    </span>
                                </>
                            ) : (
                                <>
                                    Simpan
                                    <i className="feather-save fs-20 ms-2"></i>
                                </>
                            )}
                        </button>
                    )}
                </div>
            </div>
        </>
    );
}
