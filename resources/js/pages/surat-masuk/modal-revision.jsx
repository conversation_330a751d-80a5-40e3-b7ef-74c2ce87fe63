import React, { useState, useEffect, useRef } from "react";
export default function ModalRevision() {
    const [pdfFile, setPdfFile] = useState("");
    const [sizePdf, setSizePdf] = useState({
        width: 0,
        height: 0,
    });
    const canvasRef = useRef([]);
    const [totalPage, setTotalPage] = useState(1);
    const [pdf, setPdf] = useState(false);
    const [listRevision, setListRevision] = useState([]);
    const [revisionActive, setRevisionActive] = useState(data_detail?.id);
    const [loading, setLoading] = useState(false);

    const fetchPdfFile = async (id = null) => {
        setLoading(true);
        try {
            const response = await axios.get(
                `/surat-masuk/render-pdf/${
                    id ?? data_detail?.id
                }?download=true&revision=true`,
                {
                    responseType: "blob",
                }
            );
            setPdfFile(response.data);
        } catch (error) {
            console.error("Gagal mengambil file PDF:", error);
        }
        setLoading(false);
    };

    const loadPdf = async (pageInject) => {
        if (!pdfFile) return;

        const fileReader = new FileReader();
        fileReader.onload = async function () {
            const typedArray = new Uint8Array(this.result);
            const pdf = await pdfjsLib.getDocument(typedArray).promise;
            setPdf(pdf);
            setTotalPage(pdf.numPages);
        };

        fileReader.readAsArrayBuffer(pdfFile);
    };

    useEffect(() => {
        fetchPdfFile();
        getDataListRevision();
    }, []);

    const getDataListRevision = async () => {
        try {
            const response = await axios.get(
                `/surat-keluar/revision/${data_detail?.id}`
            );
            setListRevision(response?.data?.data?.child_surat);
        } catch (error) {
            console.error("Gagal mengambil data revisi:", error);
        }
    };

    useEffect(() => {
        if (pdfFile) loadPdf();
    }, [pdfFile]);

    useEffect(() => {
        const renderPdfCanvas = async () => {
            for (let pageNum = 1; pageNum <= totalPage; pageNum++) {
                const page = await pdf?.getPage(pageNum) ?? null;

                const scale = 1.5;
                const viewport = page.getViewport({ scale: scale });
                const canvas = canvasRef.current[pageNum - 1];
                const context = canvas.getContext("2d");

                canvas.height = viewport.height * scale;
                canvas.width = viewport.width * scale;
                context.scale(scale, scale);

                // setSizePdf({
                //     width: viewport.width,
                //     height: viewport.height,
                // });

                const renderContext = {
                    canvasContext: context,
                    viewport: viewport,
                };

                await page.render(renderContext).promise.then(function () {});
            }
        };
        renderPdfCanvas();
    }, [pdf, totalPage, revisionActive]);

    const handleRevisionActive = (id) => {
        setRevisionActive(id);
        fetchPdfFile(id);
    };

    return (
        <>
            <div className="modal-header aligm-items-center">
                <h2 className="d-flex flex-column mb-0">
                    <span className="fs-18 fw-bold mb-1">
                        History Revisi Surat
                    </span>
                </h2>
                <div className="d-flex align-items-center gap-3">
                    <a
                        href="javascript:void(0)"
                        class="avatar-text avatar-md bg-soft-danger close-icon"
                        data-bs-dismiss="modal"
                    >
                        <i class="feather-x text-danger"></i>
                    </a>
                </div>
            </div>
            <div
                class="modal-body p-0 w-100"
                style={{
                    margin: "0",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "space-between",
                }}
            >
                <div className="d-flex" style={{ height: "100vh" }}>
                    <div
                        className="boxEsign"
                        style={{
                            background: "#f2f4f7",
                            display: "flex",
                            flexDirection: "column",
                            width: "100%",
                            position: "relative",
                        }}
                    >
                        <div
                            style={{
                                paddingLeft: "85px",
                                paddingRight: "77px",
                                paddingBottom: "100px",
                                marginTop: "10px",
                                paddingTop: "20px",
                                width: "100%",
                                height: "auto",
                                overflowX: "auto",
                                overflowY: "auto",
                            }}
                        >
                            {Array.from({ length: totalPage }).map(
                                (_, index) => (
                                    <div>
                                        <div
                                            key={index}
                                            style={{
                                                position: "relative",
                                                overflow: "hidden",
                                                boxShadow:
                                                    "0 0 10px 0 rgba(0, 0, 0, 0.1)",
                                            }}
                                        >
                                            <canvas
                                                ref={(el) =>
                                                    (canvasRef.current[index] =
                                                        el)
                                                }
                                                style={{
                                                    width: "100%",
                                                    height: "auto",
                                                    display: "block",
                                                }}
                                            ></canvas>
                                        </div>
                                        <div
                                            className="text-end mt-2 fw-bold text-dark"
                                            style={{
                                                marginBottom: "20px",
                                            }}
                                        >
                                            <span className="text-muted">
                                                Halaman {index + 1} dari{" "}
                                                {totalPage}
                                            </span>
                                        </div>
                                    </div>
                                )
                            )}
                        </div>
                    </div>
                    <div
                        className="py-4 px-2 signature__detail"
                        style={{
                            height: "100%",
                            maxHeight: "calc(100vh - 80px)",
                            maxWidth: "300px",
                            width: "100%",
                            position: "static",
                            bottom: "auto",
                            overflowY: "auto",
                        }}
                    >
                        {listRevision?.map((item, index) => (
                            <div
                                className={`mb-2 list_revision px-3 py-2 ${
                                    revisionActive === item.id
                                        ? "bg-body rounded"
                                        : ""
                                }`}
                                key={index}
                                role="button"
                                onClick={() => {
                                    if (revisionActive === item.id) return;
                                    handleRevisionActive(item?.id);
                                }}
                            >
                                <div class="text-primary fw-bold">
                                    <i class="feather-edit-2 me-2 fs-11"></i>
                                    Revisi
                                </div>
                                <div class="fs-13 fw-medium text-dark">
                                    {item?.date_formatted}
                                </div>
                                <div class="fs-15 fw-bold text-dark">
                                    {item.no_surat}
                                </div>
                                <div class="fs-13 text-muted">
                                    Revisi Oleh : {item?.pengirim?.nama}
                                </div>
                            </div>
                        ))}
                        <div
                            className={`mb-2 list_revision px-3 py-2 ${
                                revisionActive === data_detail.id
                                    ? "bg-body rounded"
                                    : ""
                            }`}
                            role="button"
                            onClick={() => {
                                if (revisionActive === data_detail.id) return;
                                handleRevisionActive(data_detail?.id);
                            }}
                        >
                            <div class="text-primary fw-bold">
                                <i class="feather-file-text me-2 fs-11"></i>
                                Surat Asli
                            </div>
                            <div class="fs-13 fw-medium text-dark">
                                {data_detail?.date_formatted}
                            </div>
                            <div class="fs-15 fw-bold text-dark">
                                {data_detail.no_surat}
                            </div>
                            <div class="fs-13 text-muted">
                                Oleh : {data_detail?.pengirim?.nama}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {loading && (
                <div className="modal-backdrop show">
                    <div
                        id="example_processing"
                        className="dataTables_processing card"
                    >
                        <div>
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
}
