import axios from "axios";
import html2canvas from "html2canvas";
import React, { useState, useEffect, useRef } from "react";
import Swal from "sweetalert2";
import Signer from "./signer";
import ReactDOM from "react-dom";

export default function ModalSignerEsignStep({
    data,
    signature,
    setSignature,
    collectData,
    handleCollectData
}) {
    const collectSignatures = collectData.signatures ?? {};
    const documentActive = collectData.documentActive ?? {};
    const signaturesDocument = collectSignatures[documentActive?.id] ?? {}
    
    const csrf_token = document.querySelector('meta[name="csrf-token"]');
    const [activeTab, setActiveTab] = useState(1);
    const listTabs = [
        {
            type: 1,
            name: "Upload",
        },
        {
            type: 2,
            name: "Barcode",
        },
    ];

    const [image, setImage] = useState(null);
    const [fullName, setFullName] = useState(auth_user?.nama);

    const handleFileChange = (event) => {
        const file = event.target.files[0];
        if (file && ["image/jpeg", "image/png"].includes(file.type)) {
            const reader = new FileReader();
            reader.onload = () => setImage(reader.result);
            reader.readAsDataURL(file);
        } else {
            alert("Hanya file JPG atau PNG yang diperbolehkan!");
        }
    };

    const handleRemoveImage = () => setImage(null);

    const handleSave = async () => {
        switch (activeTab) {
            case 1:
                if (!image) {
                    Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text: "Gambar tanda tangan tidak boleh kosong!",
                    });
                    return;
                }
                break;
            case 2:
                if (!fullName) {
                    Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text: "Nama lengkap tidak boleh kosong!",
                    });
                    return;
                }
                break;
        }

        const container = document.createElement("div");
        document.body.appendChild(container);

        data.type = activeTab;
        data.fullName = fullName;
        data.signature = image;
        ReactDOM.render(<Signer data={data} />, container);

        const canvas = await html2canvas(container, { backgroundColor: null });
        console.log('canvas', canvas);
        const base64Image = canvas.toDataURL("image/png");
        const imageWidth = data.element_width * 1.8;
        const imageHeight = data.element_height * data.scaleY;

        document.body.removeChild(container);
        
        collectSignatures[documentActive?.id] = {
            ...signaturesDocument,
            [data.id]: {
                type: activeTab,
                value_type: activeTab === 1 ? image : fullName,
                signature: base64Image,
                width: imageWidth,
                height: imageHeight,
            },
        }

        console.log('collectSignatures', collectSignatures);

        handleCollectData('signatures', collectSignatures);
        
        setSignature({
            ...signature,
            [data.id]: base64Image,
        });

        $('#modalSigner').modal('hide');
    }

    return (
        <>
            <div className="modal-header aligm-items-center">
                <div className="fs-14 fw-bold text-dark">Tanda Tangan</div>
                <div className="d-flex align-items-center gap-3">
                    <a
                        href="javascript:void(0)"
                        class="avatar-text avatar-md bg-soft-danger close-icon"
                        data-bs-dismiss="modal"
                    >
                        <i class="feather-x text-danger"></i>
                    </a>
                </div>
            </div>
            <div class="modal-body p-0">
                <ul class="nav nav-underline border-bottom sign-tabs gap-4">
                    {listTabs.map((tab, index) => (
                        <li
                            class={`nav-item ${
                                tab.type == activeTab
                                    ? "border-bottom border-primary border-2"
                                    : ""
                            }`}
                            key={index}
                        >
                            <button
                                onClick={() => setActiveTab(tab.type)}
                                class={`nav-link ${
                                    tab.type === activeTab
                                        ? "active fw-bold"
                                        : ""
                                } text-dark`}
                                id={`tab-${tab.type}`}
                                data-bs-toggle="tab"
                                href={`#tab-${tab.type}`}
                                role="tab"
                                aria-controls={`tab-${tab.type}`}
                                aria-selected={
                                    tab.type === activeTab ? "true" : "false"
                                }
                            >
                                {tab.name}
                            </button>
                        </li>
                    ))}
                </ul>
                <div className="p-4">
                    {(() => {
                        switch (activeTab) {
                            case 1:
                                return (
                                    <div className="">
                                        <label className="form-label">
                                            Upload Gambar
                                        </label>

                                        {image ? (
                                            <div className="position-relative text-center">
                                                <img
                                                    src={image}
                                                    alt="Preview"
                                                    className="img-thumbnail w-100"
                                                />
                                                <button
                                                    className="btn btn-danger position-absolute top-0 end-0 m-2 p-2 rounded-full"
                                                    onClick={handleRemoveImage}
                                                >
                                                    <i className="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        ) : (
                                            <input
                                                type="file"
                                                className="form-control"
                                                accept="image/png, image/jpeg"
                                                onChange={handleFileChange}
                                            />
                                        )}
                                    </div>
                                );
                            case 2:
                                return (
                                    <div className="">
                                        <div className="">
                                            <label className="form-label">Nama Lengkap</label>
                                            <input
                                                type="text"
                                                className="form-control"
                                                placeholder="Masukkan nama lengkap"
                                                value={fullName}
                                                onChange={(e) =>
                                                    setFullName(e.target.value)
                                                }
                                            />
                                        </div>
                                        <div>
                                            <div className="d-flex align-items-center gap-4 mt-5">
                                                <img src="/assets/qrcode.svg" alt="Barcode" className="" style={{
                                                    width: "120px",
                                                    height: "120px",
                                                }} />
                                                <div>
                                                    <div class="fs-18 fw-bold text-dark">
                                                        {fullName.toUpperCase()}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                );
                        }
                    })()}
                </div>
            </div>
            <div class="modal-footer d-flex align-items-center justify-content-between gap-3 w-100">
                <button
                    type="button"
                    class="btn btn-outline-secondary"
                    data-bs-dismiss="modal"
                >
                    Batal
                </button>
                <button class="btn btn-primary" onClick={handleSave}>Simpan</button>
            </div>
        </>
    );
}
