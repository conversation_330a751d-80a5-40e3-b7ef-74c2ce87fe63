import React, { useRef, useState, useEffect } from "react";
import ModalSignerEsignStep from "./modal-signer";
import Modal from "../../../../components/modal";
import ReactDOM from "react-dom/client";

export default function StepThreeEsignManual({
    step,
    collectData,
    handleCollectData,
}) {
    const annotations = collectData?.annotations ?? [];
    const documentActive = collectData.documentActive ?? {};
    const annotationDocument = annotations[documentActive.id] ?? [];
    const collectSignatures = collectData.signatures ?? {};
    const signaturesDocument = collectSignatures[documentActive?.id] ?? {};

    const [pdfFile, setPdfFile] = useState("");
    const [sizePdf, setSizePdf] = useState({
        width: 0,
        height: 0,
    });
    const canvasRef = useRef([]);
    const imageRef = useRef([]);
    const [totalPage, setTotalPage] = useState(1);
    const [pdf, setPdf] = useState(false);
    const [loading, setLoading] = useState(false);
    const [annotationActive, setAnnotationActive] = useState(null);
    const [signature, setSignature] = useState(signaturesDocument);

    const fetchPdfFile = async (id = null) => {
        setLoading(true);
        try {
            const response = await axios.get(documentActive.file_path, {
                responseType: "blob",
            });
            setPdfFile(response.data);
        } catch (error) {
            console.error("Gagal mengambil file PDF:", error);
        }
        setLoading(false);
    };

    const loadPdf = async (pageInject) => {
        if (!pdfFile) return;

        const fileReader = new FileReader();
        fileReader.onload = async function () {
            const typedArray = new Uint8Array(this.result);
            const pdf = await pdfjsLib.getDocument(typedArray).promise;
            setPdf(pdf);
            setTotalPage(pdf.numPages);
        };

        fileReader.readAsArrayBuffer(pdfFile);
    };

    useEffect(() => {
        fetchPdfFile();
    }, []);

    useEffect(() => {
        if (pdfFile) loadPdf();
    }, [pdfFile]);

    useEffect(() => {
        const renderPdfCanvas = async () => {
            for (let pageNum = 1; pageNum <= totalPage; pageNum++) {
                const page = (await pdf?.getPage(pageNum)) ?? null;

                const scale = 1.5;
                const viewport = page.getViewport({ scale: scale });
                const canvas = canvasRef.current[pageNum - 1];
                const context = canvas.getContext("2d");

                canvas.height = viewport.height * scale;
                canvas.width = viewport.width * scale;
                context.scale(scale, scale);

                const renderContext = {
                    canvasContext: context,
                    viewport: viewport,
                };

                await page.render(renderContext).promise.then(() => {
                    data_detail.tanda_tangan_surat
                        .filter(
                            (item) => item.referable_id === documentActive?.id
                        )
                        .forEach((item) => {
                            if (parseInt(item.page) === (pageNum ?? 1)) {
                                const img = new Image();
                                img.src = item?.image;
                                img.onload = async () => {
                                    if (canvas) {
                                        const scaleX = viewport.width / item.canvas_width;
                                        const scaleY = viewport.height / item.canvas_height;
                                        const autoWidth = item.height * (img.width / img.height) * 1.05;
                                        context.drawImage(
                                            img,
                                            item.posisi_x * scaleX,
                                            item.posisi_y * scaleY,
                                            autoWidth,
                                            item.height
                                        );
                                    }
                                };
                            }
                        });

                    setSizePdf({
                        width: canvas.clientWidth,
                        height: canvas.clientHeight,
                    });
                    setLoading(false);
                });
            }
        };
        renderPdfCanvas();
    }, [pdf, totalPage]);

    let root = null;
    const handleOpenSign = (data) => {
        let modalElement = document.getElementById("contentModalSigners");

        if (!modalElement) {
            $("body").append(
                Modal(
                    "z-important",
                    "modalSigner",
                    " modal-md",
                    "",
                    "contentModalSigners",
                    "",
                    ""
                )
            );
            modalElement = document.getElementById("contentModalSigners");
        }

        if (!root) {
            root = ReactDOM.createRoot(modalElement);
        }

        root.render(
            <ModalSignerEsignStep
                data={data}
                signature={signature}
                setSignature={setSignature}
                collectData={collectData}
                handleCollectData={handleCollectData}
            />
        );

        $("#modalSigner").modal("show");
    };

    const handleCancelSign = (id) => {
        const newSignature = {
            ...signature,
        };
        delete newSignature[`${id}`];
        setSignature(newSignature);

        if(Object.keys(collectSignatures[documentActive.id]).length === 1) {
            delete collectSignatures[documentActive.id];
        } else {
            delete collectSignatures[documentActive.id][`${id}`];
        }

        handleCollectData("signatures", collectSignatures);
    };

    return (
        <div className="" style={{ background: "#f1f4f7" }}>
            <div
                className="boxEsign boxEsign--stepThree"
                style={{
                    background: "#f2f4f7",
                    display: "flex",
                    flexDirection: "column",
                    position: "relative",
                    margin: "auto",
                    padding: "30px 0px",
                }}
            >
                {loading ? (
                    <div className="m-auto">
                        <div
                            className="spinner-border"
                            role="status"
                            style={{ height: "50px", width: "50px" }}
                        >
                            <span className="visually-hidden">Loading...</span>
                        </div>
                    </div>
                ) : (
                    <div
                        style={{
                            width: "100%",
                            height: "auto",
                        }}
                    >
                        {Array.from({ length: totalPage }).map((_, index) => (
                            <div key={index}>
                                <div className="fs-11 py-2">
                                    Dokumen ID: {documentActive.id}
                                </div>
                                <div
                                    key={index}
                                    style={{
                                        position: "relative",
                                        overflow: "hidden",
                                        boxShadow:
                                            "0 4px 6px -1px rgba(0, 0, 0, 0.1),0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                                    }}
                                >
                                    <canvas
                                        ref={(el) =>
                                            (canvasRef.current[index] = el)
                                        }
                                        style={{
                                            width: "100%",
                                            height: "auto",
                                            display: "block",
                                        }}
                                    ></canvas>
                                    {annotationDocument
                                        .filter(
                                            (item) => item.page == index + 1
                                        )
                                        .map((annotation, idx) => {
                                            const canvas =
                                                canvasRef.current[index];
                                            const scaleX = canvas
                                                ? canvas.clientWidth /
                                                  annotation.canvas_width
                                                : 1;
                                            const scaleY = canvas
                                                ? canvas.clientHeight /
                                                  annotation.canvas_height
                                                : 1;
                                            const signatureAnnotation =
                                                signature[`${annotation.id}`];
                                            return (
                                                <div key={idx}>
                                                    {!signatureAnnotation && (
                                                        <div
                                                            id={`annotation-${
                                                                idx + 1
                                                            }`}
                                                            role="button"
                                                            style={{
                                                                position:
                                                                    "absolute",
                                                                top:
                                                                    annotation.position_y *
                                                                    scaleY,
                                                                left:
                                                                    annotation.position_x *
                                                                        scaleX -
                                                                    140,
                                                                width: `150px`,
                                                                height: `${
                                                                    annotation.element_height *
                                                                    scaleY
                                                                }px`,
                                                                zIndex: 1,
                                                            }}
                                                            className="d-flex align-items-center p-2"
                                                        >
                                                            <div className="position-relative">
                                                                <div
                                                                    className="fs-12 text-white px-3 py-2 fw-bold"
                                                                    style={{
                                                                        background:
                                                                            "#28a745",
                                                                    }}
                                                                >
                                                                    Tanda Tangan
                                                                </div>
                                                                <div
                                                                    style={{
                                                                        position:
                                                                            "absolute",
                                                                        top: "50%",
                                                                        right: "-17px",
                                                                        transform:
                                                                            "translateY(-50%)",
                                                                        width: "0",
                                                                        height: "0",
                                                                        borderTop:
                                                                            "17px solid transparent",
                                                                        borderBottom:
                                                                            "17px solid transparent",
                                                                        borderLeft:
                                                                            "17px solid #16c666",
                                                                        zIndex: 2,
                                                                    }}
                                                                ></div>
                                                            </div>
                                                        </div>
                                                    )}

                                                    <div
                                                        role="button"
                                                        style={{
                                                            position:
                                                                "absolute",
                                                            top:
                                                                annotation.position_y *
                                                                scaleY,
                                                            left:
                                                                annotation.position_x *
                                                                scaleX,
                                                            width:
                                                                annotation.element_width *
                                                                scaleX,
                                                            height:
                                                                annotation.element_height *
                                                                scaleY,
                                                        }}
                                                        className=""
                                                    >
                                                        {signatureAnnotation ? (
                                                            <div>
                                                                <img
                                                                    ref={(el) =>
                                                                        (imageRef.current[
                                                                            annotation.id
                                                                        ] = el)
                                                                    }
                                                                    style={{
                                                                        height:
                                                                            annotation.element_height *
                                                                            scaleY,
                                                                        maxWidth:
                                                                            "fit-content",
                                                                    }}
                                                                    src={
                                                                        signatureAnnotation?.signature ??
                                                                        signatureAnnotation
                                                                    }
                                                                />
                                                                <button
                                                                    onClick={() => {
                                                                        handleCancelSign(
                                                                            annotation.id
                                                                        );
                                                                    }}
                                                                    style={{
                                                                        position:
                                                                            "absolute",
                                                                        top: "-5px",
                                                                        right: "-20px",
                                                                        zIndex: 1,
                                                                        height: "20px",
                                                                        width: "20px",
                                                                        padding:
                                                                            "0",
                                                                    }}
                                                                    className="btn bg-white border rounded-full shadow-sm"
                                                                >
                                                                    <i
                                                                        style={{
                                                                            fontSize:
                                                                                "10px",
                                                                        }}
                                                                        className="fas fa-times text-dark"
                                                                    ></i>
                                                                </button>
                                                            </div>
                                                        ) : (
                                                            <div
                                                                onClick={() => {
                                                                    handleOpenSign(
                                                                        {
                                                                            ...annotation,
                                                                            scaleX,
                                                                            scaleY,
                                                                        }
                                                                    );
                                                                }}
                                                                className="fs-12 text-black d-flex p-2 align-items-center"
                                                                style={{
                                                                    height:
                                                                        annotation.element_height *
                                                                        scaleY,
                                                                    background:
                                                                        "rgba(221, 243, 226, 0.75)",
                                                                    boxShadow:
                                                                        "rgb(60, 145, 77) 0px 0px 0px 1px inset",
                                                                }}
                                                            >
                                                                Klik disini
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            );
                                        })}
                                </div>
                                <div
                                    className="d-flex align-items-center mt-2 justify-content-between"
                                    style={{
                                        marginBottom: "20px",
                                    }}
                                >
                                    <div className="text-end fs-12 text-dark">
                                        <span className="text-muted">
                                            {/* {documentActive.document_name} */}
                                        </span>
                                    </div>
                                    <div className="text-end fs-12 text-dark">
                                        <span className="text-muted">
                                            Halaman {index + 1} dari {totalPage}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
}
