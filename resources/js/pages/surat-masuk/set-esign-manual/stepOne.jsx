import React from "react";

export default function StepOneEsignManual({
    validationFinish,
    handleChangeStep,
    collectData,
}) {
    const signatures = collectData.signatures ?? {};
    const annotations = collectData.annotations ?? [];
    const attachmentRequiredSign = data_detail?.attachment_file_surat?.filter(
        (item) => parseInt(item.required_signature) === 1
    );

    const isSignedSurat =
        Object.keys(signatures[data_detail.id] ?? {}).length ===
        annotations?.[data_detail.id]?.length;

    return (
        <div className="px-4 py-5">
            <div className="row justify-content-center">
                <div className="col-md-6 max-auto">
                    <div className="mb-4">
                        <div className="mb-5">
                            <h4 className="fw-bold fs-22 text-dark">
                                <i className="fas fa-file-alt fs-20 pe-2"></i>
                                <PERSON><PERSON><PERSON>
                            </h4>
                            <div className="text-muted fs-14 mt-1">
                                <PERSON>lih dokumen yang akan ditandatangani, pastikan
                                semua dokumen yang diperlukan sudah terpilih.
                            </div>
                        </div>
                        <div className="mt-5">
                            <div
                                className="d-flex"
                                style={{
                                    flexDirection: "column",
                                    gap: "1rem",
                                }}
                            >
                                <div className="d-md-flex align-items-center justify-content-between gap-4 fs-16">
                                    <div
                                        className={`d-flex align-items-start gap-3 ${
                                            isSignedSurat
                                                ? "text-success"
                                                : "text-dark"
                                        }`}
                                    >
                                        <div>1.</div>
                                        <div className="fw-semibold">
                                            Surat No {data_detail.no_surat}
                                            {isSignedSurat && (
                                                <i className="fas fa-check-circle text-success ms-2"></i>
                                            )}
                                        </div>
                                    </div>
                                    <div className="ms-auto">
                                        <button
                                            className={`btn btn-outline-${
                                                isSignedSurat
                                                    ? "success"
                                                    : "primary"
                                            }`}
                                            onClick={() => {
                                                handleChangeStep("next", null, {
                                                    id: data_detail.id,
                                                    file_path: `/surat-masuk/render-pdf/${data_detail?.id}?download=true`,
                                                    type: "surat",
                                                });
                                            }}
                                        >
                                            <i className="fas fa-signature me-2"></i>{" "}
                                            {isSignedSurat
                                                ? "Ubah"
                                                : "Tanda Tangan"}
                                        </button>
                                    </div>
                                </div>
                                {attachmentRequiredSign?.map((item, index) => {
                                    const isSigned =
                                        Object.keys(signatures[item.id] ?? {})
                                            .length ===
                                        annotations?.[item.id]?.length;
                                    return (
                                        <div className="d-md-flex align-items-center justify-content-between gap-4 fs-16">
                                            <div
                                                className={`d-flex align-items-start gap-3 ${
                                                    isSigned
                                                        ? "text-success"
                                                        : "text-dark"
                                                }`}
                                            >
                                                <div>{index + 2}.</div>
                                                <div className={`fw-semibold`}>
                                                    Lampiran{" "}
                                                    {item.nama_original}
                                                    {isSigned && (
                                                        <i className="fas fa-check-circle text-success ms-2"></i>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="">
                                                <button
                                                    className={`btn btn-outline-${
                                                        isSigned
                                                            ? "success"
                                                            : "primary"
                                                    }`}
                                                    style={{
                                                        whiteSpace: "nowrap",
                                                    }}
                                                    onClick={() => {
                                                        handleChangeStep(
                                                            "next",
                                                            null,
                                                            {
                                                                id: item.id,
                                                                file_path:
                                                                    item.file_convert,
                                                                type: "lampiran",
                                                            }
                                                        );
                                                    }}
                                                >
                                                    <i className="fas fa-signature me-2"></i>{" "}
                                                    {isSigned
                                                        ? "Ubah"
                                                        : "Tanda Tangan"}
                                                </button>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                        <div className="d-flex align-items-cnter justify-content-center gap-4 pt-5">
                            <button
                                className="btn btn-outline-dark rounded"
                                onClick={() => {
                                    handleChangeStep("prev");
                                }}
                            >
                                <i className="fas fa-arrow-left me-2"></i>
                                Kembali
                            </button>
                            {Object.keys(signatures).length > 0 && (
                                <button
                                    onClick={() => {
                                        const validation = validationFinish()
                                        if (!validation) return
                                        handleChangeStep(null, 5)
                                    }}
                                    className="btn btn-primary"
                                >
                                    <i className="fas fa-save me-2"></i>
                                    Simpan & Review
                                </button>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
