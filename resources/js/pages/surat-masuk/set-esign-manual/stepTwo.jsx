import axios from "axios";
import React, { useEffect, useRef, useState } from "react";
import { Rnd } from "react-rnd";
import Select from "react-select";

export default function StepTwoEsignManual({
    step,
    collectData,
    handleCollectData,
}) {
    const annotations = collectData.annotations ?? [];
    const documentActive = collectData.documentActive ?? {};
    const annotationDocument = annotations[documentActive.id] ?? [];
    const [pdfFile, setPdfFile] = useState("");
    const [sizePdf, setSizePdf] = useState({
        width: 0,
        height: 0,
    });
    const canvasRef = useRef(null);
    const [pagePdf, setPagePdf] = useState(1);
    const [totalPage, setTotalPage] = useState(1);
    const boxSignerRef = useRef(null);
    const [focusedAnnotation, setFocusedAnnotation] = useState(null);

    const fetchPdfFile = async () => {
        try {
            const response = await axios.get(documentActive.file_path, {
                responseType: "blob",
            });
            setPdfFile(response.data);
        } catch (error) {
            console.error("Gagal mengambil file PDF:", error);
        }
    };

    const generateBackgroundColor = (email) => {
        let hash = 0;
        for (let i = 0; i < email.length; i++) {
            hash = email.charCodeAt(i) + ((hash << 5) - hash);
        }
        const color = (hash & 0x00ffffff).toString(16).toUpperCase();
        return {
            background: `#${"00000".substring(0, 6 - color.length) + color}80`,
            border: `#${color}`,
        };
    };

    const [signers] = useState([
        {
            value: auth_user.id,
            label: auth_user.nama,
            email: auth_user.email,
            color: generateBackgroundColor(auth_user.email)?.background,
            borderColor: generateBackgroundColor(auth_user.email)?.border,
        },
    ]);
    const [signerSelected, setSignerSelected] = useState(signers[0]);

    const renderPDFToCanvas = async (pageInject) => {
        if (!pdfFile) return;

        const fileReader = new FileReader();
        fileReader.onload = async function () {
            const typedArray = new Uint8Array(this.result);
            const pdf = await pdfjsLib.getDocument(typedArray).promise;
            setTotalPage(pdf.numPages);
            const page = await pdf.getPage(pageInject ?? pagePdf);

            const scale = 1.5;
            const viewport = page.getViewport({ scale: scale });
            const canvas = canvasRef.current;
            const context = canvas.getContext("2d");

            canvas.height = viewport.height * scale;
            canvas.width = viewport.width * scale;
            context.scale(scale, scale);

            setSizePdf({
                width: viewport.width,
                height: viewport.height,
            });

            const renderContext = {
                canvasContext: context,
                viewport: viewport,
            };

            await page.render(renderContext).promise.then(function () {
                data_detail.tanda_tangan_surat
                    .filter((item) => item.referable_id === documentActive?.id)
                    .forEach((item) => {
                        if (parseInt(item.page) === (pageInject ?? 1)) {
                            const img = new Image();
                            img.src = item?.image;
                            img.onload = async () => {
                                if (canvas) {
                                    const scaleX = viewport.width / item.canvas_width;
                                    const scaleY = viewport.height / item.canvas_height;
                                    const autoWidth = item.height * (img.width / img.height) * 1.05;
                                    context.drawImage(
                                        img,
                                        item.posisi_x * scaleX,
                                        item.posisi_y * scaleY,
                                        autoWidth,
                                        item.height * scaleY
                                    );
                                }
                            };
                        }
                    });
            });
        };

        fileReader.readAsArrayBuffer(pdfFile);
    };

    useEffect(() => {
        fetchPdfFile();
    }, []);

    useEffect(() => {
        renderPDFToCanvas();
    }, [pdfFile]);

    const handleGetPage = (type) => {
        if (totalPage === 1) return;
        let page = pagePdf;
        if (type === "prev") {
            if (pagePdf === 1) return;
            page = pagePdf - 1;
            setPagePdf(page);
        } else {
            if (pagePdf === totalPage) return;
            page = pagePdf + 1;
            setPagePdf(page);
        }

        renderPDFToCanvas(page);
    };

    const addSignatureToCanvas = (signer) => {
        // setFocusedAnnotation(signer.value);
        // if (annotations.some((annotation) => annotation.signer === signer))
        //     return;

        const canvasWidth = boxSignerRef?.current?.clientWidth ?? 0;
        const canvasHeight = boxSignerRef?.current?.clientHeight ?? 0;
        const elementWidth = canvasWidth * 0.1; // 10% of canvas width
        const elementHeight = canvasHeight * 0.05; // 5% of canvas height

        const documentAnnotations = annotations[documentActive.id] || [];

        const newAnnotation = {
            id: documentAnnotations.length + 1,
            signer,
            position_x: 0,
            position_y: 0,
            element_width: elementWidth,
            element_height: elementHeight,
            canvas_width: canvasWidth,
            canvas_height: canvasHeight,
            page: pagePdf,
            document: documentActive,
        };

        annotations[documentActive.id] = [
            ...documentAnnotations,
            newAnnotation,
        ];

        handleCollectData("annotations", annotations);
    };

    const scrollToAnnotation = (id) => {
        const element = document.getElementById(`signature_${id}`);
        if (element) {
            element.scrollIntoView({ behavior: "smooth", block: "center" });
        }
    };

    const handleFocusAnnotation = (id) => {
        setFocusedAnnotation(id);
        scrollToAnnotation(id);
    };

    const handleRemoveAnnotation = () => {
        const newAnnotations = annotationDocument.filter(
            (annotation) => annotation.id !== focusedAnnotation
        );
        annotations[documentActive.id] = newAnnotations;
        handleCollectData("annotations", annotations);
        setFocusedAnnotation(null);
    };

    const handleClickOutside = (event) => {
        if (
            event.target.closest(".boxEsign") &&
            !event.target.closest(".annotation__signature")
        ) {
            setFocusedAnnotation(null);
        }
    };

    useEffect(() => {
        document.addEventListener("click", handleClickOutside, true);
        return () => {
            document.removeEventListener("click", handleClickOutside, true);
        };
    }, []);

    return (
        <div className="d-md-flex">
            <div className="p-4 box_signature_left">
                <div className="fs-14 fw-bold mb-3 text-dark">Signers</div>
                <div
                    className="mt-4 d-flex gap-1"
                    style={{ flexDirection: "column" }}
                >
                    <Select
                        options={signers}
                        placeholder=""
                        defaultValue={signerSelected}
                        styles={{
                            control: (baseStyles) => ({
                                ...baseStyles,
                                borderColor: "#e5e7eb",
                                boxShadow: "none",
                                padding: "4px 6px",
                                borderRadius: "5px",
                            }),
                            option: (baseStyles, state) => ({
                                ...baseStyles,
                                backgroundColor: state.isSelected
                                    ? "#EDF2F7"
                                    : "#fff",
                                color: "#2D3748",
                                "&:hover": {
                                    backgroundColor: "#EDF2F7",
                                },
                                "&:active": {
                                    backgroundColor: "#EDF2F7",
                                },
                                '&[data-selected="true"]': {
                                    backgroundColor: "#EDF2F7",
                                },
                            }),
                            menu: (baseStyles) => ({
                                ...baseStyles,
                                border: "1px solid #e5e7eb",
                                boxShadow: "0 1px 2px 0 rgba(0, 0, 0, 0.10)",
                                zIndex: 9999,
                            }),
                        }}
                        formatOptionLabel={(option, { context }) => {
                            if (context === "value") {
                                return (
                                    <div className="d-flex align-items-center gap-3">
                                        <div>
                                            <div
                                                className="avatar avatar-md"
                                                style={{
                                                    background: option.color,
                                                }}
                                            ></div>
                                        </div>
                                        <div className="fs-12">
                                            {option.label}
                                        </div>
                                    </div>
                                );
                            } else {
                                return (
                                    <div className="d-flex align-items-center gap-3">
                                        <div>
                                            <div
                                                className="avatar avatar-md"
                                                style={{
                                                    background: option.color,
                                                }}
                                            ></div>
                                        </div>
                                        <div className="fs-12">
                                            {option.label}
                                        </div>
                                    </div>
                                );
                            }
                        }}
                        onChange={async (value) => {
                            setSignerSelected(value);
                        }}
                    />
                </div>
                <div className="mt-5">
                    <button
                        className="btn w-100 p-3 fs-10"
                        style={{
                            background: "#ffe1c6",
                            color: "#2d3748",
                            border: "1px solid #2d3748",
                        }}
                        onClick={() => {
                            addSignatureToCanvas(signerSelected);
                        }}
                    >
                        <i className="fas fa-signature"></i>
                        Tambahkan Tanda Tangan
                    </button>
                </div>
                <div
                    className="mt-4 d-flex gap-1"
                    style={{ flexDirection: "column" }}
                >
                    {annotations[documentActive.id] &&
                        annotations[documentActive.id]?.map(
                            (annotation, index) => {
                                return (
                                    <div
                                        role="button"
                                        className={`d-flex align-items-center gap-3 p-2 rounded-3 cursor-pointer
                                    text-truncate
                                    ${
                                        focusedAnnotation === annotation.id
                                            ? "bg-body"
                                            : ""
                                    }
                                `}
                                        key={index}
                                        onClick={() => {
                                            setPagePdf(annotation?.page);
                                            renderPDFToCanvas(annotation?.page);
                                            handleFocusAnnotation(
                                                annotation.id
                                            );
                                        }}
                                    >
                                        <div>
                                            <div
                                                className="avatar avatar-md"
                                                style={{
                                                    background:
                                                        annotation.signer.color,
                                                }}
                                            ></div>
                                        </div>
                                        <div>
                                            <div className="fs-14 fw-bold text-dark">
                                                {annotation.signer?.label}
                                            </div>
                                            {annotation.page && (
                                                <div className="fs-12 text-dark">
                                                    Page:{" "}
                                                    {annotation?.page ?? 1}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                );
                            }
                        )}
                </div>
            </div>
            <div className="boxEsign">
                <div className="boxEsign__item">
                    <div
                        className="mb-3"
                        style={{
                            width: "100%",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                        }}
                    >
                        <div
                            className="d-flex align-items-center gap-3 justify-content-center py-2"
                            style={{
                                background: "white",
                                width: "200px",
                                borderRadius: "5px",
                                // boxShadow: "0 30px 40px 0 rgba(16, 36, 94, .2)",
                                border: "1px solid #e5e5e5",
                            }}
                        >
                            <div
                                role="button"
                                className="py-1 px-3"
                                disabled={pagePdf === 1}
                                onClick={() => handleGetPage("prev")}
                            >
                                <i class="fas fa-angle-left"></i>
                            </div>
                            <div style={{ fontSize: "14px", color: "black" }}>
                                <b>{pagePdf}</b> of {totalPage}
                            </div>
                            <div
                                role="button"
                                className="py-1 px-3"
                                disabled={pagePdf === totalPage}
                                onClick={() => handleGetPage("next")}
                            >
                                <i class="fas fa-angle-right"></i>
                            </div>
                        </div>
                    </div>
                    <div
                        ref={boxSignerRef}
                        style={{
                            position: "relative",
                            overflowY: "hidden",
                            overflowX: "hidden",
                            boxShadow: "0 0 10px 0 rgba(0, 0, 0, 0.1)",
                        }}
                    >
                        {annotations[documentActive.id] &&
                            annotationDocument.map((annotation, index) => {
                                const backgroundColor = annotation.signer.color;
                                const borderColor =
                                    annotation.signer.borderColor;
                                return (
                                    annotation.page === pagePdf && (
                                        <Rnd
                                            id={`signature_${annotation.id}`}
                                            resizeHandleStyles={{
                                                bottomRight: {
                                                    backgroundColor: "white",
                                                    border: `2px solid ${borderColor}`,
                                                    cursor: "se-resize",
                                                    width: "10px",
                                                    height: "10px",
                                                    borderRadius: "50%",
                                                    bottom: "-6px",
                                                    right: "-6px",
                                                    zIndex: 9999,
                                                    display:
                                                        focusedAnnotation ===
                                                        annotation?.id
                                                            ? "block"
                                                            : "none",
                                                },
                                                bottomLeft: {
                                                    border: `2px solid ${borderColor}`,
                                                    backgroundColor: "white",
                                                    cursor: "sw-resize",
                                                    width: "10px",
                                                    height: "10px",
                                                    borderRadius: "50%",
                                                    bottom: "-6px",
                                                    left: "-6px",
                                                    zIndex: 9999,
                                                    display:
                                                        focusedAnnotation ===
                                                        annotation?.id
                                                            ? "block"
                                                            : "none",
                                                },
                                                topRight: {
                                                    border: `2px solid ${borderColor}`,
                                                    backgroundColor: "white",
                                                    cursor: "nw-resize",
                                                    width: "10px",
                                                    height: "10px",
                                                    borderRadius: "50%",
                                                    top: "-6px",
                                                    right: "-6px",
                                                    zIndex: 9999,
                                                    display:
                                                        focusedAnnotation ===
                                                        annotation?.id
                                                            ? "block"
                                                            : "none",
                                                },
                                                topLeft: {
                                                    border: `2px solid ${borderColor}`,
                                                    backgroundColor: "white",
                                                    cursor: "nw-resize",
                                                    width: "10px",
                                                    height: "10px",
                                                    borderRadius: "50%",
                                                    top: "-6px",
                                                    left: "-6px",
                                                    zIndex: 9999,
                                                    display:
                                                        focusedAnnotation ===
                                                        annotation?.id
                                                            ? "block"
                                                            : "none",
                                                },
                                            }}
                                            key={index}
                                            size={{
                                                width: annotation.element_width,
                                                height: annotation.element_height,
                                            }}
                                            position={{
                                                x: annotation.position_x,
                                                y: annotation.position_y,
                                            }}
                                            onDragStop={(e, d) => {
                                                const newAnnotations = {
                                                    ...annotations,
                                                };
                                                newAnnotations[
                                                    documentActive.id
                                                ][index].position_x = d.x;
                                                newAnnotations[
                                                    documentActive.id
                                                ][index].position_y = d.y;
                                                handleCollectData(
                                                    "annotations",
                                                    newAnnotations
                                                );
                                            }}
                                            onResizeStop={(
                                                e,
                                                direction,
                                                ref,
                                                delta,
                                                position
                                            ) => {
                                                const newAnnotations = {
                                                    ...annotations,
                                                };
                                                newAnnotations[
                                                    documentActive.id
                                                ][index].element_width =
                                                    parseInt(
                                                        ref.style.width,
                                                        10
                                                    );
                                                newAnnotations[
                                                    documentActive.id
                                                ][index].element_height =
                                                    parseInt(
                                                        ref.style.height,
                                                        10
                                                    );

                                                handleCollectData(
                                                    "annotations",
                                                    newAnnotations
                                                );
                                            }}
                                            className="annotation__signature"
                                            style={{
                                                background: backgroundColor,
                                                position: "absolute",
                                                display: "flex",
                                                alignItems: "center",
                                                border: `2px solid ${borderColor}`,
                                                cursor:
                                                    focusedAnnotation ===
                                                    annotation?.id
                                                        ? "move"
                                                        : "pointer",
                                            }}
                                            onMouseDown={(e) => {
                                                handleFocusAnnotation(
                                                    annotation?.id
                                                );
                                            }}
                                        >
                                            <i class="fas fa-signature text-dark fs-md-20 fs-10"></i>
                                        </Rnd>
                                    )
                                );
                            })}
                        <canvas
                            ref={canvasRef}
                            style={{
                                // border: "1px solid black",
                                height: "100%",
                                width: "100%",
                            }}
                        ></canvas>
                    </div>
                </div>
            </div>
            <div className="p-4 signature__detail">
                {focusedAnnotation && (
                    <>
                        <div className="d-flex align-items-start justify-content-between w-100">
                            <div className="fs-15 fw-bold mb-3 text-dark">
                                Signature
                            </div>
                            <div
                                onClick={() => {
                                    handleRemoveAnnotation(focusedAnnotation);
                                }}
                                role="button"
                            >
                                <i class="feather-trash-2 text-danger fw-medium fs-26"></i>
                            </div>
                        </div>
                        <div className="mt-4">
                            <div className="fs-16 fw-medium text-dark">
                                Assigned to
                            </div>
                            <div className="mt-2">
                                {(() => {
                                    const signer = annotationDocument.find(
                                        (annotation) =>
                                            annotation.id === focusedAnnotation
                                    )?.signer;
                                    return (
                                        <div
                                            role="button"
                                            className="d-flex align-items-center gap-3"
                                        >
                                            <div>
                                                <div
                                                    className="avatar avatar-md"
                                                    style={{
                                                        background:
                                                            signer?.color,
                                                    }}
                                                ></div>
                                            </div>
                                            <div>
                                                <div className="fs-14 fw-bold text-dark">
                                                    {signer?.nama}
                                                </div>
                                                <div className="fs-12 text-muted">
                                                    {signer?.email}
                                                </div>
                                            </div>
                                        </div>
                                    );
                                })()}
                            </div>
                            <div className="mt-3">
                                <div className="fs-16 fw-medium text-dark mb-2">
                                    Posisi
                                </div>
                                <div className="mb-3">
                                    <label
                                        htmlFor="posisi_x"
                                        className="form-label"
                                    >
                                        Posisi X
                                    </label>
                                    <div class="input-group">
                                        <input
                                            type="number"
                                            className="form-control"
                                            id="posisi_x"
                                            name="posisi_x"
                                            value={
                                                annotationDocument.find(
                                                    (annotation) =>
                                                        annotation?.id ===
                                                        focusedAnnotation
                                                )?.position_x
                                            }
                                            onChange={(e) => {
                                                const newAnnotations = {
                                                    ...annotations,
                                                };
                                                newAnnotations[
                                                    documentActive.id
                                                ].find(
                                                    (annotation) =>
                                                        annotation.id ===
                                                        focusedAnnotation
                                                ).position_x = e.target.value;
                                                handleCollectData(
                                                    "annotations",
                                                    newAnnotations
                                                );
                                            }}
                                        />
                                        <span class="input-group-text">px</span>
                                    </div>
                                </div>
                                <div className="mb-3">
                                    <label
                                        htmlFor="posisi_x"
                                        className="form-label"
                                    >
                                        Posisi Y
                                    </label>
                                    <div class="input-group">
                                        <input
                                            type="number"
                                            className="form-control"
                                            id="posisi_x"
                                            name="posisi_x"
                                            value={
                                                annotationDocument.find(
                                                    (annotation) =>
                                                        annotation?.id ===
                                                        focusedAnnotation
                                                )?.position_y
                                            }
                                            onChange={(e) => {
                                                const newAnnotations = {
                                                    ...annotations,
                                                };
                                                newAnnotations[
                                                    documentActive.id
                                                ].find(
                                                    (annotation) =>
                                                        annotation.id ===
                                                        focusedAnnotation
                                                ).position_y = e.target.value;
                                                handleCollectData(
                                                    "annotations",
                                                    newAnnotations
                                                );
                                            }}
                                        />
                                        <span class="input-group-text">px</span>
                                    </div>
                                </div>
                                <div className="mb-3">
                                    <label
                                        htmlFor="element_height"
                                        className="form-label"
                                    >
                                        Tinggi
                                    </label>
                                    <div class="input-group">
                                        <input
                                            type="number"
                                            className="form-control"
                                            id="element_height"
                                            name="element_height"
                                            value={
                                                annotationDocument.find(
                                                    (annotation) =>
                                                        annotation?.id ===
                                                        focusedAnnotation
                                                )?.element_height
                                            }
                                            onChange={(e) => {
                                                const newAnnotations = {
                                                    ...annotations,
                                                };
                                                newAnnotations[
                                                    documentActive.id
                                                ].find(
                                                    (annotation) =>
                                                        annotation.id ===
                                                        focusedAnnotation
                                                ).element_height =
                                                    e.target.value;
                                                handleCollectData(
                                                    "annotations",
                                                    newAnnotations
                                                );
                                            }}
                                        />
                                        <span class="input-group-text">px</span>
                                    </div>
                                </div>
                                <div className="mb-3">
                                    <label
                                        htmlFor="element_width"
                                        className="form-label"
                                    >
                                        Lebar
                                    </label>
                                    <div class="input-group">
                                        <input
                                            type="number"
                                            className="form-control"
                                            id="element_width"
                                            name="element_width"
                                            value={
                                                annotationDocument.find(
                                                    (annotation) =>
                                                        annotation?.id ===
                                                        focusedAnnotation
                                                )?.element_width
                                            }
                                            onChange={(e) => {
                                                const newAnnotations = {
                                                    ...annotations,
                                                };
                                                newAnnotations[
                                                    documentActive.id
                                                ].find(
                                                    (annotation) =>
                                                        annotation.id ===
                                                        focusedAnnotation
                                                ).element_width =
                                                    e.target.value;
                                                handleCollectData(
                                                    "annotations",
                                                    newAnnotations
                                                );
                                            }}
                                        />
                                        <span class="input-group-text">px</span>
                                    </div>
                                </div>
                                <div className="mb-3">
                                    <label
                                        htmlFor="element_width"
                                        className="form-label"
                                    >
                                        Halaman
                                    </label>
                                    <div class="input-group">
                                        <input
                                            type="number"
                                            className="form-control"
                                            id="element_width"
                                            name="element_width"
                                            value={
                                                annotationDocument.find(
                                                    (annotation) =>
                                                        annotation?.id ===
                                                        focusedAnnotation
                                                )?.page ?? 1
                                            }
                                            onChange={(e) => {
                                                const newAnnotations = {
                                                    ...annotations,
                                                };

                                                const page = (newAnnotations[
                                                    documentActive.id
                                                ].find(
                                                    (annotation) =>
                                                        annotation.id ===
                                                        focusedAnnotation
                                                ).page = e.target.value
                                                    ? parseInt(e.target.value)
                                                    : 1);

                                                if (page < 1) {
                                                    newAnnotations[
                                                        documentActive.id
                                                    ].find(
                                                        (annotation) =>
                                                            annotation.id ===
                                                            focusedAnnotation
                                                    ).page = 1;
                                                }

                                                if (page > totalPage) {
                                                    newAnnotations[
                                                        documentActive.id
                                                    ].find(
                                                        (annotation) =>
                                                            annotation.id ===
                                                            focusedAnnotation
                                                    ).page = totalPage;
                                                }

                                                handleCollectData(
                                                    "annotations",
                                                    newAnnotations
                                                );
                                            }}
                                        />
                                        <span class="input-group-text">px</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </>
                )}
            </div>
        </div>
    );
}
