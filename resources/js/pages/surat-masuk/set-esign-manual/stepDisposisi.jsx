import React, { useState } from "react";
import FormForward from "../form-forward";

export default function StepDisposisi({
    step,
    setFormData,
    formData,
}) {
    const csrf_token = document.querySelector('meta[name="csrf-token"]');
    const [userData, setUserData] = useState({});

    const getDataFor = async ({ unit_bisnis_id = null }) => {
        try {
            if (userData?.[`${unit_bisnis_id}`]?.length > 0) return;

            const { data } = await axios.get(`/pengguna/list-group`, {
                headers: {
                    "X-CSRF-TOKEN": csrf_token.content,
                },
                params: {
                    unit_bisnis_id,
                },
            });
            data.map((item) => {
                item.value = item.id;
                delete item.id;
                return item;
            });

            userData[unit_bisnis_id] = data;
            setUserData(userData);
        } catch (error) {
            console.log(error);
        }
    }

    return (
        <>
            <div className="px-4 py-5">
                <div className="row justify-content-center">
                    <div className="col-md-8 max-auto">
                        <div className="mb-5">
                            <h4 className="fw-bold fs-22 text-dark">
                                <i className="fas fa-user fs-20 pe-2"></i>
                                Pilih Penerima
                            </h4>
                            <div className="text-muted fs-14 mt-1">
                                Tentukan kepada siapa surat ini akan diteruskan
                                dan tambahkan pesan jika diperlukan.
                            </div>
                        </div>
                        <div className="pb-4">
                            <FormForward
                                step={step}
                                userData={userData}
                                formData={formData}
                                getDataFor={getDataFor}
                                setFormData={setFormData}
                            />
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
