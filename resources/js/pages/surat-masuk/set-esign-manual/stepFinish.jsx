import React from "react";

export default function StepFinish({ collectData, formData }) {
    const attachmentRequiredSign = data_detail?.attachment_file_surat?.filter(
        (item) => parseInt(item.required_signature) === 1
    );

    const signatureDetailLength = Object.keys(
        collectData?.signatures[data_detail.id] ?? {}
    ).length;
    const isSignedSurat =
        signatureDetailLength ===
        collectData?.annotations?.[data_detail.id]?.length;

    return (
        <div className="row justify-content-center px-5 py-5">
            <div className="col-md-10 mx-auto">
                <div className="mb-5">
                    <h4 className="fw-bold fs-22 text-dark">
                        <i className="fas fa-check-circle fs-20 pe-2"></i>
                        Review & Kirim
                    </h4>
                    <div className="text-muted fs-14 mt-1">
                        Pastikan semua data sudah benar sebelum mengirimkan
                        dokumen.
                    </div>
                </div>
                <div className="row justify-content-center align-items-start">
                    <div className="col-md-6">
                        <div>
                            <h4 className="fw-bold fs-18 text-dark">
                                Disposisi
                            </h4>
                            <div className="d-flex flex-column gap-3 mt-4">
                                {formData?.forwardList?.map((item, index) => (
                                    <div
                                        key={index}
                                        className="d-flex align-items-center gap-3"
                                    >
                                        <div className="d-flex align-items-start gap-3">
                                            <div>
                                                <div className="fs-16 text-dark">
                                                    {index + 1}.
                                                </div>
                                            </div>
                                            <div>
                                                <div className="d-flex align-items-center gap-3">
                                                    <div className="text-dark pe-4">
                                                        <strong>
                                                            Unit Bisnis:
                                                        </strong>{" "}
                                                        {
                                                            item.unit_bisnis_id
                                                                ?.label
                                                        }
                                                    </div>
                                                    <div className="text-dark">
                                                        <strong>Kepada:</strong>{" "}
                                                        {item.kepada
                                                            .map(
                                                                (recipient) =>
                                                                    recipient.name
                                                            )
                                                            .join(", ")}
                                                    </div>
                                                </div>
                                                <div className="mt-2">
                                                    <strong className="text-dark">
                                                        Catatan:
                                                    </strong>
                                                    <div
                                                        className="text-muted"
                                                        dangerouslySetInnerHTML={{
                                                            __html:
                                                                item.catatan ??
                                                                "-",
                                                        }}
                                                    ></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                    <div className="col-md-6">
                        <div>
                            <h4 className="fw-bold fs-18 text-dark">
                                Tanda Tangan
                            </h4>
                            <div className="d-flex flex-column gap-3 mt-4">
                                <div
                                    className="d-flex"
                                    style={{
                                        flexDirection: "column",
                                        gap: "1rem",
                                    }}
                                >
                                    <div className="d-md-flex align-items-center justify-content-between gap-4 fs-16">
                                        <div
                                            className={`d-flex align-items-start gap-3 ${
                                                isSignedSurat
                                                    ? "text-success"
                                                    : "text-dark"
                                            }`}
                                        >
                                            <div>1.</div>
                                            <div>
                                                <div className="fw-semibold">
                                                    Surat No{" "}
                                                    {data_detail.no_surat}
                                                    {isSignedSurat && (
                                                        <i className="fas fa-check-circle text-success ms-2"></i>
                                                    )}
                                                </div>
                                                <div className="fs-14 text-dark">
                                                    <strong>
                                                        {signatureDetailLength ??
                                                            0}
                                                    </strong>{" "}
                                                    Tanda Tangan
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {attachmentRequiredSign?.map(
                                        (item, index) => {
                                            const signatureLength = Object.keys(
                                                collectData?.signatures[
                                                    item.id
                                                ] ?? {}
                                            ).length;
                                            const isSigned = signatureLength ===
                                                collectData?.annotations?.[
                                                    item.id
                                                ]?.length;
                                            return (
                                                <div className="d-md-flex align-items-center justify-content-between gap-4 fs-16">
                                                    <div
                                                        className={`d-flex align-items-start gap-3 ${
                                                            isSigned
                                                                ? "text-success"
                                                                : "text-dark"
                                                        }`}
                                                    >
                                                        <div>{index + 2}.</div>
                                                        <div>
                                                            <div
                                                                className={`fw-semibold`}
                                                            >
                                                                Lampiran{" "}
                                                                {
                                                                    item.nama_original
                                                                }
                                                                {isSigned && (
                                                                    <i className="fas fa-check-circle text-success ms-2"></i>
                                                                )}
                                                            </div>
                                                            <div className="fs-14 text-dark">
                                                                <strong>
                                                                    {signatureLength ? signatureLength : "Tidak ada"}
                                                                </strong>{" "}
                                                                Tanda Tangan
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            );
                                        }
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
