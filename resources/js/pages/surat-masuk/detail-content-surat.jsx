import React from "react";
import DetailContent from "./detail-content";

export default function DetailContentSurat({
    data,
    contentSurat,
    handleOpenDetail,
    handleDownload,
    isReplyForward,
}) {
    return (
        <>
            {isReplyForward ? (
                <div className="reply_message mt-4 ms-4">
                    <div>
                        Pada{" "}
                        {data_detail?.latest_revision_formatted ??
                            data_detail?.date_formatted}{" "}
                        &nbsp;
                        {data_detail?.pengirim?.nama}{" "}
                        {`<${data_detail?.pengirim?.email} >`}{" "}
                        {parseInt(data?.type) === 3 ? "merevisi" : "membuat"}{" "}
                        surat:
                    </div>
                    <div
                        style={{
                            paddingTop: "10px",
                        }}
                        className="detail_reply_message"
                    >
                        <DetailContent
                            contentSurat={
                                data?.parent_content_surat?.content_replace
                            }
                        />
                    </div>
                </div>
            ) : (
                <DetailContent contentSurat={contentSurat} />
            )}
            {data?.attachment_file_surat?.length > 0 && (
                <>
                    <hr />
                    <div className="mt-4">
                        <div className="fs-16 fw-bold text-dark mb-3">
                            {data?.attachment_file_surat?.length > 1
                                ? `${data?.attachment_file_surat?.length} Lampiran`
                                : "1 Lampiran"}
                        </div>
                        <div className="row align-items-center g-4">
                            {data?.attachment_file_surat?.map((item, index) => (
                                <div
                                    onClick={() => {
                                        handleOpenDetail(item);
                                    }}
                                    className="col-md-3"
                                    role="button"
                                    key={index}
                                >
                                    <div
                                        className="card shadow-none overflow-hidden "
                                        style={{
                                            border: "1px solid #E8E9EA",
                                            borderRadius: "5px",
                                        }}
                                    >
                                        <div
                                            className="p-3 d-flex align-items-center justify-content-between"
                                            style={{
                                                background: "#F5F5F5",
                                            }}
                                        >
                                            <div className="text-truncate">
                                                {(() => {
                                                    const fileExtensionIcons = {
                                                        pdf: {
                                                            icon: "fas fa-file-pdf",
                                                            color: "red",
                                                        },
                                                        doc: {
                                                            icon: "fas fa-file-word",
                                                            color: "blue",
                                                        },
                                                        docx: {
                                                            icon: "fas fa-file-word",
                                                            color: "blue",
                                                        },
                                                        xls: {
                                                            icon: "fas fa-file-excel",
                                                            color: "green",
                                                        },
                                                        xlsx: {
                                                            icon: "fas fa-file-excel",
                                                            color: "green",
                                                        },
                                                        jpg: {
                                                            icon: "fas fa-file-image",
                                                            color: "orange",
                                                        },
                                                        jpeg: {
                                                            icon: "fas fa-file-image",
                                                            color: "orange",
                                                        },
                                                        png: {
                                                            icon: "fas fa-file-image",
                                                            color: "orange",
                                                        },
                                                        gif: {
                                                            icon: "fas fa-file-image",
                                                            color: "orange",
                                                        },
                                                        zip: {
                                                            icon: "fas fa-file-archive",
                                                            color: "violet",
                                                        },
                                                        rar: {
                                                            icon: "fas fa-file-archive",
                                                            color: "violet",
                                                        },
                                                        ppt: {
                                                            icon: "fas fa-file-powerpoint",
                                                            color: "red",
                                                        },
                                                        pptx: {
                                                            icon: "fas fa-file-powerpoint",
                                                            color: "red",
                                                        },
                                                        txt: {
                                                            icon: "fas fa-file-alt",
                                                            color: "grey",
                                                        },
                                                        mp3: {
                                                            icon: "fas fa-file-audio",
                                                            color: "blue",
                                                        },
                                                        wav: {
                                                            icon: "fas fa-file-audio",
                                                            color: "blue",
                                                        },
                                                        ogg: {
                                                            icon: "fas fa-file-audio",
                                                            color: "blue",
                                                        },
                                                        mp4: {
                                                            icon: "fas fa-file-video",
                                                            color: "red",
                                                        },
                                                        avi: {
                                                            icon: "fas fa-file-video",
                                                            color: "red",
                                                        },
                                                        mov: {
                                                            icon: "fas fa-file-video",
                                                            color: "red",
                                                        },
                                                    };
                                                    const fileExtension =
                                                        item?.nama_original
                                                            .split(".")
                                                            .pop();
                                                    const { icon, color } =
                                                        fileExtensionIcons[
                                                            fileExtension
                                                        ] || {
                                                            icon: "fas fa-file",
                                                            color: "black",
                                                        };
                                                    const iconElement = (
                                                        <i
                                                            className={`${icon} fs-18`}
                                                            style={{
                                                                color,
                                                            }}
                                                        ></i>
                                                    );
                                                    return iconElement;
                                                })()}
                                                <span className="text-dark fw-medium fs-14 ms-2">
                                                    {item?.nama_original}
                                                </span>
                                            </div>
                                            <div
                                                className="ms-2"
                                                role="button"
                                                onClick={(e) => {
                                                    e.preventDefault();
                                                    e.stopPropagation();
                                                    handleDownload(item);
                                                }}
                                            >
                                                <i className="fas fa-download fs-15 text-success"></i>
                                            </div>
                                            {parseInt(item.required_signature) === 1 && (
                                                <div
                                                    className="ms-3"
                                                    role="button"
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                        handleDownload(
                                                            item,
                                                            "signature"
                                                        );
                                                    }}
                                                >
                                                    <i className="fas fa-signature fs-15 text-primary"></i>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </>
            )}
        </>
    );
}
