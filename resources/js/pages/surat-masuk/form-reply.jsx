import React from "react";
import Select from "react-select";
import { Editor } from "@tinymce/tinymce-react";
import Swal from "sweetalert2";
import DetailContent from "./detail-content";
import { canPermission } from "../../helper";

export default function FormReply({
    userData,
    formData,
    setFormData,
    handleChanges,
    errors,
    showReplyForward,
    editorRef,
    setAttachmentFile,
    progressAttachment,
    setProgressAttachment,
}) {
    const listApprover = [
        {
            id: "suratDisetujui",
            label: "Surat Disetujui",
            value: "disetujui",
            color: "success",
        },
        {
            id: "suratDisetujuiDenganPertimbangan",
            label: "Surat Disetujui dengan Pertimbangan",
            value: "disetujui_dengan_pertimbangan",
            color: "warning",
        },
        {
            id: "suratTidakDisetujui",
            label: "Surat Tidak Disetujui",
            value: "tidak_disetujui",
            color: "danger",
        },
    ];

    return (
        <>
            {showReplyForward?.type === "reply" &&
                canPermission("Surat Masuk.Approve") && (
                    <div className="mb-3">
                        <div className="d-flex align-items-center gap-3 reply_forward_option">
                            {listApprover.map((item, index) => (
                                <label
                                    key={index}
                                    className={`btn btn-outline-${
                                        item.color
                                    } rounded-3 ${
                                        formData.approve_status === item.value
                                            ? "active"
                                            : ""
                                    }`}
                                >
                                    <input
                                        type="radio"
                                        name="approve_status"
                                        className="d-none"
                                        id={item.id}
                                        value={item.value}
                                        autoComplete="off"
                                        onChange={(e) =>
                                            setFormData({
                                                ...formData,
                                                approve_status: e.target.value,
                                            })
                                        }
                                        checked={
                                            formData.approve_status === item.value
                                        }
                                    />{" "}
                                    {item.label}
                                </label>
                            ))}
                        </div>
                    </div>
                )}
            {/* {canPermission("Disposisi.Full_Akses") ||
            canPermission("Disposisi.Corsec") ? (
                <Select
                    className="mb-3"
                    options={userData}
                    isMulti
                    filterOption={(option, inputValue) => {
                        const inputValueLower = inputValue.toLowerCase();
                        if (!inputValue) return option;
                        return (
                            option?.data?.name
                                ?.toLowerCase()
                                .includes(inputValueLower)
                        );
                    }}
                    placeholder="Balas Kepada"
                    value={formData.kepada}
                    formatOptionLabel={(option, { context }) => {
                        if (context === "value") {
                            return (
                                <div className="d-flex align-items-center gap-3">
                                    <div>
                                        {option?.image ? (
                                            <img
                                                src={option.image ?? ""}
                                                className="avatar avatar-xs"
                                                alt=""
                                            />
                                        ) : (
                                            ""
                                        )}
                                    </div>
                                    <div className="fs-12">{option.email}</div>
                                </div>
                            );
                        } else {
                            return (
                                <div className="d-flex align-items-center gap-3">
                                    <div>
                                        {option?.image ? (
                                            <img
                                                src={option.image ?? ""}
                                                className="avatar avatar-md"
                                                alt=""
                                            />
                                        ) : (
                                            ""
                                        )}
                                    </div>
                                    <div>
                                        <div className="fs-14 fw-bold">
                                            {option.nama}
                                        </div>
                                        <div className="fs-12 text-muted">
                                            {option.email}
                                        </div>
                                    </div>
                                </div>
                            );
                        }
                    }}
                    onChange={(value) => {
                        handleChanges({
                            target: {
                                name: "kepada",
                                value: value,
                            },
                        });
                    }}
                    styles={{
                        control: (baseStyles) => ({
                            ...baseStyles,
                            borderColor: "#e5e7eb",
                            boxShadow: "none",
                            padding: "4px 6px",
                            borderRadius: "5px",
                        }),
                        option: (baseStyles, state) => ({
                            ...baseStyles,
                            backgroundColor: state.isSelected
                                ? "#EDF2F7"
                                : "#fff",
                            color: "#2D3748",
                            "&:hover": {
                                backgroundColor: "#EDF2F7",
                            },
                            "&:active": {
                                backgroundColor: "#EDF2F7",
                            },
                            '&[data-selected="true"]': {
                                backgroundColor: "#EDF2F7",
                            },
                        }),
                        multiValue: (baseStyles) => ({
                            ...baseStyles,
                            backgroundColor: "#fff",
                            borderRadius: "12px",
                            border: "1px solid #D5D7D8",
                        }),
                        menu: (baseStyles) => ({
                            ...baseStyles,
                            border: "1px solid #e5e7eb",
                            boxShadow: "0 1px 2px 0 rgba(0, 0, 0, 0.10)",
                            zIndex: 9999,
                        }),
                    }}
                />
            ) : (
                ""
            )} */}
            <div className="content_surat_detail">
                <Editor
                    apiKey="5391u6bfka4eor1n9a5d1r66gknipnlrbo7m37uxsb5nyye3"
                    onInit={(_evt, editor) => (editorRef.current = editor)}
                    initialValue=""
                    init={{
                        height: 150,
                        menubar: false,
                        plugins:
                            "visualblocks advlist autolink lists link image charmap preview anchor searchreplace visualblocks code fullscreen insertdatetime media table code help wordcount media",
                        toolbar:
                            "undo redo | bold italic underline strikethrough | link image | align | numlist bullist indent outdent",
                        content_style:
                            "body { font-family:Helvetica,Arial,sans-serif; font-size:14px }",
                        images_upload_url: "/file-manager/upload",
                        automatic_uploads: true,
                        file_picker_types: "image",
                        file_picker_callback: function (cb, value, meta) {
                            var input = document.createElement("input");
                            input.setAttribute("type", "file");
                            input.setAttribute("accept", "image/*");
                            input.onchange = async function () {
                                var file = this.files[0];
                                var formData = new FormData();
                                formData.append("file", file);

                                const { data } = await axios.post(
                                    `/file-manager/upload`,
                                    formData,
                                    {
                                        headers: {
                                            "Content-Type":
                                                "multipart/form-data",
                                        },
                                    }
                                );
                                if (data.status) {
                                    cb(data.url, {
                                        title: file.name,
                                    });
                                }
                            };
                            input.click();
                        },
                    }}
                />
                {errors.content_surat && (
                    <div className="text-danger mt-2">
                        {errors.content_surat}
                    </div>
                )}
                <div className="reply_message mt-4 ms-4">
                    <div>
                        Pada {data_detail?.latest_revision_formatted ?? data_detail?.date_formatted} &nbsp;
                        {data_detail?.pengirim?.nama}{" "}
                        {`<${data_detail?.pengirim?.email} >`} {data_detail?.latest_revision_at ? 'merevisi' : 'membuat'} surat:
                    </div>
                    <div
                        style={{
                            paddingTop: "10px",
                        }}
                        className="detail_reply_message"
                    >
                        <DetailContent
                            contentSurat={data_detail?.content_surat?.content_replace}
                        />
                    </div>
                </div>
            </div>

            {progressAttachment.length > 0 && (
                <div className="row mt-4">
                    {progressAttachment.map((item, index) => (
                        <div className="col-md-4" key={item.id}>
                            <div className="card shadow-none border  overflow-hidden">
                                <div className="card-body position-relative py-2 ps-3 pe-5">
                                    <div className="d-flex align-items-center gap-2 justify-content-between">
                                        <div className="fw-medium text-dark text-truncate">
                                            {item.name}
                                        </div>
                                        <div>
                                            <div className="text-muted fs-12 text-nowrap">
                                                (
                                                {item.file_size
                                                    ? `${(
                                                          item.file_size / 1024
                                                      ).toFixed(2)} KB`
                                                    : ""}
                                                )
                                            </div>
                                        </div>
                                    </div>
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setAttachmentFile((currentFiles) =>
                                                currentFiles.filter(
                                                    (file) =>
                                                        file.id !== item.id
                                                )
                                            );
                                            setProgressAttachment(
                                                (currentProgress) =>
                                                    currentProgress.filter(
                                                        (progress) =>
                                                            progress.id !==
                                                            item.id
                                                    )
                                            );
                                        }}
                                        className="avatar-text avatar-sm position-absolute bg-danger text-white"
                                        style={{
                                            top: "10px",
                                            right: "8px",
                                            padding: "8px",
                                        }}
                                    >
                                        <i className="feather feather-x fs-14"></i>
                                    </button>
                                    {parseInt(item.progress) < 100 ? (
                                        <div className="position-relative pt-2 pb-2">
                                            <div
                                                className="text-end position-absolute"
                                                style={{
                                                    top: "-2px",
                                                    right: "-40px",
                                                }}
                                            >
                                                {item.progress}%
                                            </div>
                                            <div
                                                className="progress"
                                                role="progressbar"
                                                aria-label="Basic example"
                                                aria-valuenow="100"
                                                aria-valuemin="0"
                                                aria-valuemax="100"
                                                style={{
                                                    height: "5px",
                                                    borderRadius: "5px",
                                                }}
                                            >
                                                <div
                                                    className="progress-bar"
                                                    style={{
                                                        width: `${item.progress}%`,
                                                        borderRadius: "5px",
                                                    }}
                                                ></div>
                                            </div>
                                        </div>
                                    ) : (
                                        ""
                                    )}
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </>
    );
}
