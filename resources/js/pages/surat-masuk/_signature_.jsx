import React, { useState, useRef, useEffect } from "react";
import Draggable from "react-draggable";
import { Resizable, ResizableBox } from "react-resizable";

import "pdfjs-dist/build/pdf.min.mjs";
import "pdfjs-dist/build/pdf.worker.min.mjs";

export default function Signature({ data, dataSignature, setDataSignature }) {
    const [pdfFile, setPdfFile] = useState("");
    const [position, setPosition] = useState(dataSignature.position);
    const [sizePdf, setSizePdf] = useState({
        width: 0,
        height: 0,
    });
    const canvasRef = useRef(null);
    const [pagePdf, setPagePdf] = useState(dataSignature.page ?? 1);
    const [totalPage, setTotalPage] = useState(1);

    // const handleStop = (e, data) => {
    //     setPosition({ x: data.x, y: data.y });
    // };

    const fetchPdfFile = async () => {
        try {
            const response = await axios.get(
                `/surat-masuk/render-pdf/${data_detail?.id}?download=true`,
                {
                    responseType: "blob",
                }
            );
            setPdfFile(response.data);
        } catch (error) {
            console.error("Gagal mengambil file PDF:", error);
        }
    };

    const renderPDFToCanvas = async (pageInject) => {
        if (!pdfFile) return;

        const fileReader = new FileReader();
        fileReader.onload = async function () {
            const typedArray = new Uint8Array(this.result);
            const pdf = await pdfjsLib.getDocument(typedArray).promise;
            setTotalPage(pdf.numPages);
            const page = await pdf.getPage(pageInject ?? pagePdf); // Render halaman pertama

            const scale = 1.5;
            const viewport = page.getViewport({ scale: scale });
            const canvas = canvasRef.current;
            const context = canvas.getContext("2d");

            canvas.height = viewport.height * scale;
            canvas.width = viewport.width * scale;
            context.scale(scale, scale);

            setSizePdf({
                width: viewport.width,
                height: viewport.height,
            });


            const renderContext = {
                canvasContext: context,
                viewport: viewport,
            };

            await page.render(renderContext).promise.then(function () {
                console.log("Render PDF Selesai");
                data.tanda_tangan_surat.forEach((item) => {
                    if (parseInt(item.page) === (pageInject ?? 1)) {
                        const img = new Image();
                        img.src = "/assets/images/barcode.png";
                        img.onload = async () => {
                            // Pastikan posisi tetap konsisten setiap kali dibuka dengan menggunakan posisi yang telah disimpan
                            const posisiX = (item?.posisi_x ?? 0); // Tambahkan posisi x yang disimpan ke posisi x item
                            const posisiY = (item?.posisi_y ?? 0); // Tambahkan posisi y yang disimpan ke posisi y item
                            const ukuran = sizePdf.width ? (60 * sizePdf.width / viewport.width) : 60; // Sesuaikan ukuran berdasarkan lebar PDF saat ini dibandingkan dengan viewport default
                            await context.drawImage(img, posisiX, posisiY, ukuran, ukuran);
                        };
                    }
                });
            });

        };

        fileReader.readAsArrayBuffer(pdfFile);
    };

    useEffect(() => {
        renderPDFToCanvas();
    }, [pdfFile]);

    useEffect(() => {
        fetchPdfFile();
        createDraggableImage("/assets/images/barcode.png");
    }, []);

    const handleGetPage = (type) => {
        if (totalPage === 1) return;
        let page = pagePdf;
        if (type === "prev") {
            if (pagePdf === 1) return;
            page = pagePdf - 1;
            setPagePdf(page);
        } else {
            if (pagePdf === totalPage) return;
            page = pagePdf + 1;
            setPagePdf(page);
        }

        renderPDFToCanvas(page);
    };

    let imageElement = null;
    const createDraggableImage = (imageSrc) => {
        imageElement = document.getElementById("draggable_barcode");
        imageElement.dataset.x = position.x;
        imageElement.dataset.y = position.y;

        interact(imageElement).draggable({
            listeners: {
                move(event) {
                    const target = event.target;
                    let x =
                        (parseFloat(target.getAttribute("data-x")) || 0) +
                        event.dx;
                    let y =
                        (parseFloat(target.getAttribute("data-y")) || 0) +
                        event.dy;

                    // Jika posisi kurang dari 0, balikkan ke 0
                    x = x < 0 ? 0 : x;
                    y = y < 0 ? 0 : y;

                    target.style.transform = `translate(${x}px, ${y}px) scale(1)`;

                    target.setAttribute("data-x", x);
                    target.setAttribute("data-y", y);
                    setPosition({ x, y });
                },
            },
        });
    };

    return (
        <>
            <div className="modal-header">
                <h2 className="d-flex flex-column mb-0">
                    <span className="fs-18 fw-bold mb-1">
                        Tanda Tangan Surat
                    </span>
                </h2>
                <div className="d-flex align-items-center gap-3">
                    <button
                        type="button"
                        disabled={position.x === 0 && position.y === 0}
                        onClick={() => {
                            setDataSignature({
                                signature: false,
                                position: {
                                    x: 0,
                                    y: 0,
                                },
                                height: sizePdf.height,
                                width: sizePdf.width,
                            });
                            setPosition({
                                x: 0,
                                y: 0,
                            });
                        }}
                        className="btn btn-danger"
                        style={{ fontSize: "10px" }}
                    >
                        <i class="feather-trash pe-2"></i> Batal
                    </button>
                    <button
                        type="button"
                        onClick={() => {
                            setDataSignature({
                                signature: true,
                                position:
                                    dataSignature.position === position
                                        ? dataSignature.position
                                        : position,
                                page: pagePdf,
                                height: sizePdf.height,
                                width: sizePdf.width,
                            });
                            $("#modalPreview").modal("hide");
                        }}
                        disabled={position.x === 0 && position.y === 0}
                        className="btn btn-primary"
                        style={{ fontSize: "10px" }}
                    >
                        <i class="feather-save pe-2"></i> Simpan & Tutup
                    </button>
                    <a
                        href="javascript:void(0)"
                        class="avatar-text avatar-md bg-soft-danger close-icon"
                        data-bs-dismiss="modal"
                    >
                        <i class="feather-x text-danger"></i>
                    </a>
                </div>
            </div>
            <div
                class="modal-body py-0"
                style={{
                    margin: "0 auto",
                }}
            >
                <div className="p-4">
                    <div className="d-flex align-items-center gap-3 justify-content-center mb-2">
                        <button
                            className="btn btn-primary btn-sm"
                            disabled={pagePdf === 1}
                            onClick={() => handleGetPage("prev")}
                        >
                            <i class="fas fa-arrow-left me-2"></i>
                            Sebelumnya
                        </button>
                        <div>
                            Page {pagePdf} of {totalPage}
                        </div>
                        <button
                            className="btn btn-primary btn-sm"
                            disabled={pagePdf === totalPage}
                            onClick={() => handleGetPage("next")}
                        >
                            Selanjutnya
                            <i class="fas fa-arrow-right ms-2"></i>
                        </button>
                    </div>
                    <div
                        className="parent_preview"
                        style={{
                            height: sizePdf.height,
                            width: sizePdf.width,
                            // border: "1px solid black",
                            position: "relative",
                        }}
                    >
                        {/* <Draggable
                            position={{
                                x: position.x < 0 ? 0 : (position.x > sizePdf.width - 104 ? sizePdf.width - 104 : position.x),
                                y: position.y < 0 ? 0 : (position.y > sizePdf.height - 104 ? sizePdf.height - 104 : position.y),
                             }}
                            onStop={handleStop}
                            bounds=".parent_preview"
                        > */}
                        <div
                            id="draggable_barcode"
                            style={{
                                display: "block",
                                cursor: "grab",
                                background: "url('/assets/images/barcode.png')",
                                backgroundSize: "cover",
                                height: "60px",
                                width: "60px",
                                padding: "10px",
                                border: "3px dashed red",
                                position: "absolute",
                                zIndex: 10,
                                transform: `translate(${position.x}px, ${position.y}px) scale(1)`,
                            }}
                        ></div>
                        {/* </Draggable> */}
                        <canvas
                            ref={canvasRef}
                            style={{
                                width: "100%",
                                height: "100%",
                                border: "1px solid black",
                            }}
                        ></canvas>
                    </div>
                </div>
            </div>
        </>
    );
}
