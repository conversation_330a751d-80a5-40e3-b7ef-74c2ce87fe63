import React, { useState } from "react";

export default function ModalSignersList({ dataDetail }) {
    const [signers, setSigners] = useState([]);

    useState(() => {
        setSigners(dataDetail?.request_esign?.signers);
    }, []);

    return (
        <>
            <div className="modal-header aligm-items-center">
                <h2 className="d-flex flex-column mb-0">
                    <span className="fs-18 fw-bold mb-1">
                        List Penandatangan
                    </span>
                </h2>
                <div></div>
                <div className="d-flex align-items-center gap-3">
                    <a
                        href="javascript:void(0)"
                        class="avatar-text avatar-md bg-soft-danger close-icon"
                        data-bs-dismiss="modal"
                    >
                        <i class="feather-x text-danger"></i>
                    </a>
                </div>
            </div>
            <div class="modal-body">
                <div className="d-flex flex-column gap-3">
                    {signers?.map((signer, index) => {
                        return (
                            <div
                                className="d-flex align-items-center justify-content-between border-bottom pb-3"
                                key={index}
                            >
                                <div className="d-flex flex-column">
                                    <span className="fs-16 text-dark fw-bold">
                                        {signer?.name}
                                    </span>
                                    <span className="fs-12">
                                        {signer?.email}
                                    </span>
                                </div>
                                {signer?.status === "completed" && (
                                    <div className="fs-12 text-success fw-bold">
                                        Ditandatangani pada:
                                        <br />
                                        <span class="fs-13">
                                            {new Date(signer?.signed_at).toLocaleDateString("id-ID", {
                                                day: "2-digit",
                                                month: "long",
                                                year: "numeric",
                                                hour: "2-digit",
                                                minute: "2-digit",
                                                second: "2-digit",
                                            })}
                                        </span>
                                    </div>
                                )}
                                <div>
                                    <div>
                                        {(() => {
                                            let status = "";
                                            let badge = "";
                                            let icon = "";
                                            switch (signer?.status) {
                                                case "your_turn":
                                                case "waiting":
                                                    status = "Menunggu";
                                                    badge = "primary";
                                                    icon = "feather-clock";
                                                    break;
                                                case "completed":
                                                    status =
                                                        "Sudah Menandatangani";
                                                    badge = "success";
                                                    icon =
                                                        "feather-check-circle";
                                                    break;
                                            }

                                            return (
                                                <span
                                                    class={`border border-${badge} text-${badge} fw-bold`}
                                                    style={{
                                                        borderRadius: "20px",
                                                        fontSize: "12px",
                                                        padding: "6px 9px",
                                                    }}
                                                >
                                                    <i
                                                        class={`${icon} pe-2 align-middle`}
                                                    ></i>
                                                    {status}
                                                </span>
                                            );
                                        })()}
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        </>
    );
}
