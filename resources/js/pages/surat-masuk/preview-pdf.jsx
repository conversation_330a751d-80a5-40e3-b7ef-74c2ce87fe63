
// PdfPreview.js
import React, { useRef } from 'react';
import html2pdf from 'html2pdf.js';

const PdfPreview = () => {
  const contentRef = useRef();

  // Fungsi untuk generate PDF dari HTML
  const generatePDF = () => {
    const element = contentRef.current;
    html2pdf().from(element).save();
  };

  return (
    <div>
      <h1>Preview Surat Reimbursement</h1>
      <div ref={contentRef} style={{ padding: '20px', border: '1px solid black' }}>
            {
                data?.tanda_tangan_surat?.map((item, index) => (
                    <img src={'/assets/images/barcode.png'} key={index} style={{ width: '60px', height: '60px',
                        transform: `translate(${item.posisi_x}px, ${item.posisi_y}px)`,
                        position: 'absolute'


                     }} />
                ))
            }
            <div
            dangerouslySetInnerHTML={{
                __html: data.content_surat?.content_replace
             }}
            ></div>
      </div>
      <button onClick={generatePDF} style={{ marginTop: '20px' }}>
        Download PDF
      </button>
    </div>
  );
};

export default PdfPreview;
