import { useEffect, useState, useRef } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import ReplyForward from "./reply-forward";
import ContentSurat from "./content-surat";
import { canPermission, handleRender } from "../../helper";
import ContentHistory from "../surat-keluar/content-history";
import ReactDOM from "react-dom/client";
import Swal from "sweetalert2";
import ModalDownloadSurat from "./modal-download-surat";
import ModalPreview from "./modal-preview";
import Modal from "../../components/modal";
import ModalRevision from "./modal-revision";
import ModalSignersList from "./modal-signers";
import ModalDetailDokumen from "./modal-detail";
import { PDFDocument } from "pdf-lib";
import DetailContentSurat from "./detail-content-surat";
import Signature from "./signature";

const DetailSuratMasuk = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [kopSuratData, setKopSuratData] = useState([]);
    const is_surat_keluar = location.pathname.includes("surat-keluar");
    const [loadingGetDetail, setLoadingGetDetail] = useState(false);
    const [scrollY, setScrollY] = useState(0);

    const [showReplyForward, setShowReplyForward] = useState({
        show: false,
        type: "",
    });

    const getDataKopSurat = async () => {
        try {
            const { data } = await axios.get(`/api/kop-surat/list`);
            data.map((item) => {
                item.value = item.id;
                item.label = item.nama;
                delete item.id;
                return item;
            });

            setKopSuratData(data);
        } catch (error) {
            console.log(error);
        }
    };

    const fetchPdfFile = async (data) => {
        try {
            const response = await axios.get(`${app_url}${data.file_convert}`, {
                responseType: "blob",
            });
            await handleDownloadPDF(response.data, data);
        } catch (error) {
            console.error("Gagal mengambil file PDF:", error);
        }
    };

    const handleDownloadPDF = async (pdfFile, data) => {
        const fileReader = new FileReader();
        fileReader.onload = async function () {
            const existingPdfBytes = new Uint8Array(this.result);
            const pdfDoc = await PDFDocument.load(existingPdfBytes);

            const promises = data_detail.tanda_tangan_surat
                .filter((item) => item.referable_id === data?.id)
                .forEach(async (item) => {
                    if (item.barcode === null) return;

                    const jpgImageBase64 = item.image.split(",")[1];
                    const pngImageDoc = await pdfDoc.embedPng(jpgImageBase64);
                    const { width: imgWidth, height: imgHeight } = pngImageDoc;

                    const pages = pdfDoc.getPages();
                    const page = pages[parseInt(item.page) - 1];
                    const { width, height } = page.getSize();

                    const scaleX = width / item.canvas_width;
                    const scaleY = height / item.canvas_height;
                    const autoWidth =
                        item.height * (imgWidth / imgHeight) * scaleY;

                    page.drawImage(pngImageDoc, {
                        x: item.posisi_x * scaleX,
                        y:
                            height -
                            item.posisi_y * scaleY -
                            item.height * scaleY,
                        width: autoWidth,
                        height: item.height * scaleY,
                    });
                });

            if (promises) {
                await Promise.all(promises);
            }

            const pdfBytes = await pdfDoc.save();
            const blob = new Blob([pdfBytes], { type: "application/pdf" });
            const link = document.createElement("a");
            link.href = URL.createObjectURL(blob);
            link.download = `${data.nama_original?.split(".")[0]}.pdf`;
            link.click();
        };

        fileReader.readAsArrayBuffer(pdfFile);
    };

    const handleDownload = async (data, type) => {
        if (type === "signature") {
            await fetchPdfFile(data);
        } else {
            const fileName = data.nama_original;
            const link = document.createElement("a");
            link.href = `${app_url}${data.file}`;
            link.setAttribute("download", fileName);
            document.body.appendChild(link);
            link.click();
            link.parentNode.removeChild(link);
        }
    };

    const handleOpenDownload = (data) => {
        const componentModal = () => {
            return `
                  <div
                class="modal fade-scale"
                id="modalPreview"
                tabindex="-1"
                data-bs-keyboard="false"
                aria-hidden="true"
                data-bs-dismiss="ou"
            >
                <div
                    class="modal-dialog modal-dialog-centered"
                    role="document"
                >
                    <div class="modal-content"  id="contentSignature">

                    </div>
                </div>
            </div>`;
        };

        $("body").append(componentModal());
        ReactDOM.createRoot(document.getElementById("contentSignature"), {
            hydrate: true,
        }).render(
            <ModalDownloadSurat
                kopSuratData={kopSuratData}
                data={data_detail}
            />
        );
        $("#modalPreview").modal("show");
    };

    const handleOpenDetail = (data, is_preview_surat = false) => {
        $("body").append(
            Modal("", "modalPreview", "modal-xl", "", "contentSignature")
        );
        ReactDOM.createRoot(document.getElementById("contentSignature"), {
            hydrate: true,
        }).render(
            <ModalDetailDokumen
                data={data}
                is_preview_surat={is_preview_surat}
            />
        );
        $("#modalPreview").modal("show");
    };

    const handleOpenHistory = (data) => {
        const componentModal = () => {
            return `
                  <div
                class="modal fade-scale"
                id="modalPreview"
                tabindex="-1"
                data-bs-keyboard="false"
                aria-hidden="true"
                data-bs-dismiss="ou"
            >
                <div
                    class="modal-dialog modal-dialog-centered modal-lg"
                    role="document"
                >
                    <div class="modal-content">
                        <div class="modal-header">
                            <h2 class="d-flex flex-column mb-0">
                                <span class="fs-18 fw-bold mb-1">
                                    History Surat
                                </span>
                            </h2>
                            <a
                                href="javascript:void(0)"
                                class="avatar-text avatar-md bg-soft-danger close-icon"
                                data-bs-dismiss="modal"
                            >
                                <i class="feather-x text-danger"></i>
                            </a>
                        </div>
                        <div
                            class="modal-body py-0"
                            style={{ minHeight: "200px" }}
                            id="contentHistory">

                        </div>
                    </div>
                </div>
            </div>`;
        };

        $("body").append(componentModal());
        ReactDOM.createRoot(document.getElementById("contentHistory"), {
            hydrate: true,
        }).render(
            <ContentHistory
                data_history={data_detail?.history}
                surat={data_detail}
            />
        );
        $("#modalPreview").modal("show");
    };

    // const handleOpenPreview = () => {
    //     // handleOpenDetail(data_detail, true);
    // };

    const handleOpenPreview = (data) => {
        const componentModal = () => {
            return `
                  <div
                class="modal fade-scale"
                id="modalPreview"
                tabindex="-1"
                data-bs-keyboard="false"
                aria-hidden="true"
                data-bs-dismiss="ou"
            >
                <div
                    class="modal-dialog modal-dialog-centered modal-xl"
                    role="document"
                >
                    <div class="modal-content"  id="contentSignature">

                    </div>
                </div>
            </div>`;
        };

        $("body").append(componentModal());
        ReactDOM.createRoot(document.getElementById("contentSignature"), {
            hydrate: true,
        }).render(<ModalPreview data={data_detail} />);
        $("#modalPreview").modal("show");
    };

    const handleRefresh = async () => {
        const csrf_token = document.querySelector('meta[name="csrf-token"]');
        try {
            setLoadingGetDetail(true);
            const response = await axios.get(
                `/surat-masuk/${data_detail?.id}`,
                {
                    headers: {
                        "X-CSRF-TOKEN": csrf_token.content,
                    },
                }
            );
            const data = response?.data?.data;
            data_detail = data;
            navigate(location.pathname);
            setTimeout(() => {
                navigate(location.pathname);
            });
        } catch (error) {
            console.log(error);
        }
        setLoadingGetDetail(false);
    };

    const handleOpenSigners = () => {
        $("body").append(
            Modal(
                "",
                "modalSignersList",
                " modal-lg",
                "",
                "contentModalSigners"
            )
        );
        ReactDOM.createRoot(document.getElementById("contentModalSigners"), {
            hydrate: true,
        }).render(<ModalSignersList dataDetail={data_detail} />);
        $("#modalSignersList").modal("show");
    };

    useEffect(() => {
        const handleVisibilityChange = () => {
            if (!document.hidden) handleRefresh();
        };

        document.addEventListener("visibilitychange", handleVisibilityChange);

        return () => {
            document.removeEventListener(
                "visibilitychange",
                handleVisibilityChange
            );
        };
    }, []);

    const handleComplete = async () => {
        Swal.fire({
            title: "Apakah Anda yakin?",
            text: `Anda akan menyatakan surat No ${
                data_detail?.no_surat ?? ""
            } sebagai selesai.`,
            icon: "warning",
            html: `
            <div style="text-align: center;">
                <div>Anda akan menyatakan surat No ${
                    data_detail?.no_surat ?? ""
                } sebagai selesai.</div>
                <div class="mt-5">
                    <div for="catatan" class="form-label fs-18">
                        <strong>
                            Berikan catatan jika ada
                        </strong>
                    </div>
                    <textarea
                        class="form-control"
                        rows="3"
                        placeholder="Masukkan catatan"
                        style="width: 100%;"
                        id="catatan"
                        name="catatan"
                    ></textarea>
                </div>
                 <div class="mt-5 d-flex align-items-center justify-content-center gap-4">
                    <button class="btn btn-success btn-lg" id="btnCompletedAcc">
                        <i class="fas fa-check-circle pe-2"></i>
                        Selesai Acc
                    </button>
                    <button class="btn btn-outline-danger btn-lg" id="btnCompletedNotAcc">
                        <i class="fas fa-times-circle pe-2"></i>
                        Selesai Tidak Acc
                    </button>
                    <button class="btn btn-outline-secondary btn-lg" id="btnCloseCompleted">
                        <i class="fas fa-times-circle pe-2"></i>
                        Batal
                    </button>
                </div>
            </div>
        `,
            showCancelButton: false,
            showCloseButton: false,
            showConfirmButton: false,
        });

        document
            .getElementById("btnCompletedAcc")
            .addEventListener("click", async () => {
                try {
                    await completeSurat({
                        is_acc: true,
                    });
                    Swal.close();
                } catch (error) {
                    console.error("Error completing surat with acc:", error);
                }
            });

        document
            .getElementById("btnCompletedNotAcc")
            .addEventListener("click", async () => {
                try {
                    await completeSurat({
                        is_acc: false,
                    });
                    Swal.close();
                } catch (error) {
                    console.error("Error completing surat without acc:", error);
                }
            });
        document
            .getElementById("btnCloseCompleted")
            .addEventListener("click", () => {
                Swal.close();
            });
    };

    const completeSurat = async ({ is_acc = false }) => {
        const csrf_token = document.querySelector('meta[name="csrf-token"]');
        try {
            setLoadingGetDetail(true);
            const catatan = document.getElementById("catatan").value;
            const { data } = await axios.post(
                `/surat-masuk/${data_detail?.id}/complete`,
                {
                    is_acc: is_acc,
                    catatan: catatan,
                },
                {
                    headers: {
                        "X-CSRF-TOKEN": csrf_token.content,
                    },
                }
            );
            if (data?.status) {
                handleRefresh();
                setTimeout(() => {
                    history.goBack();
                });
                Swal.fire(
                    "Berhasil!",
                    `Surat No ${data_detail?.no_surat ?? ""} telah selesai.`,
                    "success"
                );
            }
        } catch (error) {
            console.log(error);
        }
        setLoadingGetDetail(false);
    };

    useEffect(() => {
        $(document).on("hide.bs.modal", "#modalPreview", function () {
            $("#modalPreview").remove();
        });
        getDataKopSurat();
    }, []);

    const handleOpenSignature = (data) => {
        $("body").append(
            Modal(
                "",
                "modalPreview",
                "modal-dialog-centered modal-fullscreen",
                "",
                "contentSignature"
            )
        );
        ReactDOM.createRoot(document.getElementById("contentSignature"), {
            hydrate: true,
        }).render(
            <Signature
                data={data_detail}
            />
        );
        $("#modalPreview").modal("show");
    };

    const handleOpenRevision = () => {
        console.log("open revision");
        $("body").append(
            Modal(
                "",
                "modalPreview",
                "modal-dialog-centered modal-fullscreen",
                "",
                "contentSignature"
            )
        );
        ReactDOM.createRoot(document.getElementById("contentSignature"), {
            hydrate: true,
        }).render(<ModalRevision />);
        $("#modalPreview").modal("show");
    };

    useEffect(() => {
        const handleScroll = () => {
            setScrollY(window.scrollY);
        };

        window.addEventListener("scroll", handleScroll);

        return () => {
            window.removeEventListener("scroll", handleScroll);
        };
    }, []);

    const allDisposisi = data_detail?.child_surat?.filter(
        (item) => parseInt(item.type) === 3
    );
    const direkturHolding =
        parseInt(
            auth_user?.user_unit_bisnis_data?.find(
                (item) => parseInt(item.is_holding) === 1
            )?.is_holding
        ) === 1 && auth_user?.role?.name?.includes("Direktur");

    return (
        <>
            <div className="card m-4">
                <div
                    className="card-header"
                    style={{
                        paddingTop: "22px",
                        paddingBottom: "20px",
                    }}
                >
                    <div className="w-100">
                        <div className="d-flex align-items-center gap-4">
                            <a
                                href={
                                    is_surat_keluar
                                        ? "/surat-keluar"
                                        : "/surat-masuk"
                                }
                            >
                                <i className="fas fa-arrow-left fs-20"></i>
                            </a>
                            <div className="fs-20 fw-medium text-dark title__surat">
                                {data_detail?.perihal}{" "}
                                <span
                                    className="ps-3"
                                    dangerouslySetInnerHTML={{
                                        __html: data_detail?.status_formatted,
                                    }}
                                ></span>
                                {data_detail?.request_esign && (
                                    <span
                                        className="ps-3"
                                        role="button"
                                        onClick={() => handleOpenSigners()}
                                    >
                                        {data_detail?.request_esign
                                            ?.signing_status === "completed" ? (
                                            <span
                                                class="border border-success text-success fw-bold"
                                                style={{
                                                    borderRadius: "20px",
                                                    fontSize: "12px",
                                                    padding: "6px 9px",
                                                }}
                                            >
                                                <i class="fas fa-signature pe-2 align-middle"></i>
                                                Semua sudah ditandatangani
                                            </span>
                                        ) : (
                                            <span
                                                class="btn btn-outline-dark fw-bold d-inline-block"
                                                style={{
                                                    borderRadius: "20px",
                                                    fontSize: "12px",
                                                    padding: "6px 9px",
                                                }}
                                            >
                                                <i class="fas fa-signature pe-2 align-middle"></i>
                                                <span class="align-middle fw-bold fs-14">
                                                    {
                                                        data_detail?.request_esign?.signers?.filter(
                                                            (signer) =>
                                                                signer?.status ===
                                                                "completed"
                                                        ).length
                                                    }{" "}
                                                    dari{" "}
                                                    {
                                                        data_detail
                                                            ?.request_esign
                                                            ?.signers?.length
                                                    }
                                                </span>
                                                &nbsp; Ditandatangani
                                            </span>
                                        )}
                                    </span>
                                )}
                            </div>
                        </div>
                        <div className="w-100 mt-4">
                            <div className="d-flex align-items-center justify-content-start gap-3">
                                {canPermission("Surat Masuk.Complete") &&
                                    data_detail?.status !== "Selesai" && (
                                        <div
                                            className="btn btn-outline-success rounded-3 text-nowrap"
                                            style={{
                                                fontSize: "10px",
                                                padding: "9px 10px",
                                            }}
                                            onClick={() => handleComplete()}
                                        >
                                            <i className="fas fa-check-circle pe-2"></i>
                                            Nyatakan Selesai
                                        </div>
                                    )}
                                {(canPermission("Disposisi.Approver") ||
                                    canPermission("Disposisi.Checker") ||
                                    canPermission("Disposisi.Corsec") ||
                                    canPermission("Disposisi.Full_Akses")) &&
                                    (direkturHolding
                                        ? allDisposisi?.length === 0
                                        : true) && (
                                        <button
                                            type="button"
                                            onClick={() => {
                                                handleOpenSignature()
                                            }}
                                            style={{
                                                fontSize: "10px",
                                                padding: "9px 10px",
                                            }}
                                            className="btn btn-primary rounded-3"
                                        >
                                            {direkturHolding ? (
                                                <>
                                                    <i className="fas fa-user-plus me-2"></i>
                                                    Disposisi
                                                </>
                                            ) : (
                                                <>
                                                    <i className="fas fa-share me-2"></i>
                                                    Teruskan
                                                </>
                                            )}
                                        </button>
                                    )}
                                {canPermission("Surat Masuk.Revisi") && (
                                    <a
                                        style={{
                                            fontSize: "10px",
                                            padding: "9px 10px",
                                        }}
                                        href={`/surat-keluar/${data_detail?.id}/revision?from=reply&reply_to=${lastReplyForward?.id}`}
                                        target="_blank"
                                        className="btn btn-outline-primary rounded-3"
                                    >
                                        <i class="fas fa-pencil me-2"></i>
                                        Revisi Surat
                                    </a>
                                )}
                                <button
                                    type="button"
                                    onClick={() => handleOpenPreview()}
                                    className="btn btn-outline-primary rounded-3"
                                    style={{ fontSize: "10px" }}
                                >
                                    PREVIEW
                                </button>
                                <button
                                    onClick={() => handleOpenDownload()}
                                    className="btn btn-outline-primary rounded-3 text-nowrap"
                                    style={{
                                        fontSize: "10px",
                                        padding: "9px 10px",
                                    }}
                                >
                                    <i className="feather-download pe-2"></i>
                                    Download PDF
                                </button>

                                <div className="btn-group">
                                    <a
                                        href="javascript:void(0)"
                                        class="btn btn-outline-primary rounded-3"
                                        data-bs-toggle="dropdown"
                                        data-bs-offset="0,21"
                                    >
                                        More...
                                    </a>
                                    <ul className="dropdown-menu">
                                        <li>
                                            <a
                                                class="dropdown-item fs-14"
                                                href="javascript:void(0)"
                                                onClick={() =>
                                                    handleOpenHistory()
                                                }
                                            >
                                                <i className="fas fa-history fs-14 pe-2"></i>
                                                History Surat
                                            </a>
                                        </li>
                                        <li class="dropdown-divider"></li>
                                        <li>
                                            <a
                                                class="dropdown-item fs-14"
                                                href="javascript:void(0)"
                                                onClick={() =>
                                                    handleOpenRevision()
                                                }
                                            >
                                                <i className="fas fa-history fs-14 pe-2"></i>
                                                History Revisi
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                <div
                                    className="btn btn-primary rounded-3"
                                    style={{
                                        fontSize: "10px",
                                        padding: "9px 10px",
                                    }}
                                    data-bs-toggle="tooltip"
                                    data-bs-placement="top"
                                    title="Refresh"
                                    data-bs-trigger="hover"
                                    id="refresh-detail"
                                    onClick={() => handleRefresh()}
                                >
                                    <i className="fas fa-refresh"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    className={`card-body ${
                        data_detail?.child_surat?.length > 0 ? "pt-3" : ""
                    }`}
                >
                    <ContentSurat
                        data={data_detail}
                        handleDownload={handleDownload}
                        handleOpenDetail={handleOpenDetail}
                        setShowReplyForward={setShowReplyForward}
                        hasDetail={data_detail?.child_surat?.length > 0}
                        isParent={true}
                    />
                    {/* {data_detail?.child_surat?.length > 0 &&
                        data_detail?.child_surat?.map((item, index) => (
                            <div key={index} id={`detail-surat-${item.id}`}>
                                <ContentSurat
                                    index={index}
                                    data={item}
                                    handleDownload={handleDownload}
                                    handleOpenDetail={handleOpenDetail}
                                    setShowReplyForward={setShowReplyForward}
                                    isLast={
                                        index ===
                                        data_detail?.child_surat?.length - 1
                                    }
                                    hasDetail={
                                        data_detail?.child_surat?.length > 0
                                    }
                                />
                            </div>
                        ))} */}
                    <div
                        className="rounded-3 mt-4"
                        style={{
                            background: "rgb(243 249 255)",
                        }}
                    >
                        <div
                            className="p-4"
                            style={{
                                position: "sticky",
                                top: "200px",
                                zIndex: "1",
                            }}
                        >
                            <h4 className="fw-bold fs-16">
                                Disposisi dan Balasan
                            </h4>
                        </div>
                        <hr />
                        <div className="d-flex flex-column gap-4 mt-5 pb-4 px-4">
                            {data_detail?.child_surat?.length > 0 ? (
                                data_detail?.child_surat?.map((item, index) => (
                                    <div
                                        className="d-flex aling-items-start gap-3"
                                        id={`detail-surat-${item.id}`}
                                        key={index}
                                    >
                                        <div>
                                            <img
                                                src={item?.pengirim?.avatar_url}
                                                alt="Surat Masuk"
                                                className="rounded-circle"
                                                style={{
                                                    width: "40px",
                                                    height: "40px",
                                                    border: "1px solid #D3D3D3",
                                                }}
                                            />
                                        </div>
                                        <div
                                            className="rounded-3 px-4 py-3 w-100"
                                            style={{
                                                backgroundColor: "#EFF0F5",
                                            }}
                                        >
                                            <div>
                                                {parseInt(item.type) === 2 ? (
                                                    <>
                                                        <i className="fas fa-reply fs-12"></i>{" "}
                                                        <span
                                                            className="fs-12 ps-1"
                                                            style={{
                                                                fontStyle:
                                                                    "italic",
                                                            }}
                                                        >
                                                            Balasan
                                                        </span>
                                                    </>
                                                ) : parseInt(item.type) ===
                                                  3 ? (
                                                    <>
                                                        <i className="fas fa-share fs-12"></i>{" "}
                                                        <span
                                                            className="fs-12 ps-1"
                                                            style={{
                                                                fontStyle:
                                                                    "italic",
                                                            }}
                                                        >
                                                            Disposisi
                                                        </span>
                                                    </>
                                                ) : parseInt(item.type) ===
                                                  4 ? (
                                                    <>
                                                        <i className="fas fa-pencil fs-12"></i>{" "}
                                                        <span
                                                            className="fs-12 ps-1"
                                                            style={{
                                                                fontStyle:
                                                                    "italic",
                                                            }}
                                                        >
                                                            Revisi
                                                        </span>
                                                    </>
                                                ) : (
                                                    ""
                                                )}
                                            </div>
                                            <div className="d-flex align-items-start justify-content-between">
                                                <div>
                                                    <div className="fs-15 fw-bold text-dark">
                                                        {item?.pengirim?.nama}{" "}
                                                        <span className="text-dark fw-normal fs-14">
                                                            {`<${item?.pengirim?.email}>`}
                                                        </span>
                                                    </div>
                                                    <div
                                                        className="fs-12 text-muted fw-normal position-relative email__to"
                                                        style={{
                                                            fontStyle: "italic",
                                                        }}
                                                    >
                                                        kepada{" "}
                                                        {item?.tujuan_surat
                                                            ?.length > 1
                                                            ? item?.tujuan_surat?.map(
                                                                  (
                                                                      tujuan,
                                                                      index
                                                                  ) => {
                                                                      return (
                                                                          <span
                                                                              key={
                                                                                  index
                                                                              }
                                                                          >
                                                                              {tujuan
                                                                                  ?.user
                                                                                  ?.nama ??
                                                                                  tujuan
                                                                                      ?.role
                                                                                      ?.name}
                                                                              {index <
                                                                              item
                                                                                  ?.tujuan_surat
                                                                                  ?.length -
                                                                                  1
                                                                                  ? ", "
                                                                                  : ""}
                                                                          </span>
                                                                      );
                                                                  }
                                                              )
                                                            : item
                                                                  ?.tujuan_surat?.[0]
                                                                  ?.role_id ===
                                                                  auth_user?.role_id ||
                                                              item
                                                                  ?.tujuan_surat?.[0]
                                                                  ?.user_id ===
                                                                  auth_user?.id
                                                            ? "saya"
                                                            : item
                                                                  ?.tujuan_surat?.[0]
                                                                  ?.user
                                                                  ?.nama ??
                                                              item
                                                                  ?.tujuan_surat?.[0]
                                                                  ?.role?.name}
                                                    </div>
                                                </div>
                                                <div className="d-flex align-items-center gap-2">
                                                    <div className="fs-14 text-secondary">
                                                        {item?.date_formatted} (
                                                        {item?.created_at_human}
                                                        )
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="my-3">
                                                {parseInt(item?.type) === 2 ||
                                                parseInt(item?.type) === 3 ||
                                                parseInt(item?.type) === 4 ? (
                                                    <div>
                                                        <div
                                                            dangerouslySetInnerHTML={{
                                                                __html: item
                                                                    ?.content_surat
                                                                    ?.full_content,
                                                            }}
                                                        ></div>
                                                        {(() => {
                                                            let type_bg = "";
                                                            let type_name = "";
                                                            const status =
                                                                item?.status;
                                                            const approve_status =
                                                                parseInt(
                                                                    item?.approve_status
                                                                );

                                                            if (
                                                                status ===
                                                                "Disposisi"
                                                            )
                                                                return;

                                                            if (
                                                                !item?.approve_status
                                                            )
                                                                return;

                                                            if (
                                                                status ===
                                                                "Selesai"
                                                            ) {
                                                                type_bg =
                                                                    "success";
                                                                type_name =
                                                                    "Selesai";
                                                            }

                                                            switch (
                                                                approve_status
                                                            ) {
                                                                case 1:
                                                                    type_bg =
                                                                        "success";
                                                                    type_name =
                                                                        item?.approve_status_name;
                                                                    break;
                                                                case 2:
                                                                    type_bg =
                                                                        "warning";
                                                                    type_name =
                                                                        item?.approve_status_name;
                                                                    break;
                                                                case 3:
                                                                    type_bg =
                                                                        "danger";
                                                                    type_name =
                                                                        item?.approve_status_name;
                                                                    break;
                                                            }

                                                            let is_acc = null;
                                                            if (
                                                                status ===
                                                                "Selesai"
                                                            ) {
                                                                if (
                                                                    parseInt(
                                                                        data_detail?.is_acc_complete
                                                                    ) === 1
                                                                ) {
                                                                    type_bg =
                                                                        "success";
                                                                    type_name =
                                                                        "Selesai";
                                                                    is_acc =
                                                                        "Disetujui";
                                                                } else {
                                                                    type_bg =
                                                                        "danger";
                                                                    type_name =
                                                                        "Selesai";
                                                                    is_acc =
                                                                        "Tidak Disetujui";
                                                                }
                                                            }

                                                            return (
                                                                <div
                                                                    className={`text-bg-${type_bg} text-white py-2 px-3 fw-medium rounded-2 fs-14`}
                                                                >
                                                                    Surat{" "}
                                                                    <strong>
                                                                        {
                                                                            type_name
                                                                        }
                                                                    </strong>{" "}
                                                                    {is_acc ? (
                                                                        <strong>
                                                                            {
                                                                                is_acc
                                                                            }
                                                                        </strong>
                                                                    ) : (
                                                                        ""
                                                                    )}{" "}
                                                                    oleh{" "}
                                                                    {item
                                                                        .pengirim
                                                                        ?.role
                                                                        ?.name ??
                                                                        ""}{" "}
                                                                    -{" "}
                                                                    {
                                                                        item
                                                                            ?.pengirim
                                                                            ?.nama
                                                                    }{" "}
                                                                    pada tanggal{" "}
                                                                    {
                                                                        item?.date_formatted
                                                                    }
                                                                </div>
                                                            );
                                                        })()}
                                                    </div>
                                                ) : (
                                                    ""
                                                )}
                                                <DetailContentSurat
                                                    data={item}
                                                    contentSurat={
                                                        parseInt(item?.type) ===
                                                            2 ||
                                                        parseInt(item?.type) ===
                                                            3 ||
                                                        parseInt(item?.type) ===
                                                            4
                                                            ? item
                                                                  ?.content_surat
                                                                  ?.original_content
                                                            : item
                                                                  ?.content_surat
                                                                  ?.content_replace
                                                    }
                                                    handleOpenDetail={
                                                        handleOpenDetail
                                                    }
                                                    handleDownload={
                                                        handleDownload
                                                    }
                                                    isReplyForward={
                                                        parseInt(item?.type) ===
                                                            2 ||
                                                        parseInt(item?.type) ===
                                                            3 ||
                                                        parseInt(item?.type) ===
                                                            4
                                                    }
                                                />
                                            </div>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <div className="d-flex justify-content-center align-items-center gap-md-3">
                                    <div className="d-flex align-items-center gap-2">
                                        <div>
                                            <i className="feather feather-alert-circle fs-26 fs-7 text-muted"></i>
                                        </div>
                                        <div className="fs-md-16 fs-8 fw-bold">
                                            Belum ada disposisi atau balasan
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                        <div className="px-4">
                            {!showReplyForward?.show && (
                                <div
                                    style={{
                                        paddingLeft: "55px",
                                    }}
                                >
                                    <hr />
                                    <div className="d-flex align-items-center mt-4 gap-3 pb-3">
                                        {canPermission("Surat Masuk.Reply") && (
                                            <button
                                                type="button"
                                                onClick={() => {
                                                    setShowReplyForward({
                                                        show: true,
                                                        type: "reply",
                                                    });
                                                }}
                                                className="btn btn-outline-dark rounded-3"
                                            >
                                                <i className="fas fa-reply me-2"></i>{" "}
                                                Balas
                                            </button>
                                        )}
                                    </div>
                                </div>
                            )}
                            {showReplyForward?.show && (
                                <ReplyForward
                                    setShowReplyForward={setShowReplyForward}
                                    showReplyForward={showReplyForward}
                                />
                            )}
                        </div>
                    </div>
                </div>
            </div>
            {/* <div className="position-absolute top-0 end-0 start-0 w-100 h-100"></div> */}
            {loadingGetDetail && (
                <div
                    className=""
                    style={{
                        position: "fixed",
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.5)",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        height: "100vh",
                        width: "100vw",
                        overflow: "hidden",
                        pointerEvents: "none",
                        zIndex: 9999999999,
                    }}
                >
                    <div
                        id="example_processing"
                        className="dataTables_processing card"
                    >
                        <div>
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                    </div>
                </div>
            )}
            {!showReplyForward?.show &&
                (scrollY === 0 ? (
                    <button
                        style={{
                            position: "fixed",
                            bottom: "20px",
                            right: "20px",
                            zIndex: 1050,
                            display: "block",
                            padding: "10px 20px",
                            backgroundColor: "white",
                            color: "#000",
                            borderRadius: "20px",
                            cursor: "pointer",
                            boxShadow: "0 0 10px rgba(0, 0, 0, 0.1)",
                            border: "1px solid rgb(222 222 222)",
                        }}
                        onClick={() => {
                            const lastId = data_detail?.child_surat?.length
                                ? data_detail?.child_surat[
                                      data_detail?.child_surat?.length - 1
                                  ].id
                                : data_detail?.id;
                            const lastMessageElement = document.getElementById(
                                `detail-surat-${lastId}`
                            );
                            if (lastMessageElement) {
                                lastMessageElement.scrollIntoView({
                                    behavior: "smooth",
                                });
                            }
                        }}
                    >
                        <i className="fas fa-arrow-down me-2"></i>
                        Pesan Terakhir
                    </button>
                ) : (
                    <button
                        style={{
                            position: "fixed",
                            bottom: "60px",
                            right: "20px",
                            zIndex: 1050,
                            display: "block",
                            padding: "10px 20px",
                            backgroundColor: "white",
                            color: "#000",
                            borderRadius: "20px",
                            cursor: "pointer",
                            boxShadow: "0 0 10px rgba(0, 0, 0, 0.1)",
                            border: "1px solid rgb(222 222 222)",
                        }}
                        onClick={() =>
                            window.scrollTo({ top: 0, behavior: "smooth" })
                        }
                    >
                        <i className="fas fa-arrow-up"></i>
                        <span className="ms-2">Kembali ke Atas</span>
                    </button>
                ))}
        </>
    );
};

export default DetailSuratMasuk;
