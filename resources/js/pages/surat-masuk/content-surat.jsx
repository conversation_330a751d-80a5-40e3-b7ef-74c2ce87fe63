import React, { useEffect, useState } from "react";
import { handleRender } from "../../helper";
import DetailContentSurat from "./detail-content-surat";

export default function ContentSurat({
    data,
    setShowReplyForward,
    handleOpenDetail,
    handleDownload,
    isLast,
    hasDetail,
    isParent,
}) {
    const [showDetail, setShowDetail] = useState(
        !hasDetail || isLast || isParent || false
    );
    const handleShowHideDetail = async () => {
        setShowDetail(!showDetail);
    };

    useEffect(() => {
        handleRender();
    }, [showDetail]);

    const list_detail_surat = [
        {
            title: "Dari",
            value: () => {
                return (
                    <>
                        {data?.pengirim?.nama}{" "}
                        <span className="text-secondary fw-normal">{`<${data?.pengirim?.email}>`}</span>{" "}
                        ({data?.pengirim?.role?.name})
                    </>
                );
            },
        },
        {
            title: "<PERSON>pada",
            value: () => {
                return (
                    <>
                        {data?.tujuan_surat?.map((tujuan, index) => (
                            <div key={index}>
                                {tujuan?.user?.nama ?? tujuan?.role?.name}{" "}
                                {tujuan?.user?.email
                                    ? `<${tujuan?.user?.email}>`
                                    : ""}
                                {index < data?.tujuan_surat?.length - 1
                                    ? ", "
                                    : ""}
                            </div>
                        ))}
                    </>
                );
            },
        },
        {
            title: "Tanggal",
            value: () => {
                return (
                    <>
                        {data?.date_formatted} ({data?.created_at_human})
                    </>
                );
            },
        },
        {
            title: "Perihal",
            value: () => {
                return data?.perihal;
            },
        },
        {
            title: "No. Surat",
            value: () => {
                return data?.no_surat ?? "-";
            },
        },
        {
            title: "Status",
            value: () => {
                return (
                    <span
                        dangerouslySetInnerHTML={{
                            __html: data?.approve_status
                                ? data?.approve_status_name_formatted
                                : data?.status_formatted,
                        }}
                    ></span>
                );
            },
        },
    ];

    return (
        <div>
            {hasDetail && !showDetail ? (
                <div
                    className={`${!isParent ? " py-3" : "pb-3"}`}
                    role="button"
                    onClick={() => {
                        handleShowHideDetail();
                    }}
                >
                    <div
                        role="button"
                        className={`d-flex align-items-center justify-content-between`}
                    >
                        <div className="d-flex align-items-center gap-3">
                            <div>
                                <img
                                    src={data?.pengirim?.avatar_url}
                                    alt="Surat Masuk"
                                    className="rounded-circle"
                                    style={{ width: "40px", height: "40px" }}
                                />
                            </div>
                            <div>
                                <div className="fs-15 fw-bold text-dark">
                                    {data?.pengirim?.nama}{" "}
                                    <span className="text-dark fw-normal fs-14">
                                        {`<${data?.pengirim?.email}>`}
                                    </span>
                                </div>
                                <div className="fs-13 text-secondary position-relative email__to">
                                    kepada{" "}
                                    {data?.tujuan_surat?.length > 1
                                        ? data?.tujuan_surat?.map(
                                              (tujuan, index) => {
                                                  return (
                                                      <span key={index}>
                                                          {tujuan?.user?.nama ??
                                                              tujuan?.role
                                                                  ?.name}
                                                          {index <
                                                          data?.tujuan_surat
                                                              ?.length -
                                                              1
                                                              ? ", "
                                                              : ""}
                                                      </span>
                                                  );
                                              }
                                          )
                                        : data?.tujuan_surat?.[0]?.role_id ===
                                              auth_user?.role_id ||
                                          data?.tujuan_surat?.[0]?.user_id ===
                                              auth_user?.id
                                        ? "saya"
                                        : data?.tujuan_surat?.[0]?.user?.nama ??
                                          data?.tujuan_surat?.[0]?.role?.name}
                                </div>
                            </div>
                        </div>
                        <div className="d-flex align-items-center gap-2">
                            <div className="fs-14 text-secondary">
                                {data?.date_formatted} ({data?.created_at_human}
                                )
                            </div>
                        </div>
                    </div>
                    {!isParent && (
                        <div
                            style={{
                                paddingLeft: "55px",
                                marginTop: "15px",
                            }}
                            className="text-dark"
                        >
                            {data?.content_surat?.content_replace
                                .replace(/<[^>]+>/g, "")
                                .replace(/&nbsp;/g, "")}
                        </div>
                    )}
                </div>
            ) : (
                <div
                    onClick={() => {
                        if (!hasDetail || isLast) return;
                        handleShowHideDetail();
                    }}
                    role="button"
                    className={`d-flex align-items-center justify-content-between ${
                        hasDetail ? " pt-3" : ""
                    }`}
                >
                    <div className="d-flex align-items-center gap-3">
                        <div>
                            <img
                                src={data?.pengirim?.avatar_url}
                                alt="Surat Masuk"
                                className="rounded-circle"
                                style={{ width: "40px", height: "40px" }}
                            />
                        </div>
                        <div>
                            <div className="fs-15 fw-bold text-dark">
                                {data?.pengirim?.nama}{" "}
                                <span className="text-dark fw-normal fs-14">
                                    {`<${data?.pengirim?.email}>`} (
                                    {data?.pengirim?.role?.name})
                                </span>
                            </div>
                            <div className="fs-13 text-secondary position-relative email__to">
                                kepada{" "}
                                {data?.tujuan_surat?.length > 1
                                    ? data?.tujuan_surat?.map(
                                          (tujuan, index) => {
                                              return (
                                                  <span key={index}>
                                                      {tujuan?.user?.nama ??
                                                          tujuan?.role?.name}
                                                      {index <
                                                      data?.tujuan_surat
                                                          ?.length -
                                                          1
                                                          ? ", "
                                                          : ""}
                                                  </span>
                                              );
                                          }
                                      )
                                    : data?.tujuan_surat?.[0]?.role_id ===
                                          auth_user?.role_id ||
                                      data?.tujuan_surat?.[0]?.user_id ===
                                          auth_user?.id
                                    ? "saya"
                                    : data?.tujuan_surat?.[0]?.user?.nama ??
                                      data?.tujuan_surat?.[0]?.role?.name}
                                <i className="fas fa-caret-down ms-2"></i>
                                <div className="position-absolute detail__email_to">
                                    <div
                                        className="bg-white border rounded-3 shadow-sm"
                                        style={{
                                            minWidth: "500px",
                                            width: "100%",
                                        }}
                                    >
                                        <div className="p-3">
                                            <table className="table table-hover fs-14">
                                                <tbody>
                                                    {list_detail_surat.map(
                                                        (item, index) => (
                                                            <tr key={index}>
                                                                <td
                                                                    className="fw-bold text-end"
                                                                    style={{
                                                                        width: "100px",
                                                                    }}
                                                                >
                                                                    {item.title}
                                                                </td>
                                                                <td className="">
                                                                    {item.value()}
                                                                </td>
                                                            </tr>
                                                        )
                                                    )}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="d-flex align-items-center gap-2">
                        <div className="fs-14 text-secondary">
                            {data?.date_formatted} ({data?.created_at_human})
                        </div>
                        <div>
                            <div
                                onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    setShowReplyForward({
                                        show: true,
                                        type: "reply",
                                    });
                                }}
                                className="mx-3"
                                role="button"
                            >
                                <i className="fas fa-reply"></i>
                            </div>
                        </div>
                    </div>
                </div>
            )}
            {showDetail && (
                <div
                    className="my-4"
                    style={{
                        paddingLeft: "55px",
                    }}
                >
                    {parseInt(data?.type) === 2 ||
                    parseInt(data?.type) === 3 ||
                    parseInt(data?.type) === 4 ? (
                        <div>
                            <div
                                dangerouslySetInnerHTML={{
                                    __html: data?.content_surat?.full_content,
                                }}
                            ></div>
                            {(() => {
                                let type_bg = "";
                                let type_name = "";
                                const status = data?.status;
                                const approve_status = parseInt(
                                    data?.approve_status
                                );

                                if (status === "Disposisi") return;
                                if (!data?.approve_status) return

                                if (status === "Selesai") {
                                    type_bg = "success";
                                    type_name = "Selesai";
                                }

                                switch (approve_status) {
                                    case 1:
                                        type_bg = "success";
                                        type_name = data?.approve_status_name;
                                        break;
                                    case 2:
                                        type_bg = "warning";
                                        type_name = data?.approve_status_name;
                                        break;
                                    case 3:
                                        type_bg = "danger";
                                        type_name = data?.approve_status_name;
                                        break;
                                }

                                let is_acc = null;
                                if(status === "Selesai") {
                                    if (parseInt(data_detail?.is_acc_complete) === 1) {
                                        type_bg = "success";
                                        type_name = "Selesai";
                                        is_acc = "Disetujui";
                                    } else {
                                        type_bg = "danger";
                                        type_name = "Selesai";
                                        is_acc = "Tidak Disetujui";
                                    }
                                }

                                return (
                                    <div
                                        className={`text-bg-${type_bg} text-white py-2 px-3 fw-medium rounded-2 fs-14`}
                                    >
                                        Surat <strong>{type_name}</strong>{" "}
                                        {is_acc ? (
                                            <strong>{is_acc}</strong>
                                        ) : (
                                            ""
                                        )}
                                        {" "}
                                        oleh {data.pengirim?.role?.name ?? ""} -{" "}
                                        {data?.pengirim?.nama} pada tanggal{" "}
                                        {data?.date_formatted}
                                    </div>
                                );
                            })()}
                        </div>
                    ) : (
                        ""
                    )}
                    <DetailContentSurat
                        data={data}
                        contentSurat={
                            parseInt(data?.type) === 2 ||
                            parseInt(data?.type) === 3 ||
                            parseInt(data?.type) === 4
                                ? data?.content_surat?.original_content
                                : data?.content_surat?.content_replace
                        }
                        handleOpenDetail={handleOpenDetail}
                        handleDownload={handleDownload}
                        isReplyForward={
                            parseInt(data?.type) === 2 ||
                            parseInt(data?.type) === 3 ||
                            parseInt(data?.type) === 4
                        }
                    />
                </div>
            )}
            {!isLast && hasDetail && (
                <div className="border-bottom border-light "></div>
            )}
        </div>
    );
}
