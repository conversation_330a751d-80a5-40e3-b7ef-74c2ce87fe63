import React, { useState, useEffect, useRef } from "react";
import { useParams } from "react-router-dom";
import { canPermission, handleRender } from "../../helper";
import FormReply from "./form-reply";
import Swal from "sweetalert2";
import ContentHistory from "../surat-keluar/content-history";
import ReactDOM from "react-dom/client";
import Modal from "../../components/modal";
import IndexEsign from "./set-esign";
import Signer from "./set-esign-manual/stepThree/signer";
import RenderReactDom from "react-dom";
import html2canvas from "html2canvas";

export default function ReplyForward({
    setShowReplyForward,
    showReplyForward,
}) {
    const { id } = useParams();
    const attachmentRequiredSign = data_detail?.attachment_file_surat?.filter(
        (item) => parseInt(item.required_signature) === 1
    );
    const [esignData, setEsignData] = useState([]);
    const csrf_token = document.querySelector('meta[name="csrf-token"]');
    const [esignSigner, setEsignSigner] = useState(null);

    const replyForwardRef = useRef(null);
    const editorRef = useRef(null);
    const [errors, setErrors] = useState({});
    const [attachmentFile, setAttachmentFile] = useState([]);
    const [progressAttachment, setProgressAttachment] = useState([]);
    const [userData, setUserData] = useState({});
    const [loadingSubmit, setLoadingSubmit] = useState(false);
    const [formData, setFormData] = useState({
        kepada: [],
        content_surat: "",
        type_message: "",
        approve_status: "",
        original_content: "",
    });

    const getDataFor = async ({ unit_bisnis_id = null }) => {
        try {
            if (userData?.[`${unit_bisnis_id}`]?.length > 0) return;

            const { data } = await axios.get(`/pengguna/list-group`, {
                headers: {
                    "X-CSRF-TOKEN": csrf_token.content,
                },
                params: {
                    unit_bisnis_id,
                },
            });
            data.map((item) => {
                item.value = item.id;
                delete item.id;
                return item;
            });

            const data_pengirim = data_detail?.pengirim;
            if (data_pengirim && showReplyForward?.type === "reply") {
                setFormData((currentData) => ({
                    ...currentData,
                    kepada: data.filter(
                        (item) => item.value === data_pengirim.id
                    ),
                }));
            }

            userData[unit_bisnis_id] = data;
            setUserData(userData);
        } catch (error) {
            console.log(error);
        }
    };

    useEffect(() => {
        if (data_detail?.request_esign?.id) {
            const signers = data_detail?.request_esign?.signers?.find(
                (item) => item.email === auth_user?.email
            );
            setEsignSigner(signers);
        }
    }, []);

    const handleChanges = (e) => {
        setFormData((currentData) => ({
            ...currentData,
            [e.target.name]: e.target.value,
        }));
    };

    const handleAttachmentFile = async (e) => {
        const files = e.target.files;
        if (files.length) {
            const uploadFiles = Array.from(files).map(async (file) => {
                const formData = new FormData();
                formData.append("file", file);

                const uniqueFileId = `file-${Date.now()}-${file.name}`;

                // Inisialisasi progress file jika belum ada
                setProgressAttachment((currentProgress) => {
                    const existingFileIndex = currentProgress.findIndex(
                        (up) => up.id === uniqueFileId
                    );
                    if (existingFileIndex === -1) {
                        return [
                            ...currentProgress,
                            {
                                id: uniqueFileId,
                                name: file.name,
                                file_size: file.size,
                                progress: 0,
                                status: "uploading",
                            },
                        ];
                    } else {
                        return currentProgress;
                    }
                });

                try {
                    const upload = await axios.post(
                        `/file-manager/upload`,
                        formData,
                        {
                            onUploadProgress: (progressEvent) => {
                                const percentCompleted = Math.round(
                                    (progressEvent.loaded * 100) /
                                        progressEvent.total
                                );
                                // Update progress pada state
                                setProgressAttachment((currentProgress) =>
                                    currentProgress.map((up) => {
                                        if (up.id === uniqueFileId) {
                                            return {
                                                ...up,
                                                progress: percentCompleted,
                                                status: "uploading",
                                            };
                                        }
                                        return up;
                                    })
                                );
                            },
                        }
                    );
                    // Update state setelah file terupload
                    setProgressAttachment((currentProgress) =>
                        currentProgress.map((up) => {
                            if (up.id === uniqueFileId) {
                                return {
                                    ...up,
                                    status: "uploaded",
                                    progress: 100,
                                };
                            }
                            return up;
                        })
                    );
                    // Menyimpan response dari upload ke state
                    setAttachmentFile((currentFiles) => [
                        ...currentFiles,
                        {
                            id: uniqueFileId,
                            ...file,
                            data: {
                                url: upload.data?.url,
                                name_original: upload.data?.name_original,
                                size: upload.data?.size,
                                mime_type: upload.data?.mime_type,
                            },
                        },
                    ]);
                } catch (error) {
                    setProgressAttachment((currentProgress) =>
                        currentProgress.map((up) => {
                            if (up.id === uniqueFileId) {
                                return { ...up, status: "error" };
                            }
                            return up;
                        })
                    );
                }
            });

            Promise.all(uploadFiles).then(() => {});
        }
    };

    const resetForm = () => {
        setFormData({
            kepada: [],
            content_surat: "",
            attachment: [],
            approve_status: "",
        });
        setAttachmentFile([]);
        setProgressAttachment([]);
        setErrors({});
    };

    const handleSubmit = async () => {
        formData.content_surat = editorRef.current?.getContent() ?? "";
        formData.attachment = attachmentFile;
        formData.type_message = showReplyForward.type;
        formData.original_content = data_detail?.content_surat?.content;
        formData.surat_id = id;
        formData.perihal = data_detail?.perihal;
        if (showReplyForward.type === "reply") {
            formData.kepada = [{ value: lastReplyForward?.pengirim?.id }];
            formData.reply_to = lastReplyForward?.id;
        }

        const errors = validateForm(formData);
        if (Object.keys(errors).length && showReplyForward.type !== "forward") {
            return;
        }

        try {
            setLoadingSubmit(true);
            if (Object.keys(esignData).length > 0) {
                const requestEsign = await submitRequestEsign();
                if (!requestEsign?.status) {
                    setLoadingSubmit(false);
                    return;
                }
            }

            const { data } = await axios.post("/surat-keluar/reply", formData, {
                headers: {
                    "X-CSRF-TOKEN": csrf_token.content,
                },
            });

            if (data?.status) {
                // if (dataSignature?.documentComplete.length > 0) {
                //     console.log("submitEsignManual", data);
                //     const resEsign = await submitEsignManual(data?.signatures);
                //     if (!resEsign.status) {
                //         Swal.fire({
                //             title: "Gagal",
                //             text: resEsign.message,
                //             icon: "error",
                //             showConfirmButton: false,
                //         });
                //         setLoadingSubmit(false);
                //         return;
                //     }
                // }

                resetForm();
                setShowReplyForward({
                    show: false,
                    type: "",
                });
                // data_detail = data?.data;
                $('#refresh-detail').click();
                Swal.fire({
                    title: "Berhasil",
                    text: data?.message,
                    icon: "success",
                    showConfirmButton: false,
                });
            } else {
                Swal.fire({
                    title: "Gagal",
                    text: resEsign.message,
                    icon: "error",
                    showConfirmButton: false,
                });
            }

            if (data?.error) {
                setErrors(data?.errors ?? {});
            }
        } catch (error) {
            Swal.fire({
                title: "Gagal",
                text: error?.response?.data?.message,
                icon: "error",
                showConfirmButton: false,
            });
        }
        setLoadingSubmit(false);
    };

    const submitEsignManual = async (dataSignatures) => {
        let collectSignatures = [];
        for (const item of dataSignatures) {
            const container = document.createElement("div");
            document.body.appendChild(container);

            item.type = parseInt(item.type_esign);
            item.fullName = item.type === 2 ? item.value_type_esign : "";
            item.signature =
                item.type === 1 ? item.value_type_esign : item.barcode;
            RenderReactDom.render(<Signer data={item} />, container);

            const canvas = await html2canvas(container, {
                backgroundColor: null,
            });
            const base64Image = canvas.toDataURL("image/png");

            collectSignatures.push({
                id: item.id,
                base_64_image: base64Image,
            });
            document.body.removeChild(container);
        }

        try {
            const { data: dataEsign } = await axios.post(
                `/surat-keluar/disposisi/esign-manual`,
                {
                    signatures: collectSignatures,
                    surat_id: data_detail?.id,
                },
                {
                    headers: {
                        "X-CSRF-TOKEN": csrf_token.content,
                    },
                }
            );

            return dataEsign;
        } catch (error) {
            Swal.fire({
                title: "Gagal",
                text: error?.response?.data?.message,
                icon: "error",
                showConfirmButton: false,
            });
            console.log(error);
        }
    };

    const submitRequestEsign = async () => {
        const { data: dataEsign } = await axios.post(
            `/surat-masuk/${data_detail?.id}/e-sign`,
            {
                ...esignData,
                surat_id: data_detail?.id,
            },
            {
                headers: {
                    "X-CSRF-TOKEN": csrf_token.content,
                },
            }
        );
        if (dataEsign?.status) {
            Swal.fire({
                title: "Berhasil",
                text: `${dataEsign?.message}. Jangan berpindah halaman, silahkan tunggu sampai proses disposisi selesai!`,
                icon: "success",
                showConfirmButton: false,
            });
        } else {
            Swal.fire({
                title: "Gagal",
                text: dataEsign?.message,
                icon: "error",
                showConfirmButton: false,
            });
        }

        return dataEsign;
    };

    const validateForm = (data) => {
        const errors = {};
        const rules = {
            kepada: {
                required: "Tujuan email harus diisi",
            },
            content_surat: {
                required: "Isi surat harus diisi",
            },
        };

        Object.keys(rules).forEach((field) => {
            const value = data[field];
            const rule = rules[field];
            if (rule.required) {
                if (Array.isArray(value)) {
                    if (!value.length) errors[field] = rule.required;
                } else if (typeof value === "object" && value !== null) {
                    if (Object.keys(value).length === 0)
                        errors[field] = rule.required;
                } else if (!value) {
                    errors[field] = rule.required;
                }
            }
        });

        setErrors(errors);

        return errors;
    };

    useEffect(() => {
        if (showReplyForward) {
            replyForwardRef.current?.scrollIntoView({
                behavior: "smooth",
                block: "end",
            });
            handleRender();
        }
    }, [showReplyForward]);


    const handleOpenSetEsign = () => {
        $("body").append(
            Modal(
                "",
                "modalPreview",
                "modal-dialog-centered modal-fullscreen",
                "",
                "contentSignature"
            )
        );
        ReactDOM.createRoot(document.getElementById("contentSignature"), {
            hydrate: true,
        }).render(
            <IndexEsign setEsignData={setEsignData} esignData={esignData} />
        );
        $("#modalPreview").modal("show");
    };

    return (
        <>
            <hr />
            <div className="w-100 mt-4" ref={replyForwardRef}>
                <div className="d-flex align-items-start gap-3 w-100">
                    <div>
                        <img
                            src={auth_user?.avatar_url}
                            alt="Surat Masuk"
                            className="rounded-circle"
                            style={{ width: "40px", height: "40px" }}
                        />
                    </div>
                    <div className="w-100">
                        <div className="pb-4">
                            <FormReply
                                getDataFor={getDataFor}
                                userData={userData}
                                formData={formData}
                                setFormData={setFormData}
                                handleChanges={handleChanges}
                                errors={errors}
                                showReplyForward={showReplyForward}
                                editorRef={editorRef}
                                setAttachmentFile={setAttachmentFile}
                                progressAttachment={progressAttachment}
                                setProgressAttachment={setProgressAttachment}
                            />
                        </div>
                        <div className="py-3 d-flex align-items-start justify-content-end border-top">
                            <div className="d-flex align-items-start gap-3">
                                {showReplyForward?.type === "reply" && (
                                    <>
                                        <input
                                            type="file"
                                            className="d-none"
                                            id="attachment_file"
                                            multiple
                                            onChange={handleAttachmentFile}
                                            accept="application/pdf, image/*, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .txt"
                                        />
                                        <label
                                            htmlFor="attachment_file"
                                            className="btn btn-icon"
                                            data-bs-toggle="tooltip"
                                            data-bs-trigger="hover"
                                            title="Upload Attachments"
                                        >
                                            <i className="feather-paperclip"></i>
                                        </label>
                                    </>
                                )}
                                <button
                                    disabled={loadingSubmit}
                                    type="button"
                                    onClick={() => {
                                        if (
                                            esignSigner &&
                                            esignSigner?.status !== "completed"
                                        ) {
                                            Swal.fire({
                                                title: "Peringatan",
                                                text: "Surat ini perlu tanda tangan!. Anda belum menandatangani surat, silahkan tandatangani surat terlebih dahulu",
                                                icon: "warning",
                                                showConfirmButton: false,
                                                timer: 3000,
                                            });
                                        } else {
                                            if (loadingSubmit) return;
                                            handleSubmit();
                                        }
                                    }}
                                    style={{ fontSize: "14px" }}
                                    className="btn btn-primary"
                                    data-bs-toggle="tooltip"
                                    data-bs-trigger="hover"
                                    title="Kirim"
                                >
                                    {loadingSubmit ? (
                                        <i className="fas fa-spinner fa-spin me-2"></i>
                                    ) : (
                                        <i className="fas fa-paper-plane pe-2"></i>
                                    )}
                                    Kirim{" "}
                                </button>
                            </div>
                        </div>
                    </div>
                    {showReplyForward?.type === "reply" && (
                        <div>
                            <button
                                type="button"
                                onClick={() => {
                                    setShowReplyForward({
                                        show: false,
                                        type: "",
                                    });
                                    setFormData({
                                        kepada: [],
                                        content_surat: "",
                                        attachment: [],
                                        approve_status: "",
                                    });
                                }}
                                className="avatar-text avatar-sm p-3 bg-danger"
                            >
                                <i className="fas fa-trash-alt fs-14 text-white"></i>
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
