import React, { useState, useEffect, useRef } from 'react';
import Draggable from 'react-draggable';
import { PDFDocument } from 'pdf-lib';
import 'pdfjs-dist/build/pdf.min.mjs'
import 'pdfjs-dist/build/pdf.worker.min.mjs'

const App = () => {
  const [pdfFile, setPdfFile] = useState('');
  const [eMaterai, setEMaterai] = useState(null);
  const canvasRef = useRef(null);
  const dragRef = useRef(null);
  const position = useRef({ x: 0, y: 0 });

  // Fungsi untuk menangani unggahan file PDF
  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setPdfFile(file);
    }
  };

  useEffect(() => {
    const fetchPdfFile = async () => {
      try {
        const response = await axios.get('http://127.0.0.1:8000/surat-masuk/render-pdf/9d062b53-e6da-46eb-b76f-139d84d26cae', {
          responseType: 'blob'
        });
        setPdfFile(response.data);
      } catch (error) {
        console.error("Gagal mengambil file PDF:", error);
      }
    };

    fetchPdfFile();
  }, []);

  // Fungsi untuk mengunggah gambar eMaterai
  const handleEMateraiUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const url = URL.createObjectURL(file);
      setEMaterai(url);
    }
  };

  // Fungsi untuk menampilkan PDF ke canvas
  const renderPDFToCanvas = async () => {
    if (!pdfFile) return;

    const fileReader = new FileReader();
    fileReader.onload = async function() {
      const typedArray = new Uint8Array(this.result);
      const pdf = await pdfjsLib.getDocument(typedArray).promise;
      const page = await pdf.getPage(1); // Render halaman pertama

      const viewport = page.getViewport({ scale: 1.5 }); // Skalakan sesuai kebutuhan
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');

      canvas.height = viewport.height;
      canvas.width = viewport.width;

      const renderContext = {
        canvasContext: context,
        viewport: viewport,
      };

      await page.render(renderContext).promise;
    };

    fileReader.readAsArrayBuffer(pdfFile);
  };

  // Panggil fungsi renderPDFToCanvas jika ada file PDF yang dipilih
  useEffect(() => {
    renderPDFToCanvas();
  }, [pdfFile]);

  // Fungsi untuk menyimpan posisi drag
  const handleDragStop = (e, data) => {
    position.current = { x: data.x, y: data.y };
  };

  // Fungsi untuk menyematkan eMaterai ke PDF
  const embedEMateraiToPdf = async () => {
    const fileArrayBuffer = await pdfFile.arrayBuffer();
    const pdfDoc = await PDFDocument.load(fileArrayBuffer);
    const pages = pdfDoc.getPages();
    const firstPage = pages[0];

    const pngImageBytes = await fetch(eMaterai).then((res) => res.arrayBuffer());
    const pngImage = await pdfDoc.embedPng(pngImageBytes);

    const { x, y } = position.current;

    firstPage.drawImage(pngImage, {
      x, // Posisi X dari draggable
      y: firstPage.getHeight() - y - 50, // Posisi Y (PDF menggunakan koordinat terbalik)
      width: 100, // Lebar e-materai
      height: 50, // Tinggi e-materai
    });

    const pdfBytes = await pdfDoc.save();
    const blob = new Blob([pdfBytes], { type: 'application/pdf' });
    const url = URL.createObjectURL(blob);

    // Download file PDF baru dengan eMaterai
    const link = document.createElement('a');
    link.href = url;
    link.download = 'pdf_with_eMaterai.pdf';
    link.click();
  };

  return (
    <div>
      <h2>Upload File PDF</h2>
      <input type="file" accept="application/pdf" onChange={handleFileUpload} />

      <canvas ref={canvasRef} style={{ border: '1px solid black', width: '600px', height: '800px' }}></canvas>

      {eMaterai && (
        <Draggable onStop={handleDragStop}>
          <img
            ref={dragRef}
            src={eMaterai}
            alt="E-Materai"
            style={{
              width: '100px',
              height: '50px',
              position: 'absolute',
              top: '0px',
              left: '0px',
            }}
          />
        </Draggable>
      )}

      <h2>Upload Gambar E-Materai</h2>
      <input type="file" accept="image/*" onChange={handleEMateraiUpload} />

      {pdfFile && eMaterai && (
        <button onClick={embedEMateraiToPdf}>Simpan eMaterai ke PDF</button>
      )}
    </div>
  );
};

export default App;
