import React, { useCallback, useEffect, useRef, useState } from "react";
import "pdfjs-dist/build/pdf.min.mjs";
import "pdfjs-dist/build/pdf.worker.min.mjs";

export default function ModalDetailDokumen({ data, is_preview_surat = false }) {
    const [activeTab, setActiveTab] = useState(1);
    const [loading, setLoading] = useState(false);
    const getUrlAndImageFlag = () => {
        let url = "";
        let is_image = false;
        if (data.nama_original?.includes("pdf")) {
            url = `https://docs.google.com/gview?url=${app_url}${data.file}&embedded=true`;
        } else if (
            data.nama_original?.includes("doc") ||
            data.nama_original?.includes("docx") ||
            data.nama_original?.includes("ppt") ||
            data.nama_original?.includes("pptx") ||
            data.nama_original?.includes("xls") ||
            data.nama_original?.includes("xlsx")
        ) {
            url = `https://view.officeapps.live.com/op/embed.aspx?src=${app_url}${data.file}`;
        } else if (is_preview_surat) {
            url = `${app_url}/surat-masuk/render-pdf/${data.id}`;
        } else {
            url = `${app_url}${data.file}`;
            is_image = true;
        }
        return { url, is_image };
    };

    const { url, is_image } = getUrlAndImageFlag();

    const listTabs = [
        {
            type: 1,
            name: "Dokumen",
        },
        {
            type: 2,
            name: "Dokumen Tanda tangan",
        },
    ];

    const [isRenderPdf, setIsRenderPdf] = useState(false);
    const [pdfFile, setPdfFile] = useState("");
    const [sizePdf, setSizePdf] = useState({
        width: 0,
        height: 0,
    });
    const canvasRef = useRef(null);
    const [pagePdf, setPagePdf] = useState(1);
    const [totalPage, setTotalPage] = useState(1);

    const fetchPdfFile = async () => {
        setLoading(true);
        try {
            const response = await axios.get(`${app_url}${data.file_convert}`, {
                responseType: "blob",
            });
            setPdfFile(response.data);
        } catch (error) {
            console.error("Gagal mengambil file PDF:", error);
        }
        setLoading(false);
    };

    const renderPDFToCanvas = async (pageInject = pagePdf) => {
        if (!pdfFile) return;

        const fileReader = new FileReader();
        fileReader.onload = async function () {
            const typedArray = new Uint8Array(this.result);
            const pdf = await pdfjsLib.getDocument(typedArray).promise;
            setTotalPage(pdf.numPages);
            const page = await pdf.getPage(pageInject);

            const scale = 1.5;
            const viewport = page.getViewport({ scale: scale });
            const canvas = canvasRef.current;
            const context = canvas.getContext("2d");

            canvas.width = viewport.width * scale;
            canvas.height = viewport.height * scale;
            context.setTransform(scale, 0, 0, scale, 0, 0);

            setSizePdf({
                width: viewport.width,
                height: viewport.height,
            });

            await page.render({ canvasContext: context, viewport }).promise;

            const signatures = data_detail.tanda_tangan_surat.filter(
                (item) =>
                    item.referable_id === data?.id &&
                    parseInt(item.page) === pageInject
            );

            await Promise.all(
                signatures.map((item) => {
                    return new Promise((resolve) => {
                        const img = new Image();
                        img.src = item?.image;
                        img.onload = () => {
                            const scaleX =
                                canvas.clientWidth / item.canvas_width;
                            const scaleY =
                                canvas.clientHeight / item.canvas_height;
                            const autoWidth =
                                item.height * (img.width / img.height) * scaleY;

                            context.drawImage(
                                img,
                                item.posisi_x * scaleX,
                                item.posisi_y * scaleY,
                                autoWidth,
                                item.height * scaleY
                            );

                            resolve();
                        };
                    });
                })
            );
        };

        fileReader.readAsArrayBuffer(pdfFile);
    };

    useEffect(() => {
        if (pdfFile && activeTab === 2 && !isRenderPdf) {
            setIsRenderPdf(true);
            renderPDFToCanvas();
        }
    }, [pdfFile, activeTab]);

    useEffect(() => {
        fetchPdfFile();
    }, []);

    const handleGetPage = (type) => {
        console.log("type", type);
        if (totalPage === 1) return;
        let page = pagePdf;
        if (type === "prev") {
            if (pagePdf === 1) return;
            page = pagePdf - 1;
            setPagePdf(page);
        } else {
            if (pagePdf === totalPage) return;
            page = pagePdf + 1;
            setPagePdf(page);
        }

        renderPDFToCanvas(page);
    };

    return (
        <>
            <div className="modal-header">
                <h2 className="d-flex flex-column mb-0">
                    <span className="fs-18 fw-bold mb-1">
                        Preview {is_preview_surat ? "Surat" : "Lampiran"}{" "}
                        {data?.nama_original ?? ""}
                    </span>
                </h2>
                <a
                    href="javascript:void(0)"
                    className="avatar-text avatar-md bg-soft-danger close-icon"
                    data-bs-dismiss="modal"
                >
                    <i className="feather-x text-danger"></i>
                </a>
            </div>
            <div className="modal-body" style={{ minHeight: "200px" }}>
                {parseInt(data.required_signature) === 1 && (
                    <ul className="nav nav-tabs">
                        {listTabs.map((tab, index) => (
                            <li className="nav-item" key={index}>
                                <button
                                    onClick={() => setActiveTab(tab.type)}
                                    className={`nav-link ${
                                        tab.type === activeTab ? "active" : ""
                                    }`}
                                    id={`tab-${tab.type}`}
                                    data-bs-toggle="tab"
                                    href={`#tab-${tab.type}`}
                                    role="tab"
                                    aria-controls={`tab-${tab.type}`}
                                    aria-selected={
                                        tab.type === 1 ? "true" : "false"
                                    }
                                >
                                    {tab.name}
                                </button>
                            </li>
                        ))}
                    </ul>
                )}
                <div
                    className={`${activeTab === 1 ? "" : "d-none"}`}
                    id="tab-2"
                    role="tabpanel"
                >
                    {is_image ? (
                        <img
                            src={`${app_url}${data.file}`}
                            alt=""
                            style={{ width: "100%" }}
                        />
                    ) : (
                        <iframe
                            src={url}
                            style={{
                                width: "100%",
                                height: "500px",
                                border: "none",
                            }}
                        ></iframe>
                    )}
                </div>
                <div
                    className={`${activeTab === 2 ? "" : "d-none"}`}
                    id="tab-2"
                    role="tabpanel"
                >
                    {loading ? (
                        <div className="m-auto">
                            <div
                                className="spinner-border"
                                role="status"
                                style={{ height: "50px", width: "50px" }}
                            >
                                <span className="visually-hidden">
                                    Loading...
                                </span>
                            </div>
                        </div>
                    ) : (
                        <div className="p-4 position-relative">
                            <div
                                className="mb-3"
                                style={{
                                    width: "100%",
                                    display: "flex",
                                    justifyContent: "center",
                                    alignItems: "center",
                                }}
                            >
                                <div
                                    className="d-flex align-items-center gap-3 justify-content-center py-2"
                                    style={{
                                        background: "white",
                                        width: "200px",
                                        borderRadius: "5px",
                                        // boxShadow: "0 30px 40px 0 rgba(16, 36, 94, .2)",
                                        border: "1px solid #e5e5e5",
                                    }}
                                >
                                    <div
                                        role="button"
                                        className="py-1 px-3"
                                        disabled={pagePdf === 1}
                                        onClick={() => handleGetPage("prev")}
                                    >
                                        <i class="fas fa-angle-left"></i>
                                    </div>
                                    <div
                                        style={{
                                            fontSize: "18px",
                                            color: "black",
                                        }}
                                    >
                                        {pagePdf} of {totalPage}
                                    </div>
                                    <div
                                        role="button"
                                        className="py-1 px-3"
                                        disabled={pagePdf === totalPage}
                                        onClick={() => handleGetPage("next")}
                                    >
                                        <i class="fas fa-angle-right"></i>
                                    </div>
                                </div>
                            </div>
                            <div
                                className="parent_preview mx-auto"
                                style={{
                                    height: sizePdf.height,
                                    width: sizePdf.width,
                                    // border: "1px solid black",
                                    position: "relative",
                                }}
                            >
                                <canvas
                                    ref={canvasRef}
                                    style={{
                                        width: "100%",
                                        height: "100%",
                                        border: "1px solid black",
                                    }}
                                ></canvas>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
