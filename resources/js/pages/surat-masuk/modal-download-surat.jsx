import React, { useRef, useState } from "react";
import Select from "react-select";
import "pdfjs-dist/build/pdf.min.mjs";
import "pdfjs-dist/build/pdf.worker.min.mjs";
import jsPDF from "jspdf";
import axios from "axios";
import fs from "fs";
import { PDFDocument } from "pdf-lib";

export default function ModalDownloadSurat({ kopSuratData, data }) {
    const [kopSuratSelected, setKopSuratSelected] = useState(null);
    const [notShowTanggalSurat, setNotShowTanggalSurat] = useState(false);
    // const [pdfFile, setPdfFile] = useState("");
    const [loadingDownload, setLoadingDownload] = useState(false);
    const canvasRef = useRef(null);
    const [sizePdf, setSizePdf] = useState({
        width: 0,
        height: 0,
    });

    const fetchPdfFile = async () => {
        try {
            const response = await axios.get(
                `${app_url}/surat-masuk/render-pdf/${
                    data_detail?.id
                }?download=true&kop_surat_id=${
                    kopSuratSelected?.value ?? data_detail?.kop_surat_id
                }&not_show_tanggal_surat=${notShowTanggalSurat}&download=true`,
                {
                    responseType: "blob",
                }
            );
            // setPdfFile(response.data);
            await downloadPDF(response.data);
        } catch (error) {
            console.error("Gagal mengambil file PDF:", error);
        }
    };

    const downloadPDF = async (pdfFile) => {
        const fileReader = new FileReader();
        fileReader.onload = async function () {
            const existingPdfBytes = new Uint8Array(this.result);
            const pdfDoc = await PDFDocument.load(existingPdfBytes);

            const promises = 
            data_detail.tanda_tangan_surat
            .filter((item) => item.referable_id === data_detail?.id)
            .forEach(async(item) => {
                if (item.barcode === null) return;

                const jpgImageBase64 = item.image.split(',')[1];
                const pngImageDoc = await pdfDoc.embedPng(jpgImageBase64);
                const { width: imgWidth, height: imgHeight } = pngImageDoc;

                const pages = pdfDoc.getPages();
                const page = pages[parseInt(item.page) - 1];
                const { width, height } = page.getSize();

                const scaleX = width / item.canvas_width;
                const scaleY = height / item.canvas_height;
                const autoWidth = item.height * (imgWidth / imgHeight) * scaleY;

                page.drawImage(pngImageDoc, {
                    x: item.posisi_x * scaleX,
                    y: height - (item.posisi_y * scaleY) - (item.height * scaleY),
                    width: autoWidth,
                    height: item.height * scaleY,
                });
            });

            if (promises) {
                await Promise.all(promises);
            }

            const pdfBytes = await pdfDoc.save();
            const blob = new Blob([pdfBytes], { type: "application/pdf" });
            const link = document.createElement("a");
            link.href = URL.createObjectURL(blob);
            link.download = `Surat_${data?.no_surat ?? ''}_${data?.perihal ?? ''}.pdf`;
            link.click();
        };

        fileReader.readAsArrayBuffer(pdfFile);
    };

    const handleDownloadSurat = async () => {
        setLoadingDownload(true);
        await fetchPdfFile();
        // await downloadPDF();
        setLoadingDownload(false);
    };

    return (
        <>
            <div className="modal-header">
                <h2 className="d-flex flex-column mb-0">
                    <span className="fs-18 fw-bold mb-1">Download Surat</span>
                </h2>
                <a
                    href="javascript:void(0)"
                    className="avatar-text avatar-md bg-soft-danger close-icon"
                    data-bs-dismiss="modal"
                >
                    <i className="feather-x text-danger"></i>
                </a>
            </div>
            <div className="modal-body">
                <div className="mb-3">
                    <label className="form-label">Kop Surat</label>
                    <Select
                        options={kopSuratData}
                        placeholder="Pilih kop surat"
                        defaultValue={kopSuratData.find(
                            (item) => item.value === data_detail?.kop_surat_id
                        )}
                        isClearable
                        styles={{
                            control: (baseStyles) => ({
                                ...baseStyles,
                                borderColor: "#e5e7eb",
                                boxShadow: "none",
                                padding: "4px 6px",
                                borderRadius: "5px",
                            }),
                            option: (baseStyles, state) => ({
                                ...baseStyles,
                                backgroundColor: state.isSelected
                                    ? "#EDF2F7"
                                    : "#fff",
                                color: "#2D3748",
                                "&:hover": {
                                    backgroundColor: "#EDF2F7",
                                },
                                "&:active": {
                                    backgroundColor: "#EDF2F7",
                                },
                                '&[data-selected="true"]': {
                                    backgroundColor: "#EDF2F7",
                                },
                            }),
                            menu: (baseStyles) => ({
                                ...baseStyles,
                                border: "1px solid #e5e7eb",
                                boxShadow: "0 1px 2px 0 rgba(0, 0, 0, 0.10)",
                                zIndex: 9999,
                            }),
                        }}
                        onChange={(value) => {
                            setKopSuratSelected(value);
                        }}
                    />
                </div>
                {/* <div className="mb-3">
                    <label className="form-label">Kop Surat</label>
                    <div className="form-check form-switch">
                        <input
                            className="form-check-input"
                            type="checkbox"
                            role="switch"
                            id="flexSwitchCheckDefault"
                            onInput={(e) => {
                                console.log(
                                    "e.target.checked",
                                    e.target.checked
                                );
                                setNotShowTanggalSurat(e.target.checked);
                            }}
                        />
                        <label
                            className="form-check-label"
                            htmlFor="flexSwitchCheckDefault"
                        >
                            Jangan Tampilkan Tanggal Surat
                        </label>
                    </div>
                </div> */}
                <div>
                    <button
                        type="button"
                        onClick={() => {
                            handleDownloadSurat();
                        }}
                        disabled={loadingDownload}
                        className="btn btn-primary w-100"
                    >
                        {loadingDownload ? (
                            <div
                                className="spinner-border spinner-border-sm"
                                role="status"
                            >
                                <span className="visually-hidden">
                                    Loading...
                                </span>
                            </div>
                        ) : (
                            "Download Surat"
                        )}
                    </button>
                </div>
            </div>
            <div
                className=""
                style={{
                    height: sizePdf.height,
                    width: sizePdf.width,
                    // border: "1px solid black",
                    position: "relative",
                }}
            >
                <canvas
                    ref={canvasRef}
                    style={{
                        width: "100%",
                        height: "100%",
                        border: "1px solid black",
                    }}
                ></canvas>
            </div>
        </>
    );
}
