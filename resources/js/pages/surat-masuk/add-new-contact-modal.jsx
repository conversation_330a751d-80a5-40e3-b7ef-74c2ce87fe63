import axios from "axios";
import React, { useState } from "react";
import Swal from "sweetalert2";

export default function AddNewContactModal({
    handleRefresh
}) {
    const csrf_token = document.querySelector('meta[name="csrf-token"]');
    const [form, setForm] = useState({
        nama: "",
        email: "",
        keterangan: "",
    });

    const handleSubmit = async (e) => {
        e.preventDefault();

        if(form.nama === "" || form.email === ""){
            Swal.fire({
                icon: "error",
                title: "Gagal menambahkan kontak",
                text: "<PERSON><PERSON> dan email tidak boleh kosong",
            });
            return;
        }

        try {

            const {data} = await axios.post("/contact", form, {
                headers: {
                    "X-CSRF-TOKEN": csrf_token.content,
                },
            });

            if(data.status) {
                Swal.fire({
                    icon: "success",
                    title: "Berhasil menambahkan kontak",
                    text: data.message,
                });
                $("#modalAddNewContact").modal("hide");
                setForm({
                    nama: "",
                    email: "",
                    keterangan: "",
                });
                if (typeof handleRefresh === "function") {
                    handleRefresh();
                }
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Gagal menambahkan kontak",
                    text: data.message,
                });
            }

        } catch (error) {
            Swal.fire({
                icon: "error",
                title: "Gagal menambahkan kontak",
                text: error?.response?.data?.message,
            });
        }


    }

    const handleChange = (e) => {
        setForm({
            ...form,
            [e.target.name]: e.target.value,
        });
    }

    return (
        <>
            <div className="modal-header aligm-items-center">
                <h2 className="d-flex flex-column mb-0">
                    <span className="fs-16 fw-bold mb-1">
                        Tambah Kontak Baru
                    </span>
                </h2>
                <div></div>
                <div className="d-flex align-items-center gap-3">
                    <a
                        href="javascript:void(0)"
                        class="avatar-text avatar-md bg-soft-danger close-icon"
                        data-bs-dismiss="modal"
                    >
                        <i class="feather-x text-danger"></i>
                    </a>
                </div>
            </div>
            <form
            onSubmit={handleSubmit}
            >
                <div class="modal-body">
                    <div class="form-group mb-4">
                        <label htmlFor="nama" class="form-label">
                            Nama
                        </label>
                        <input
                            type="text"
                            class="form-control"
                            id="nama"
                            name="nama"
                            placeholder="Masukan nama"
                            onChange={handleChange}
                            value={form.nama}
                        />
                    </div>
                    <div class="form-group mb-4">
                        <label htmlFor="email" class="form-label">
                            Email
                        </label>
                        <input
                            type="email"
                            class="form-control"
                            id="email"
                            name="email"
                            placeholder="Masukan email"
                            onChange={handleChange}
                            value={form.email}
                        />
                    </div>
                    <div class="form-group mb-4">
                        <label htmlFor="name" class="form-label">
                            keterangan
                        </label>
                        <textarea
                            class="form-control"
                            id="keterangan"
                            name="keterangan"
                            placeholder="Masukan ketarangan"
                            onChange={handleChange}
                            defaultValue={form.keterangan}
                        >
                        </textarea>
                    </div>
                </div>
                <div class="modal-footer d-flex align-items-center justify-content-between gap-3 w-100">
                    <button
                        type="button"
                        class="btn btn-outline-secondary"
                        data-bs-dismiss="modal"
                    >
                        Batal
                    </button>
                    <button class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </>
    );
}
