import React from "react";
import "./document-esign.css";

export default function Signer({ data }) {
    const signature = data.signature || "/assets/qrcode.svg";
    const documentId = data.id || "123456789";

    return (
        <div style={{ paddingBottom: "4px", paddingTop: "16px" }}>
            <div className="signature-box">
                <div className="signature-header">
                    Ditandatangani pada : {new Date().toLocaleString('id-ID', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: false
                    }).replace(',', '')},
                    <br />
                    Oleh : {data.type === 1 && data.fullName}
                </div>
                <div
                    style={{
                        display: "flex",
                        alignItems: "center",
                    }}
                >
                    {
                        data.type == 1 && (
                            <img
                                style={{
                                    padding: "22px 14px 0px 24px",
                                    height: "165px",
                                }}
                                src={data.signature}
                                alt="Tanda Tangan"
                            />
                        )
                    }
                    {data.type === 2 && (
                        <>
                            <img
                                style={{
                                    padding: "28px 14px 0px 24px",
                                    height: "165px",
                                }}
                                src={"/assets/qrcode.svg"}
                                alt="Tanda Tangan"
                            />
                            <div
                                style={{
                                    fontSize: "30px",
                                    fontWeight: "bold",
                                    color: "#000",
                                }}
                            >
                                {data.fullName.toUpperCase()}
                            </div>
                        </>
                    )}
                </div>
                <div className="signature-id">{documentId}</div>
            </div>
        </div>
    );
}
