import React, { useRef, useState, useEffect } from "react";
import ModalSigner from "./modal-signer";
import Modal from "../../components/modal";
import ReactDOM from "react-dom/client";

export default function DocumentEsignIndex() {
    const [pdfFile, setPdfFile] = useState("");
    const [sizePdf, setSizePdf] = useState({
        width: 0,
        height: 0,
    });
    const canvasRef = useRef([]);
    const [totalPage, setTotalPage] = useState(1);
    const [pdf, setPdf] = useState(false);
    const [loading, setLoading] = useState(false);
    const [annotationActive, setAnnotationActive] = useState(null);
    const [signature, setSignature] = useState({});

    const fetchPdfFile = async (id = null) => {
        setLoading(true);
        try {
            const response = await axios.get(`/${dataDocument.document_path}`, {
                responseType: "blob",
            });
            setPdfFile(response.data);
        } catch (error) {
            console.error("Gagal mengambil file PDF:", error);
        }
        setLoading(false);
    };

    const loadPdf = async (pageInject) => {
        if (!pdfFile) return;

        const fileReader = new FileReader();
        fileReader.onload = async function () {
            const typedArray = new Uint8Array(this.result);
            const pdf = await pdfjsLib.getDocument(typedArray).promise;
            setPdf(pdf);
            setTotalPage(pdf.numPages);
        };

        fileReader.readAsArrayBuffer(pdfFile);
    };

    useEffect(() => {
        fetchPdfFile();
    }, []);

    useEffect(() => {
        if (pdfFile) loadPdf();
    }, [pdfFile]);

    useEffect(() => {
        const renderPdfCanvas = async () => {
            for (let pageNum = 1; pageNum <= totalPage; pageNum++) {
                const page = (await pdf?.getPage(pageNum)) ?? null;

                const scale = 1.5;
                const viewport = page.getViewport({ scale: scale });
                const canvas = canvasRef.current[pageNum - 1];
                const context = canvas.getContext("2d");

                canvas.height = viewport.height * scale;
                canvas.width = viewport.width * scale;
                context.scale(scale, scale);

                const renderContext = {
                    canvasContext: context,
                    viewport: viewport,
                };

                await page.render(renderContext).promise.then(() => {
                    setSizePdf({
                        width: canvas.clientWidth,
                        height: canvas.clientHeight,
                    });
                    setLoading(false);
                });
            }
        };
        renderPdfCanvas();
    }, [pdf, totalPage]);

    const handleOpenSign = (data) => {
        $("body").append(
            Modal("", "modalSigner", " modal-md", "", "contentModalSigners")
        );
        ReactDOM.createRoot(document.getElementById("contentModalSigners"), {
            hydrate: true,
        }).render(
            <ModalSigner
                data={data}
                signature={signature}
                setSignature={setSignature}
            />
        );
        $("#modalSigner").modal("show");
    };

    return (
        <div className="" style={{ background: "#f1f4f7" }}>
            <div
                style={{
                    borderBottom: "1px solid #D0D6DD",
                }}
                className="d-flex justify-content-between align-items-center position-fixed top-0 start-0 end-0 z-10 p-3 bg-white"
            >
                <div>
                    <img
                        src="/assets/images/logo.png"
                        alt="Logo"
                        style={{ height: "40px" }}
                    />
                </div>
                <div>
                    {(() => {
                        const signatureNeeded =
                            dataAnnotations.length -
                            Object.keys(signature ?? {}).length;

                        return (
                            <button
                                className="btn btn-primary"
                                style={{
                                    padding: "8px 16px",
                                    borderRadius: "4px",
                                    fontSize: "14px",
                                    textTransform: "capitalize",
                                }}
                                onClick={() => {
                                    let active = annotationActive
                                        ? annotationActive + 1
                                        : 1;
                                    if (active > dataAnnotations.length) {
                                        active = 1;
                                    }
                                    const targetElement =
                                        document.getElementById(
                                            `annotation-${active}`
                                        );
                                    if (targetElement) {
                                        targetElement.scrollIntoView({
                                            behavior: "smooth",
                                            block: "center",
                                        });
                                    }
                                    setAnnotationActive(active);
                                }}
                            >
                                {
                                    signatureNeeded > 0 ? `${signatureNeeded} kolom untuk ditandatangani` : 
                                        (
                                            <>
                                                <i className="fas fa-check-circle me-2"></i>
                                                Selesai
                                            </>
                                        )
                                    
                                }
                            </button>
                        );
                    })()}
                </div>
            </div>
            <div
                className="boxEsign"
                style={{
                    background: "#f2f4f7",
                    display: "flex",
                    flexDirection: "column",
                    position: "relative",
                    margin: "80px auto",
                }}
            >
                {loading ? (
                    <div className="m-auto">
                        <div
                            className="spinner-border"
                            role="status"
                            style={{ height: "50px", width: "50px" }}
                        >
                            <span className="visually-hidden">Loading...</span>
                        </div>
                    </div>
                ) : (
                    <div
                        style={{
                            width: "100%",
                            height: "auto",
                        }}
                    >
                        {Array.from({ length: totalPage }).map((_, index) => (
                            <div key={index}>
                                <div className="fs-11 py-2">
                                    Dokumen ID: {dataDocument.id}
                                </div>
                                <div
                                    key={index}
                                    style={{
                                        position: "relative",
                                        overflow: "hidden",
                                        boxShadow:
                                            "0 4px 6px -1px rgba(0, 0, 0, 0.1),0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                                    }}
                                >
                                    <canvas
                                        ref={(el) =>
                                            (canvasRef.current[index] = el)
                                        }
                                        style={{
                                            width: "100%",
                                            height: "auto",
                                            display: "block",
                                        }}
                                    ></canvas>
                                    {dataAnnotations.map((annotation, idx) => {
                                        const canvas = canvasRef.current[index];
                                        const scaleX = canvas
                                            ? canvas.clientWidth /
                                              annotation.canvas_width
                                            : 1;
                                        const scaleY = canvas
                                            ? canvas.clientHeight /
                                              annotation.canvas_height
                                            : 1;
                                        const signatureAnnotation =
                                            signature[`${annotation.id}`];
                                        return (
                                            <div key={idx}>
                                                {!signatureAnnotation && (
                                                    <div
                                                        id={`annotation-${
                                                            idx + 1
                                                        }`}
                                                        role="button"
                                                        style={{
                                                            position:
                                                                "absolute",
                                                            top:
                                                                annotation.position_y *
                                                                scaleY,
                                                            left:
                                                                annotation.position_x *
                                                                    scaleX -
                                                                140,
                                                            width: `150px`,
                                                            height: `${
                                                                annotation.element_height *
                                                                scaleY
                                                            }px`,
                                                            zIndex: 1,
                                                        }}
                                                        className="d-flex align-items-center p-2"
                                                    >
                                                        <div className="position-relative">
                                                            <div
                                                                className="fs-12 text-white px-3 py-2 fw-bold"
                                                                style={{
                                                                    background:
                                                                        "#28a745",
                                                                }}
                                                            >
                                                                Tanda Tangan
                                                            </div>
                                                            <div
                                                                style={{
                                                                    position:
                                                                        "absolute",
                                                                    top: "50%",
                                                                    right: "-17px",
                                                                    transform:
                                                                        "translateY(-50%)",
                                                                    width: "0",
                                                                    height: "0",
                                                                    borderTop:
                                                                        "17px solid transparent",
                                                                    borderBottom:
                                                                        "17px solid transparent",
                                                                    borderLeft:
                                                                        "17px solid #16c666",
                                                                    zIndex: 2,
                                                                }}
                                                            ></div>
                                                        </div>
                                                    </div>
                                                )}

                                                <div
                                                    role="button"
                                                    style={{
                                                        position: "absolute",
                                                        top:
                                                            annotation.position_y *
                                                            scaleY,
                                                        left:
                                                            annotation.position_x *
                                                            scaleX,
                                                        width:
                                                            annotation.element_width *
                                                            scaleX,
                                                        height:
                                                            annotation.element_height *
                                                            scaleY,
                                                    }}
                                                    className=""
                                                >
                                                    {signatureAnnotation ? (
                                                        <div>
                                                            <img
                                                                style={{
                                                                    height:
                                                                        annotation.element_height *
                                                                        scaleY,
                                                                    maxWidth:
                                                                        "fit-content",
                                                                }}
                                                                src={
                                                                    signatureAnnotation
                                                                }
                                                            />
                                                            <button
                                                                onClick={() => {
                                                                    const newSignature =
                                                                        {
                                                                            ...signature,
                                                                        };
                                                                    delete newSignature[
                                                                        `${annotation.id}`
                                                                    ];
                                                                    setSignature(
                                                                        newSignature
                                                                    );
                                                                }}
                                                                style={{
                                                                    position:
                                                                        "absolute",
                                                                    top: "-5px",
                                                                    right: "-20px",
                                                                    zIndex: 1,
                                                                    height: "20px",
                                                                    width: "20px",
                                                                    padding:
                                                                        "0",
                                                                }}
                                                                className="btn bg-white border rounded-full shadow-sm"
                                                            >
                                                                <i
                                                                    style={{
                                                                        fontSize:
                                                                            "10px",
                                                                    }}
                                                                    className="fas fa-times text-dark"
                                                                ></i>
                                                            </button>
                                                        </div>
                                                    ) : (
                                                        <div
                                                            onClick={() => {
                                                                handleOpenSign({
                                                                    ...annotation,
                                                                    scaleX,
                                                                    scaleY,
                                                                });
                                                            }}
                                                            className="fs-12 text-black d-flex p-2 align-items-center"
                                                            style={{
                                                                height:
                                                                    annotation.element_height *
                                                                    scaleY,
                                                                background:
                                                                    "rgba(221, 243, 226, 0.75)",
                                                                boxShadow:
                                                                    "rgb(60, 145, 77) 0px 0px 0px 1px inset",
                                                            }}
                                                        >
                                                            Klik disini
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                                <div
                                    className="d-flex align-items-center mt-2 justify-content-between"
                                    style={{
                                        marginBottom: "20px",
                                    }}
                                >
                                    <div className="text-end fs-12 text-dark">
                                        <span className="text-muted">
                                            {dataDocument.document_name}
                                        </span>
                                    </div>
                                    <div className="text-end fs-12 text-dark">
                                        <span className="text-muted">
                                            Halaman {index + 1} dari {totalPage}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
}
