import React from "react";

export default function ContentHistory({ data_history, surat }) {
    return (
        <div className="recent-activity p-4">
            <div className="mb-2 d-flex justify-content-between">
                <h5 className="fw-bold">
                    Aktifitas Surat No {surat?.no_surat}
                </h5>
            </div>
            <div
                style={{
                    height: "calc(100vh - 130px)",
                    overflow: "auto",
                    padding: "20px 5px",
                }}
            >
                <ul className="list-unstyled activity-feed">
                    {data_history.map((item, index) => (
                        <li
                            key={index}
                            className="d-flex justify-content-between feed-item feed-item-success"
                        >
                            <div>
                                <span className="lead_date mb-1">
                                    {item?.user?.nama} - {item?.role?.name}{" "}
                                    <span className="date">
                                        [{item?.created_at_formatted}]
                                    </span>{" "}
                                    <span
                                        className="ms-2"
                                        dangerouslySetInnerHTML={{
                                            __html: item?.approval_status
                                                ? item?.approve_status_name_formatted
                                                : (parseInt(item?.type) === 5 || parseInt(item?.type) === 6) ? surat?.status_formatted : "",
                                        }}
                                    ></span>
                                </span>
                                <span className="text fs-15">
                                    {item?.status}
                                </span>
                            </div>
                        </li>
                    ))}
                </ul>
            </div>
        </div>
    );
}
