import React, { useState } from "react";
import ReactD<PERSON> from "react-dom/client";
import { Link } from "react-router-dom";

const SuratMasuk = () => {
    const [showInputSearch, setShowInputSearch] = useState(false);
    const [checkInput, setCheckInput] = useState(false);

    return (
        <div className="content-area">
            <div className="content-area-header sticky-top" style={{ backgroundColor: '#F6F6F6' }} >
                <div className="page-header-left d-flex align-items-center gap-2 show-action">
                    <a href="" className="app-sidebar-open-trigger me-2">
                        <i className="feather-align-left fs-24"></i>
                    </a>
                    <div className="custom-control custom-checkbox ms-1 me-2">
                        <input
                            type="checkbox"
                            className="custom-control-input"
                            id="checkAll"
                            data-checked-action="show-options"
                        />
                        <label
                            className="custom-control-label"
                            htmlFor="checkAll"
                        ></label>
                    </div>
                    <div className="action-list-items">
                        <div className="dropdown">
                            <a
                                href=""
                                className="avatar-text avatar-md p-3"
                                data-bs-toggle="dropdown"
                                data-bs-offset="0,22"
                            >
                                <i className="feather-chevron-down fs-16"></i>
                            </a>
                            <ul className="dropdown-menu">
                                <li>
                                    <a className="dropdown-item" href="">
                                        <i className="feather-eye me-3"></i>
                                        <span>Read</span>
                                    </a>
                                </li>
                                <li>
                                    <a className="dropdown-item" href="">
                                        <i className="feather-eye-off me-3"></i>
                                        <span>Unread</span>
                                    </a>
                                </li>
                                <li>
                                    <a className="dropdown-item" href="">
                                        <i className="feather-star me-3"></i>
                                        <span>Starred</span>
                                    </a>
                                </li>
                                <li>
                                    <a className="dropdown-item" href="">
                                        <i className="feather-shield-off me-3"></i>
                                        <span>Unstarred</span>
                                    </a>
                                </li>
                                <li className="dropdown-divider"></li>
                                <li>
                                    <a className="dropdown-item" href="">
                                        <i className="feather-clock me-3"></i>
                                        <span>Snooze</span>
                                    </a>
                                </li>
                                <li>
                                    <a className="dropdown-item" href="">
                                        <i className="feather-check-circle me-3"></i>
                                        <span>Add Tasks</span>
                                    </a>
                                </li>
                                <li className="dropdown-divider"></li>
                                <li>
                                    <a className="dropdown-item" href="">
                                        <i className="feather-archive me-3"></i>
                                        <span>Archive</span>
                                    </a>
                                </li>
                                <li>
                                    <a className="dropdown-item" href="">
                                        <i className="feather-alert-octagon me-3"></i>
                                        <span>Report Spam</span>
                                    </a>
                                </li>
                                <li className="dropdown-divider"></li>
                                <li>
                                    <a className="dropdown-item" href="">
                                        <i className="feather-trash-2 me-3"></i>
                                        <span>Delete</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <a href="" className="d-none d-sm-flex">
                        <div
                            className="avatar-text avatar-md p-3"
                            data-bs-toggle="tooltip"
                            data-bs-trigger="hover"
                            title="Refresh"
                        >
                            <i className="feather-refresh-cw fs-16"></i>
                        </div>
                    </a>
                </div>
                <div className="page-header-right ms-auto">
                    <div className="hstack gap-2">
                        <div className="hstack">
                            <button
                                type="button"
                                onClick={() => {
                                    setShowInputSearch(true);
                                }}
                                className="search-form-open-toggle btn btn-light-brand p-0 rounded-pill border-0"
                            >
                                <div
                                    className="avatar-text avatar-md p-3"
                                    data-bs-toggle="tooltip"
                                    data-bs-trigger="hover"
                                    title="Search"
                                >
                                    <i className="feather-search fs-16"></i>
                                </div>
                            </button>
                            {showInputSearch && (
                                <form className="search-form">
                                    <div className="search-form-inner">
                                        <button
                                            type="button"
                                            onClick={() => {
                                                setShowInputSearch(false);
                                            }}
                                            className="search-form-open-toggle btn btn-light-brand p-0 rounded-pill border-0"
                                        >
                                            <div
                                                className="avatar-text avatar-md p-3"
                                                data-bs-toggle="tooltip"
                                                data-bs-trigger="hover"
                                                title="Back"
                                            >
                                                <i className="feather-arrow-left fs-16"></i>
                                            </div>
                                        </button>
                                        <input
                                            type="search"
                                            className="py-3 px-0 border-0 w-100"
                                            id="emailSearch"
                                            placeholder="Search..."
                                            autoComplete="off"
                                        />
                                    </div>
                                </form>
                            )}
                        </div>
                        <div className="dropdown d-none d-sm-flex">
                            <a
                                href=""
                                className="btn btn-light-brand btn-sm rounded-pill dropdown-toggle px-3 py-2 bg-white" style={{fontSize: '12px'}}
                                data-bs-toggle="dropdown"
                                data-bs-offset="0,23"
                                aria-expanded="false"
                            >
                                1-15 of 762{" "}
                            </a>
                            <ul className="dropdown-menu">
                                <li>
                                    <a className="dropdown-item" href="">
                                        Oldest
                                    </a>
                                </li>
                                <li>
                                    <a className="dropdown-item" href="">
                                        Newest
                                    </a>
                                </li>
                                <li>
                                    <a className="dropdown-item" href="">
                                        Replied
                                    </a>
                                </li>
                                <li className="dropdown-divider"></li>
                                <li>
                                    <a className="dropdown-item" href="">
                                        Ascending
                                    </a>
                                </li>
                                <li>
                                    <a className="dropdown-item" href="">
                                        Descending
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div className="hstack d-none d-sm-flex">
                            <a href="" className="d-flex me-1">
                                <div
                                    className="avatar-text avatar-md p-3"
                                    data-bs-toggle="tooltip"
                                    data-bs-trigger="hover"
                                    title="Previous"
                                >
                                    <i className="feather-chevron-left fs-16"></i>
                                </div>
                            </a>
                            <a href="" className="d-flex me-1">
                                <div
                                    className="avatar-text avatar-md p-3"
                                    data-bs-toggle="tooltip"
                                    data-bs-trigger="hover"
                                    title="Next"
                                >
                                    <i className="feather-chevron-right fs-16"></i>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div className="content-area-body p-0">
                <Link to="/surat-masuk/2/detail" className="single-items">
                    <div className="d-flex wd-80 gap-4 ms-1 item-meta">
                        <div className="item-checkbox">
                            <div className="custom-control custom-checkbox">
                                <input
                                    type="checkbox"
                                    className="custom-control-input checkbox"
                                    id="checkBox_1"
                                    data-checked-action="show-options"
                                />
                                <label
                                    className="custom-control-label"
                                    htmlFor="checkBox_1"
                                ></label>
                            </div>
                        </div>
                        <div className="item-favorite">
                            <a href="">
                                <i className="feather-star fs-16"></i>
                            </a>
                        </div>
                    </div>
                    <div
                        className="d-flex align-items-center gap-2 w-100 item-info"
                        data-view-toggle="details"
                    >
                        <a href="" className="hstack gap-3 wd-200 item-user">
                            {/* <div className="avatar-image avatar-md">
                                <img
                                    src="assets/images/avatar/1.png"
                                    alt=""
                                    className="img-fluid"
                                />
                            </div> */}
                            <div className="text-truncate-1-line mb-0 fw-bold" style={{fontSize: '14px'}}>
                                SMP IT Al Hikmah
                            </div>
                        </a>
                        <a href="" className="d-none d-md-block">
                            <div className="w-100 text-truncate-1-line item-desc">
                                <span className="ms-3">
                                    Ruhul Hasan, log into Facebook with one click - <span className="fw-normal">Halo company pendidkakndan ajdjajsjdjd </span>
                                </span>
                            </div>
                        </a>
                    </div>
                    <div className="d-flex align-items-center justify-content-end wd-150 gap-3 item-data" style={{width: '300px', maxWidth: '300px', minWidth: '250px'}}>
                        <div className="fs-12 fw-medium text-muted text-uppercase d-none d-sm-block item-time">
                            26 May, 2023 10:30 AM
                        </div>
                        <div className="item-action">
                            <div className="dropdown">
                                <a
                                    href=""
                                    data-bs-toggle="dropdown"
                                    data-bs-offset="0, 28"
                                >
                                    <div
                                        className="avatar-text avatar-sm "
                                        style={{padding: '12px',}}
                                        data-bs-toggle="tooltip"
                                        data-bs-trigger="hover"
                                        title="More Options"
                                    >
                                        <i className="feather-more-vertical fs-16"></i>
                                    </div>
                                </a>
                                <div className="dropdown-menu dropdown-menu-end">
                                    <a
                                        href=""
                                        className="dropdown-item"
                                        data-view-toggle="details"
                                    >
                                        <i className="feather-eye me-3"></i>
                                        <span>View</span>
                                    </a>
                                    <a href="" className="dropdown-item">
                                        <i className="feather-corner-up-right me-3"></i>
                                        <span>Reply</span>
                                    </a>
                                    <a href="" className="dropdown-item">
                                        <i className="feather-fast-forward me-3"></i>
                                        <span>Forward</span>
                                    </a>
                                    <a href="" className="dropdown-item">
                                        <i className="feather-repeat me-3"></i>
                                        <span>Reply All</span>
                                    </a>
                                    <div className="dropdown-divider"></div>
                                    <a
                                        href=""
                                        className="dropdown-item"
                                        data-action-target="#mailDeleteMessage"
                                    >
                                        <i className="feather-x me-3"></i>
                                        <span>Delete</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </Link>
                {/* <div className="p-4 d-flex align-items-center justify-content-center justify-content-md-between">
                    <div className="content-sidebar-footer wd-300 d-none d-md-block">
                        <div className="d-flex justify-content-between align-items-center mb-1">
                            <h2 className="fs-11 tx-spacing-1 mb-0">Storage</h2>
                            <div className="fs-11 text-muted">
                                43.5GB used of{" "}
                                <span className="fw-bold text-dark">100GB</span>
                            </div>
                        </div>
                        <div className="progress ht-3">
                            <div
                                className="progress-bar bg-primary"
                                role="progressbar"
                                aria-valuenow="43"
                                aria-valuemin="0"
                                aria-valuemax="100"
                                style={{ width: "43%" }}
                            ></div>
                        </div>
                    </div>
                    <div className="hstack gap-2 fs-11">
                        <a href="">Terms</a>
                        <span className="wd-3 ht-3 bg-gray-500 rounded-circle"></span>
                        <a href="">Privacy</a>
                        <span className="wd-3 ht-3 bg-gray-500 rounded-circle"></span>
                        <a href="">Policies</a>
                    </div>
                </div> */}
            </div>
        </div>
    );
};

export default SuratMasuk
