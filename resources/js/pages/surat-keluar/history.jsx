import { Link } from "react-router-dom";
import ContentHistory from "./content-history";

const HistorySurat = () => {
    return (
        <div className="content-area items-details-active">
            <div className="items-details">
                <div className="items-details-header bg-white sticky-top justify-content-between">
                    <div className="d-flex align-items-center">
                        <a
                            href="/surat-keluar"
                            className="avatar-text avatar-md item-info-close"
                            data-bs-toggle="tooltip"
                            data-bs-trigger="hover"
                            title="Back"
                        >
                            <i className="feather-arrow-left"></i>
                        </a>
                        <span className="vr mx-4"></span>
                        <div className="d-flex align-items-center">
                            <h4 className="fw-bold mb-0 text-dark text-truncate-1-line">
                                History Surat
                            </h4>
                        </div>
                    </div>
                </div>
                <div className="items-details-footer mail-action-editor mx-4 mt-3 mb-5 border border-top-0 rounded-3">
                    <ContentHistory 
                        data_history={data_history}
                        surat={surat}
                    />
                </div>
            </div>
        </div>
    );
};

export default HistorySurat;
