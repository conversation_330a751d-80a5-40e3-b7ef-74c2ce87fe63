import { Link } from "react-router-dom";
import { useEffect, useRef, useState } from "react";
import { Editor } from "@tinymce/tinymce-react";
import axios from "axios";
import Select from "react-select";
import Swal from "sweetalert2";
import Sortable from "sortablejs";

const CreateEditSuraKeluar = () => {
    const editorRef = useRef(null);
    const [userData, setUserData] = useState([]);
    const [kopSuratData, setKopSuratData] = useState([]);
    const [jenisSuratData, setJenisSuratData] = useState([]);
    const [attachmentFile, setAttachmentFile] = useState([]);
    const [progressAttachment, setProgressAttachment] = useState([]);
    const [errors, setErrors] = useState({});
    const [previewPdf, setPreviewPdf] = useState(null);
    const [loadingSubmit, setLoadingSubmit] = useState(false);
    const [formData, setFormData] = useState({
        kepada: null,
        jenis_surat: null,
        perihal: null,
        kop_surat: null,
        content_surat: null,
        unit_bisnis_id: null,
        ukuran: { value: "A4", label: "A4" },
    });

    const getDataFor = async ({ unit_bisnis_id = null }) => {
        const csrf_token = document.querySelector('meta[name="csrf-token"]');
        try {
            const { data } = await axios.get(`/pengguna/list-group`, {
                headers: {
                    "X-CSRF-TOKEN": csrf_token.content,
                },
                params: {
                    unit_bisnis_id: unit_bisnis_id,
                },
            });
            data.map((item) => {
                item.value = item.id;
                delete item.id;
                return item;
            });

            setUserData(data);
        } catch (error) {
            console.log(error);
        }
    };

    const getDataKopSurat = async ({ unit_bisnis_id = null }) => {
        try {
            const { data } = await axios.get(`/api/kop-surat/list`, {
                params: {
                    unit_bisnis_id: unit_bisnis_id,
                },
            });
            data.map((item) => {
                item.value = item.id;
                item.label = item.nama;
                delete item.id;
                return item;
            });

            console.log(data);

            setKopSuratData(data);
        } catch (error) {
            console.log(error);
        }
    };

    const getDataJenisSurat = async ({ unit_bisnis_id = null }) => {
        try {
            const csrf_token = document.querySelector(
                'meta[name="csrf-token"]'
            );
            const { data } = await axios.get(`/jenis-surat/list`, {
                headers: {
                    "X-CSRF-TOKEN": csrf_token.content,
                },
                params: {
                    unit_bisnis_id: unit_bisnis_id,
                },
            });
            data.map((item) => {
                item.value = item.id;
                item.label = item.jenis_surat;
                delete item.id;
                return item;
            });

            console.log(data);

            setJenisSuratData(data);
        } catch (error) {
            console.log(error);
        }
    };

    useEffect(() => {
        if (data_surat?.id) {
            setFormData((prevState) => ({
                ...prevState,
                content_surat: data_surat?.content_surat?.content ?? "",
            }));

            const attachment_file_progress =
                data_surat?.attachment_file_surat?.map((item) => {
                    return {
                        id: item.id,
                        name: item.nama_original,
                        file_size: item.size,
                        progress: 100,
                        status: "uploaded",
                        required_signature: parseInt(item.required_signature),
                    };
                });
            setProgressAttachment(attachment_file_progress);

            const attachment_file = data_surat?.attachment_file_surat?.map(
                (item) => {
                    return {
                        id: item.id,
                        data: {
                            url: item.file,
                            name_original: item.nama_original,
                            size: item.size,
                            mime_type: item.mime_type,
                            required_signature: parseInt(
                                item.required_signature
                            ),
                            file_convert: item.file_convert,
                        },
                    };
                }
            );

            setAttachmentFile(attachment_file);
        }

        user_unit_bisnis_data.map((item) => {
            item.value = item.id;
            item.label = item.nama;
            return item;
        });

        return () => {};
    }, []);

    const handleChanges = (e) => {
        setFormData((prevState) => ({
            ...prevState,
            [e.target.name]: e.target.value,
        }));
    };
    const handleAttachmentFile = async (e) => {
        const files = e.target.files;
        if (files.length) {
            const uploadFiles = Array.from(files).map(async (file) => {
                const formData = new FormData();
                formData.append("file", file);

                const uniqueFileId = `file-${Date.now()}-${file.name}`;

                setProgressAttachment((currentProgress) => {
                    const existingFileIndex = currentProgress.findIndex(
                        (up) => up.id === uniqueFileId
                    );
                    if (existingFileIndex === -1) {
                        return [
                            ...currentProgress,
                            {
                                id: uniqueFileId,
                                name: file.name,
                                file_size: file.size,
                                progress: 0,
                                status: "uploading",
                            },
                        ];
                    } else {
                        return currentProgress;
                    }
                });

                try {
                    const upload = await axios.post(
                        `/file-manager/upload`,
                        formData,
                        {
                            onUploadProgress: (progressEvent) => {
                                const percentCompleted = Math.round(
                                    (progressEvent.loaded * 100) /
                                        progressEvent.total
                                );
                                setProgressAttachment((currentProgress) =>
                                    currentProgress.map((up) => {
                                        if (up.id === uniqueFileId) {
                                            return {
                                                ...up,
                                                progress: percentCompleted,
                                                status: "uploading",
                                            };
                                        }
                                        return up;
                                    })
                                );
                            },
                        }
                    );
                    // Update state setelah file terupload
                    setProgressAttachment((currentProgress) =>
                        currentProgress.map((up) => {
                            if (up.id === uniqueFileId) {
                                return {
                                    ...up,
                                    status: "uploaded",
                                    progress: 100,
                                };
                            }
                            return up;
                        })
                    );
                    // Menyimpan response dari upload ke state
                    setAttachmentFile((currentFiles) => [
                        ...currentFiles,
                        {
                            id: uniqueFileId,
                            ...file,
                            data: {
                                url: upload.data?.url,
                                name_original: upload.data?.name_original,
                                size: upload.data?.size,
                                mime_type: upload.data?.mime_type,
                                required_signature: false,
                                file_convert: upload?.data?.file_convert,
                            },
                        },
                    ]);
                } catch (error) {
                    setProgressAttachment((currentProgress) =>
                        currentProgress.map((up) => {
                            if (up.id === uniqueFileId) {
                                return { ...up, status: "error" };
                            }
                            return up;
                        })
                    );
                }
            });

            Promise.all(uploadFiles).then(() => {});
        }
    };

    const resetForm = () => {
        setFormData({
            kepada: null,
            jenis_surat: null,
            perihal: null,
            kop_surat: null,
            content_surat: null,
            unit_bisnis_id: null,
        });
        setAttachmentFile([]);
        setProgressAttachment([]);
    };

    const handleSubmit = async () => {
        formData.content_surat = editorRef.current?.getContent() ?? "";
        formData.attachment = attachmentFile;
        const errors = validateForm(formData);
        if (Object.keys(errors).length) {
            console.log(errors);
            return;
        }

        const csrf_token = document.querySelector('meta[name="csrf-token"]');

        setLoadingSubmit(true);
        try {
            let url = `/surat-keluar/store`;
            if (data_surat?.id) {
                const reply_to = new URLSearchParams(
                    window.location.search
                ).get("reply_to");
                if (reply_to) {
                    formData.reply_to = reply_to;
                }
                url = `/surat-keluar/${data_surat.id}/revision`;
            }
            const { data } = await axios.post(url, formData, {
                headers: {
                    "X-CSRF-TOKEN": csrf_token.content,
                },
            });

            if (data?.status) {
                resetForm();
                Swal.fire({
                    title: "Berhasil",
                    text: data.message,
                    icon: "success",
                    confirmButtonText: "OK",
                }).then(() => {
                    window.location.href = "/surat-keluar";
                });
            } else {
                Swal.fire({
                    title: "Gagal",
                    text: data.message,
                    icon: "error",
                    confirmButtonText: "OK",
                });
            }

            if (data?.error) {
                setErrors(data?.errors ?? {});
            }
        } catch (error) {
            alert("Terjadi kesalahan, silahkan coba lagi");
        }
        setLoadingSubmit(false);
    };

    const validateForm = (data) => {
        const errors = {};
        const rules = {
            kepada: {
                required: "Tujuan email harus diisi",
            },
            jenis_surat: {
                required: "Jenis surat harus diisi",
            },
            unit_bisnis_id: {
                required: "Unit bisnis harus diisi",
            },
            perihal: {
                required: "Perihal surat harus diisi",
            },
            kop_surat: {
                required: "Kop surat harus diisi",
            },
            content_surat: {
                required: "Isi surat harus diisi",
            },
            ukuran: {
                required: "Ukuran surat harus diisi",
            },
        };

        if (data_surat?.id) {
            delete rules.jenis_surat;
            delete rules.kop_surat;
            delete rules.perihal;
            delete rules.kepada;
            delete rules.unit_bisnis_id;
        }

        Object.keys(rules).forEach((field) => {
            const value = data[field];
            const rule = rules[field];
            if (rule.required) {
                if (Array.isArray(value)) {
                    if (!value.length) errors[field] = rule.required;
                } else if (typeof value === "object" && value !== null) {
                    if (Object.keys(value).length === 0)
                        errors[field] = rule.required;
                } else if (!value) {
                    errors[field] = rule.required;
                }
            }
        });

        setErrors(errors);

        return errors;
    };

    const handlePreview = async () => {
        const content = editorRef.current?.getContent() ?? "";
        try {
            const { data } = await axios.post("/surat-keluar/preview", {
                ukuran_kertas: formData.ukuran?.value,
                content_surat: content,
                kop_surat: formData.kop_surat?.value,
                surat_id: data_surat?.id ?? null,
                perihal: formData.perihal,
            });
            if (data?.status) {
                window.open(data.url, "_blank");
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text:
                        data?.message ??
                        "Terjadi kesalahan saat membuat pratinjau!",
                });
            }
        } catch (error) {
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text:
                    error?.response?.data?.message ??
                    "Terjadi kesalahan saat membuat pratinjau!",
            });
        }
    };

    useEffect(() => {
        if (editorRef.current) {
            editorRef.current.setContent(editorRef.current.getContent());
        }
    }, [formData.ukuran]);

    useEffect(() => {
        if (editorRef.current) {
            // Update paper size attribute
            document.body.setAttribute('data-paper-size', formData?.ukuran?.value || 'A4');

            // Force editor to update its content style
            const editor = editorRef.current;

            // Calculate proper dimensions for paper sizes
            const paperDimensions = {
                A4: { width: "210mm", height: "297mm" },
                F4: { width: "216mm", height: "330mm" }
            };

            const currentPaper = paperDimensions[formData?.ukuran?.value] || paperDimensions.A4;

            // Simple style update for paper size
            const newStyle = `
                body {
                    font-family: Arial, Helvetica, sans-serif;
                    font-size: 12pt;
                    width: ${currentPaper.width};
                    max-width: ${currentPaper.width};
                    margin: 0 auto;
                    padding: 20px;
                    box-sizing: border-box;
                    background: white;
                    min-height: ${currentPaper.height};
                    line-height: 1.5;
                }
            `;

            // Update the editor's content CSS
            const doc = editor.getDoc();
            let styleElement = doc.getElementById('dynamic-content-style');
            if (!styleElement) {
                styleElement = doc.createElement('style');
                styleElement.id = 'dynamic-content-style';
                doc.head.appendChild(styleElement);
            }
            styleElement.textContent = newStyle;

            // Refresh editor display with proper width
            editor.getBody().style.width = currentPaper.width;
            editor.getBody().style.maxWidth = currentPaper.width;
            editor.getBody().style.minHeight = currentPaper.height;
            editor.nodeChanged();
        }
    }, [formData.ukuran, setting]);

    return (
        <div className="content-area items-details-active">
            <div className="items-details">
                <div className="items-details-header bg-white sticky-top justify-content-between">
                    <div className="d-flex align-items-center">
                        <a
                            href="/surat-keluar"
                            className="avatar-text avatar-md item-info-close"
                            data-bs-toggle="tooltip"
                            data-bs-trigger="hover"
                            title="Back"
                        >
                            <i className="feather-arrow-left"></i>
                        </a>
                        <span className="vr mx-4"></span>
                        <div className="d-flex align-items-center">
                            <h4 className="fw-bold mb-0 text-dark text-truncate-1-line">
                                {data_surat?.id ?? null
                                    ? " Edit Surat Keluar"
                                    : " Buat Surat Keluar"}
                            </h4>
                        </div>
                    </div>
                    <div className="d-flex align-items-center gap-3">
                        <button
                            type="button"
                            onClick={() => handlePreview()}
                            className="btn btn-outline-primary rounded-3"
                        >
                            PREVIEW
                        </button>
                        {/* <button className="btn btn-outline-primary rounded-3">
                            <i className="feather-download pe-2"></i>
                            Download PDF
                        </button> */}
                    </div>
                </div>
                <div className="items-details-footer mail-action-editor mx-4 mt-3 mb-5 border border-top-0 rounded-3">
                    <div className="pt-4 px-4">
                        {/* <div
                            className=" mb-3 px-4 py-2 rounded-sm fs-14"
                            style={{
                                backgroundColor: "#EFFDFF",
                                color: "#40474D",
                            }}
                        >
                            No Surat Keluar <span className="fw-bold">HD</span>
                        </div> */}
                        {!(data_surat?.id ?? null) && (
                            <div className="row">
                                <div className="col-md-4 mb-3">
                                    <label className="form-label">
                                        Unit Bisnis
                                    </label>
                                    <Select
                                        options={user_unit_bisnis_data}
                                        placeholder="Pilih unit bisnis"
                                        isClearable
                                        styles={{
                                            control: (baseStyles) => ({
                                                ...baseStyles,
                                                borderColor: "#e5e7eb",
                                                boxShadow: "none",
                                                padding: "4px 6px",
                                                borderRadius: "5px",
                                            }),
                                            option: (baseStyles, state) => ({
                                                ...baseStyles,
                                                backgroundColor:
                                                    state.isSelected
                                                        ? "#EDF2F7"
                                                        : "#fff",
                                                color: "#2D3748",
                                                "&:hover": {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                                "&:active": {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                                '&[data-selected="true"]': {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                            }),
                                            menu: (baseStyles) => ({
                                                ...baseStyles,
                                                border: "1px solid #e5e7eb",
                                                boxShadow:
                                                    "0 1px 2px 0 rgba(0, 0, 0, 0.10)",
                                                zIndex: 9999,
                                            }),
                                        }}
                                        onChange={async (value) => {
                                            if (!value) {
                                                setUserData([]);
                                                setFormData((prevState) => ({
                                                    ...prevState,
                                                    kepada: null,
                                                    jenis_surat: null,
                                                    perihal: null,
                                                    kop_surat: null,
                                                    unit_bisnis_id: null,
                                                }));

                                                return;
                                            }

                                            await getDataKopSurat({
                                                unit_bisnis_id: value?.id,
                                            });

                                            await getDataJenisSurat({
                                                unit_bisnis_id: value?.id,
                                            });
                                            await getDataFor({
                                                unit_bisnis_id: value?.id,
                                            });

                                            handleChanges({
                                                target: {
                                                    name: "unit_bisnis_id",
                                                    value: value,
                                                },
                                            });
                                        }}
                                    />
                                    {errors.unit_bisnis_id && (
                                        <div className="text-danger mt-2">
                                            {errors.unit_bisnis_id ??
                                                errors.unit_bisnis_id[0]}
                                        </div>
                                    )}
                                </div>
                                <div className="col-md-4 mb-3">
                                    <label className="form-label">
                                        Kop Surat
                                    </label>
                                    <Select
                                        options={kopSuratData}
                                        placeholder="Pilih kop surat"
                                        isClearable
                                        isDisabled={!formData.unit_bisnis_id}
                                        value={formData.kop_surat}
                                        styles={{
                                            control: (baseStyles) => ({
                                                ...baseStyles,
                                                borderColor: "#e5e7eb",
                                                boxShadow: "none",
                                                padding: "4px 6px",
                                                borderRadius: "5px",
                                            }),
                                            option: (baseStyles, state) => ({
                                                ...baseStyles,
                                                backgroundColor:
                                                    state.isSelected
                                                        ? "#EDF2F7"
                                                        : "#fff",
                                                color: "#2D3748",
                                                "&:hover": {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                                "&:active": {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                                '&[data-selected="true"]': {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                            }),
                                            menu: (baseStyles) => ({
                                                ...baseStyles,
                                                border: "1px solid #e5e7eb",
                                                boxShadow:
                                                    "0 1px 2px 0 rgba(0, 0, 0, 0.10)",
                                                zIndex: 9999,
                                            }),
                                        }}
                                        onChange={(value) => {
                                            handleChanges({
                                                target: {
                                                    name: "kop_surat",
                                                    value: value,
                                                },
                                            });
                                        }}
                                    />
                                    {errors.kop_surat && (
                                        <div className="text-danger mt-2">
                                            {errors.kop_surat ??
                                                errors.kop_surat[0]}
                                        </div>
                                    )}
                                </div>
                                <div className="col-md-4 mb-3">
                                    <label className="form-label">
                                        Jenis Surat
                                    </label>
                                    <Select
                                        options={jenisSuratData}
                                        placeholder="Pilih jenis surat"
                                        isClearable
                                        value={formData.jenis_surat}
                                        isDisabled={!formData.unit_bisnis_id}
                                        styles={{
                                            control: (baseStyles) => ({
                                                ...baseStyles,
                                                borderColor: "#e5e7eb",
                                                boxShadow: "none",
                                                padding: "4px 6px",
                                                borderRadius: "5px",
                                            }),
                                            option: (baseStyles, state) => ({
                                                ...baseStyles,
                                                backgroundColor:
                                                    state.isSelected
                                                        ? "#EDF2F7"
                                                        : "#fff",
                                                color: "#2D3748",
                                                "&:hover": {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                                "&:active": {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                                '&[data-selected="true"]': {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                            }),
                                            menu: (baseStyles) => ({
                                                ...baseStyles,
                                                border: "1px solid #e5e7eb",
                                                boxShadow:
                                                    "0 1px 2px 0 rgba(0, 0, 0, 0.10)",
                                                zIndex: 9999,
                                            }),
                                        }}
                                        onChange={(value) => {
                                            handleChanges({
                                                target: {
                                                    name: "jenis_surat",
                                                    value: value,
                                                },
                                            });
                                        }}
                                    />
                                    {errors.jenis_surat && (
                                        <div className="text-danger mt-2">
                                            {errors.jenis_surat ??
                                                errors.jenis_surat[0]}
                                        </div>
                                    )}
                                </div>
                                <div className="col-md-5 mb-3">
                                    <label className="form-label">Kepada</label>
                                    <Select
                                        options={userData}
                                        isMulti
                                        value={formData.kepada}
                                        isDisabled={!formData.unit_bisnis_id}
                                        placeholder="Pilih tujuan email"
                                        filterOption={(option, inputValue) => {
                                            const inputValueLower =
                                                inputValue.toLowerCase();
                                            if (!inputValue) return option;
                                            return (
                                                option?.data?.name
                                                    ?.toLowerCase()
                                                    .includes(
                                                        inputValueLower
                                                    ) ||
                                                option?.data?.user_roles?.some(
                                                    (role) =>
                                                        role?.nama
                                                            ?.toLowerCase()
                                                            .includes(
                                                                inputValueLower
                                                            ) ||
                                                        role?.email
                                                            ?.toLowerCase()
                                                            .includes(
                                                                inputValueLower
                                                            )
                                                )
                                            );
                                        }}
                                        formatOptionLabel={(
                                            option,
                                            { context }
                                        ) => {
                                            if (context === "value") {
                                                return (
                                                    <div className="d-flex align-items-center gap-3">
                                                        <div className="fs-12">
                                                            {option.name}
                                                        </div>
                                                    </div>
                                                );
                                            } else {
                                                return (
                                                    <div className="d-flex align-items-center gap-3">
                                                        <div>
                                                            <div className="fs-14 fw-bold">
                                                                {option.name}
                                                            </div>
                                                            <div className="fs-12 d-flex flex-wrap gap-3 mt-2">
                                                                {option?.user_roles?.map(
                                                                    (
                                                                        user,
                                                                        index
                                                                    ) => (
                                                                        <div
                                                                            key={
                                                                                index
                                                                            }
                                                                            className="badge bg-light text-secondary text-start"
                                                                        >
                                                                            <div>
                                                                                {
                                                                                    user?.nama
                                                                                }
                                                                            </div>
                                                                            <div className="mt-1">
                                                                                {
                                                                                    user?.email
                                                                                }
                                                                            </div>
                                                                        </div>
                                                                    )
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                );
                                            }
                                        }}
                                        onChange={(value) => {
                                            handleChanges({
                                                target: {
                                                    name: "kepada",
                                                    value: value,
                                                },
                                            });
                                        }}
                                        styles={{
                                            control: (baseStyles) => ({
                                                ...baseStyles,
                                                borderColor: "#e5e7eb",
                                                boxShadow: "none",
                                                padding: "4px 6px",
                                                borderRadius: "5px",
                                            }),
                                            option: (baseStyles, state) => ({
                                                ...baseStyles,
                                                backgroundColor:
                                                    state.isSelected
                                                        ? "#EDF2F7"
                                                        : "#fff",
                                                color: "#2D3748",
                                                "&:hover": {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                                "&:active": {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                                '&[data-selected="true"]': {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                            }),
                                            multiValue: (baseStyles) => ({
                                                ...baseStyles,
                                                backgroundColor: "#fff",
                                                borderRadius: "12px",
                                                border: "1px solid #D5D7D8",
                                            }),
                                            menu: (baseStyles) => ({
                                                ...baseStyles,
                                                border: "1px solid #e5e7eb",
                                                boxShadow:
                                                    "0 1px 2px 0 rgba(0, 0, 0, 0.10)",
                                                zIndex: 9999,
                                            }),
                                        }}
                                    />
                                    {errors.kepada && (
                                        <div className="text-danger mt-2">
                                            {errors.kepada ?? errors.kepada[0]}
                                        </div>
                                    )}
                                </div>
                                <div className="col-md-5 mb-3">
                                    <label className="form-label">
                                        Perihal
                                    </label>
                                    <input
                                        type="text"
                                        className="form-control"
                                        placeholder="Masukan perihal surat"
                                        name="perihal"
                                        onChange={handleChanges}
                                        value={formData.perihal ?? ""}
                                        disabled={
                                            formData.unit_bisnis_id
                                                ? false
                                                : true
                                        }
                                    />
                                    {errors.perihal && (
                                        <div className="text-danger mt-2">
                                            {errors.perihal ??
                                                errors.jenis_surat[0]}
                                        </div>
                                    )}
                                </div>
                                <div className="col-md-2 mb-3">
                                    <label className="form-label">Ukuran</label>
                                    <Select
                                        options={[
                                            { value: "A4", label: "A4" },
                                            { value: "F4", label: "F4" },
                                        ]}
                                        placeholder="Pilih ukuran"
                                        value={formData.ukuran}
                                        styles={{
                                            control: (baseStyles) => ({
                                                ...baseStyles,
                                                borderColor: "#e5e7eb",
                                                boxShadow: "none",
                                                padding: "4px 6px",
                                                borderRadius: "5px",
                                            }),
                                            option: (baseStyles, state) => ({
                                                ...baseStyles,
                                                backgroundColor:
                                                    state.isSelected
                                                        ? "#EDF2F7"
                                                        : "#fff",
                                                color: "#2D3748",
                                                "&:hover": {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                                "&:active": {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                                '&[data-selected="true"]': {
                                                    backgroundColor: "#EDF2F7",
                                                },
                                            }),
                                            menu: (baseStyles) => ({
                                                ...baseStyles,
                                                border: "1px solid #e5e7eb",
                                                boxShadow:
                                                    "0 1px 2px 0 rgba(0, 0, 0, 0.10)",
                                                zIndex: 9999,
                                            }),
                                        }}
                                        onChange={(value) => {
                                            handleChanges({
                                                target: {
                                                    name: "ukuran",
                                                    value: value,
                                                },
                                            });
                                        }}
                                    />
                                    {errors.ukuran && (
                                        <div className="text-danger mt-2">
                                            {errors.ukuran ?? errors.ukuran[0]}
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}
                    </div>
                    <div className="px-4 pt-3 pb-4">
                        <hr />
                        <div
                            className="p-2 mb-3"
                            style={{
                                color: "#666C71",
                                backgroundColor: "#F5F6F6",
                            }}
                        >
                            {"{kop_surat}"} &gt; Kop Surat <br />
                            {"{perihal}"} &gt; Perihal Surat <br />
                            {"{no_surat}"} &gt; Nomor Surat <br />
                            {"{tanggal_surat|format_tanggal}"} &gt; Tanggal
                            Surat (Format: d/m/Y, e.g.,{" "}
                            {"{tanggal_surat|d/m/Y}"}) &nbsp;
                            <a
                                href="/assets/format-tanggal.pdf"
                                target="_blank"
                                rel="noreferrer"
                                style={{
                                    textDecoration: "underline",
                                    fontStyle: "italic",
                                }}
                            >
                                Lihat Format Tanggal
                            </a>{" "}
                            <br />
                        </div>
                        <Editor
                            isDisabled={loadingSubmit}
                            apiKey="5391u6bfka4eor1n9a5d1r66gknipnlrbo7m37uxsb5nyye3"
                            onInit={(_evt, editor) => (editorRef.current = editor)}
                            initialValue={data_surat?.content_surat?.content ?? ""}
                            init={{
                                min_height: 400,
                                width: "100%",
                                menubar: "file edit view insert format tools table help",
                                plugins: "visualblocks advlist autolink lists link image charmap preview anchor searchreplace visualblocks code fullscreen insertdatetime media table code help wordcount",
                                toolbar: "undo redo | blocks | bold italic underline link image table | fontfamily btnIncreaseFontSize fontsize btnDecreaseFontSize btnNewPage | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | basicitem backcolor | help",
                                toolbar_sticky: true,
                                autosave_interval: "30s",
                                autosave_prefix: "{path}{query}-{id}-",
                                autosave_restore_when_empty: false,
                                autosave_retention: "2m",
                                image_advtab: true,
                                paste_as_text: false,
                                paste_data_images: true,
                                paste_merge_formats: true,
                                paste_postprocess: function(plugin, args) {
                                    // Force consistent formatting on pasted content
                                    const content = args.node;
                                    const walker = new tinymce.dom.TreeWalker(content, content);
                                    let node;
                                    while ((node = walker.next())) {
                                        if (node.nodeType === 1) { // Element node
                                            // Force font family and size
                                            node.style.fontFamily = 'Arial, Helvetica, sans-serif';
                                            node.style.fontSize = '12pt';

                                            // Handle tables specifically
                                            if (node.tagName === 'TABLE') {
                                                node.style.borderCollapse = 'collapse';
                                                node.style.width = '100%';
                                                node.style.fontSize = '12pt';
                                                node.style.fontFamily = 'Arial, Helvetica, sans-serif';
                                            }

                                            if (node.tagName === 'TD' || node.tagName === 'TH') {
                                                node.style.border = '1px solid #000';
                                                node.style.padding = '8pt';
                                                node.style.fontSize = '12pt';
                                                node.style.fontFamily = 'Arial, Helvetica, sans-serif';
                                                node.style.verticalAlign = 'top';
                                            }
                                        }
                                    }
                                },
                                table_default_attributes: {
                                    border: '1',
                                    cellpadding: '8',
                                    cellspacing: '0',
                                    style: 'border-collapse: collapse; width: 100%; font-size: 12pt; font-family: Arial, Helvetica, sans-serif;'
                                },
                                table_default_styles: {
                                    'border-collapse': 'collapse',
                                    'width': '100%',
                                    'font-size': '12pt',
                                    'font-family': 'Arial, Helvetica, sans-serif'
                                },
                                table_cell_class_list: [
                                    {title: 'None', value: ''},
                                    {title: 'No Border', value: 'no-border'},
                                    {title: 'Center', value: 'text-center'},
                                    {title: 'Left', value: 'text-left'},
                                    {title: 'Right', value: 'text-right'}
                                ],
                                table_class_list: [
                                    {title: 'Default', value: ''},
                                    {title: 'No Border', value: 'no-border'}
                                ],
                                menu: {
                                    huruf: {
                                        title: "Ukuran Huruf",
                                        items: "menuIncreaseFontSize menuDecreaseFontSize btnSizeInput",
                                    },
                                },
                                link_list: [
                                    {title: "My page 1", value: "https://www.tiny.cloud"},
                                    {title: "My page 2", value: "http://www.moxiecode.com"},
                                ],
                                image_list: [
                                    {title: "My page 1", value: "https://www.tiny.cloud"},
                                    {title: "My page 2", value: "http://www.moxiecode.com"},
                                ],
                                image_class_list: [
                                    {title: "None", value: ""},
                                    {title: "Some class", value: "class-name"},
                                ],
                                importcss_append: true,
                                file_picker_callback: function (cb, value, meta) {
                                    var input = document.createElement("input");
                                    input.setAttribute("type", "file");
                                    input.setAttribute("accept", "image/*");
                                    input.onchange = async function () {
                                        var file = this.files[0];
                                        var formData = new FormData();
                                        formData.append("file", file);

                                        const { data } = await axios.post(
                                            `/file-manager/upload`,
                                            formData,
                                            {
                                                headers: {
                                                    "Content-Type": "multipart/form-data",
                                                },
                                            }
                                        );
                                        if (data.status) {
                                            var url = data.url;
                                            if (url.startsWith("../")) {
                                                url = url.substring(3);
                                            }
                                            cb(url, {title: file.name});
                                        }
                                    };
                                    input.click();
                                },
                                image_caption: true,
                                quickbars_selection_toolbar: "bold italic | quicklink h2 h3 blockquote quickimage quicktable",
                                noneditable_noneditable_class: "mceNonEditable",
                                toolbar_mode: "sliding",
                                contextmenu: "link image imagetools table",
                                skin: "oxide",
                                content_css: "default",
                                font_formats: "Andale Mono=andale mono,times; Arial=arial,helvetica,sans-serif; Arial Black=arial black,avant garde; Book Antiqua=book antiqua,palatino; Courier New=courier new,courier; Georgia=georgia,palatino; Helvetica=helvetica; Impact=impact,chicago; Symbol=symbol; Tahoma=tahoma,arial,helvetica,sans-serif; Terminal=terminal,monaco; Times New Roman=times new roman,times; Verdana=verdana,geneva; Webdings=webdings; Wingdings=wingdings,zapf dingbats",
                                relative_urls: false,
                                remove_script_host: false,
                                content_style: (() => {
                                    // Calculate proper dimensions for paper sizes
                                    const paperDimensions = {
                                        A4: { width: "210mm", height: "297mm" },
                                        F4: { width: "216mm", height: "330mm" }
                                    };

                                    const currentPaper = paperDimensions[formData?.ukuran?.value] || paperDimensions.A4;

                                    return `
                                        body {
                                            font-family: Arial, Helvetica, sans-serif;
                                            font-size: 12pt;
                                            width: ${currentPaper.width};
                                            max-width: ${currentPaper.width};
                                            margin: 0 auto;
                                            padding: 20px;
                                            box-sizing: border-box;
                                            background: white;
                                            min-height: ${currentPaper.height};
                                            line-height: 1.5;
                                        }
                                        p {
                                            margin: 0 0 12pt 0;
                                            font-size: 12pt;
                                            line-height: 1.5;
                                            font-family: Arial, Helvetica, sans-serif;
                                        }
                                        table {
                                            border-collapse: collapse;
                                            width: 100%;
                                            font-size: 12pt;
                                            margin: 12pt 0;
                                            font-family: Arial, Helvetica, sans-serif;
                                        }
                                        table td, table th {
                                            border: 1px solid #000;
                                            padding: 8pt;
                                            font-size: 12pt;
                                            vertical-align: top;
                                            font-family: Arial, Helvetica, sans-serif;
                                            line-height: 1.5;
                                        }
                                        table th {
                                            background-color: #f5f5f5;
                                            font-weight: bold;
                                        }
                                        table.no-border, table.no-border td, table.no-border th {
                                            border: none;
                                        }
                                        h1, h2, h3, h4, h5, h6 {
                                            font-size: 12pt;
                                            font-weight: bold;
                                            margin: 12pt 0 6pt 0;
                                            font-family: Arial, Helvetica, sans-serif;
                                            line-height: 1.5;
                                        }
                                        ul, ol {
                                            margin: 12pt 0;
                                            padding-left: 24pt;
                                            font-size: 12pt;
                                            font-family: Arial, Helvetica, sans-serif;
                                        }
                                        li {
                                            font-size: 12pt;
                                            margin: 6pt 0;
                                            font-family: Arial, Helvetica, sans-serif;
                                            line-height: 1.5;
                                        }
                                    `;
                                })(),
                                setup: function (editor) {
                                    const defaultFontSize = "12pt";
                                    
                                    // Custom buttons for font size
                                    const btnIncrease = {
                                        icon: "plus",
                                        onAction: (api) => {
                                            let fontSize = editor.selection.getNode().style.fontSize;
                                            if (!fontSize) fontSize = defaultFontSize;
                                            let value = parseInt(fontSize.substring(0, 2));
                                            fontSize = `${value + 1}pt`;
                                            editor.selection.getNode().style.fontSize = fontSize;
                                            let styleText = editor.selection.getNode().getAttribute("style");
                                            styleText = styleText.replaceAll('"', "'");
                                            editor.selection.getNode().setAttribute("data-mce-style", styleText);
                                        },
                                    };

                                    const btnDecrease = {
                                        icon: "horizontal-rule",
                                        onAction: (api) => {
                                            let fontSize = editor.selection.getNode().style.fontSize;
                                            if (!fontSize) fontSize = defaultFontSize;
                                            let value = parseInt(fontSize.substring(0, 2));
                                            fontSize = `${value - 1}pt`;
                                            editor.selection.getNode().style.fontSize = fontSize;
                                            let styleText = editor.selection.getNode().getAttribute("style");
                                            styleText = styleText.replaceAll('"', "'");
                                            editor.selection.getNode().setAttribute("data-mce-style", styleText);
                                        },
                                    };

                                    editor.ui.registry.addButton("btnIncreaseFontSize", btnIncrease);
                                    editor.ui.registry.addButton("btnDecreaseFontSize", btnDecrease);

                                    const newText = {
                                        text: "New Page",
                                        tooltip: "Tambah Halaman Baru",
                                        onAction: function () {
                                            editor.insertContent("<div>_new-page_</div>");
                                        },
                                    };
                                    editor.ui.registry.addButton("btnNewPage", newText);

                                    // Force font size on new content
                                    editor.on('NewBlock', function(e) {
                                        if (e.newBlock && !e.newBlock.style.fontSize) {
                                            e.newBlock.style.fontSize = '12pt';
                                        }
                                    });

                                    // Handle table creation with proper styling
                                    editor.on('ObjectResized', function(e) {
                                        if (e.target.nodeName === 'TABLE') {
                                            // Maintain table proportions
                                            const table = e.target;
                                            table.style.fontSize = '12pt';
                                            const cells = table.querySelectorAll('td, th');
                                            cells.forEach(cell => {
                                                if (!cell.style.fontSize) {
                                                    cell.style.fontSize = '12pt';
                                                }
                                            });
                                        }
                                    });

                                    // Handle paste events to maintain formatting
                                    editor.on('PastePreProcess', function(e) {
                                        // Clean and standardize pasted content
                                        let content = e.content;

                                        // Remove unwanted styles but keep structure
                                        content = content.replace(/style="[^"]*"/g, '');
                                        content = content.replace(/font-family:[^;]*;?/gi, '');
                                        content = content.replace(/font-size:[^;]*;?/gi, '');

                                        // Apply consistent styling
                                        content = content.replace(/<p[^>]*>/g, '<p style="font-size: 12pt; margin: 0 0 12pt 0; font-family: Arial, Helvetica, sans-serif; line-height: 1.5;">');
                                        content = content.replace(/<table[^>]*>/g, '<table style="border-collapse: collapse; width: 100%; font-size: 12pt; font-family: Arial, Helvetica, sans-serif; margin: 12pt 0;">');
                                        content = content.replace(/<td[^>]*>/g, '<td style="border: 1px solid #000; padding: 8pt; font-size: 12pt; font-family: Arial, Helvetica, sans-serif; vertical-align: top; line-height: 1.5;">');
                                        content = content.replace(/<th[^>]*>/g, '<th style="border: 1px solid #000; padding: 8pt; font-size: 12pt; font-family: Arial, Helvetica, sans-serif; font-weight: bold; background-color: #f5f5f5; vertical-align: top; line-height: 1.5;">');
                                        content = content.replace(/<div[^>]*>/g, '<div style="font-size: 12pt; font-family: Arial, Helvetica, sans-serif;">');
                                        content = content.replace(/<span[^>]*>/g, '<span style="font-size: 12pt; font-family: Arial, Helvetica, sans-serif;">');
                                        content = content.replace(/<h([1-6])[^>]*>/g, '<h$1 style="font-size: 12pt; font-family: Arial, Helvetica, sans-serif; font-weight: bold; margin: 12pt 0 6pt 0; line-height: 1.5;">');
                                        content = content.replace(/<li[^>]*>/g, '<li style="font-size: 12pt; font-family: Arial, Helvetica, sans-serif; margin: 6pt 0; line-height: 1.5;">');

                                        e.content = content;
                                    });

                                    editor.on('NodeChange', function (e) {
                                        const node = e.element;
                                        if (node.style) {
                                            const styles = node.style;
                                            for (let i = 0; i < styles.length; i++) {
                                                const style = styles[i];
                                                node.style.setProperty(style, styles.getPropertyValue(style), "important");
                                            }
                                        }
                                    });

                                    // Update editor content style when paper size changes
                                    editor.on('init', function() {
                                        const updateContentStyle = () => {
                                            const newStyle = `
                                                @page { 
                                                    size: ${formData?.ukuran?.value || 'A4'}; 
                                                    margin: ${setting?.surat_margin_top || 25}mm ${setting?.surat_margin_right || 25}mm ${setting?.surat_margin_bottom || 25}mm ${setting?.surat_margin_left || 25}mm; 
                                                } 
                                                body {
                                                    font-family: Arial, Helvetica, sans-serif;
                                                    font-size: 12pt;
                                                    width: ${formData?.ukuran?.value === "F4" ? "216mm" : "210mm"};
                                                    max-width: ${formData?.ukuran?.value === "F4" ? "216mm" : "210mm"};
                                                    margin: 0 auto;
                                                    padding: 20px;
                                                    box-sizing: border-box;
                                                    background: white;
                                                    min-height: ${formData?.ukuran?.value === "F4" ? "330mm" : "297mm"};
                                                    line-height: 1.5;
                                                }
                                                p {
                                                    margin: 0 0 12pt 0;
                                                    font-size: 12pt !important;
                                                    line-height: 1.5;
                                                    font-family: Arial, Helvetica, sans-serif;
                                                }
                                                table {
                                                    border-collapse: collapse !important;
                                                    width: 100% !important;
                                                    font-size: 12pt !important;
                                                    margin: 12pt 0 !important;
                                                    font-family: Arial, Helvetica, sans-serif;
                                                }
                                                table td, table th {
                                                    border: 1px solid #000 !important;
                                                    padding: 8pt !important;
                                                    font-size: 12pt !important;
                                                    vertical-align: top;
                                                    font-family: Arial, Helvetica, sans-serif;
                                                    line-height: 1.5;
                                                }
                                                table th {
                                                    background-color: #f5f5f5;
                                                    font-weight: bold;
                                                }
                                                table.no-border, table.no-border td, table.no-border th {
                                                    border: none !important;
                                                }
                                                h1, h2, h3, h4, h5, h6 {
                                                    font-size: 12pt !important;
                                                    font-weight: bold;
                                                    margin: 12pt 0 6pt 0;
                                                    font-family: Arial, Helvetica, sans-serif;
                                                    line-height: 1.5;
                                                }
                                                ul, ol {
                                                    margin: 12pt 0;
                                                    padding-left: 24pt;
                                                    font-size: 12pt !important;
                                                    font-family: Arial, Helvetica, sans-serif;
                                                }
                                                li {
                                                    font-size: 12pt !important;
                                                    margin: 6pt 0;
                                                    font-family: Arial, Helvetica, sans-serif;
                                                    line-height: 1.5;
                                                }
                                                div, span {
                                                    font-size: 12pt !important;
                                                    font-family: Arial, Helvetica, sans-serif;
                                                }
                                                strong, b {
                                                    font-size: 12pt !important;
                                                    font-family: Arial, Helvetica, sans-serif;
                                                }
                                                em, i {
                                                    font-size: 12pt !important;
                                                    font-family: Arial, Helvetica, sans-serif;
                                                }
                                            `;
                                            
                                            // Update the editor's content CSS
                                            const doc = editor.getDoc();
                                            let styleElement = doc.getElementById('dynamic-content-style');
                                            if (!styleElement) {
                                                styleElement = doc.createElement('style');
                                                styleElement.id = 'dynamic-content-style';
                                                doc.head.appendChild(styleElement);
                                            }
                                            styleElement.textContent = newStyle;
                                        };
                                        
                                        // Initial update
                                        updateContentStyle();
                                        
                                        // Listen for paper size changes
                                        const observer = new MutationObserver(updateContentStyle);
                                        observer.observe(document.body, { 
                                            attributes: true, 
                                            subtree: true, 
                                            attributeFilter: ['data-paper-size'] 
                                        });
                                    });
                                },
                                init_instance_callback: function (editor) {
                                    // Set paper size attribute for CSS updates
                                    document.body.setAttribute('data-paper-size', formData?.ukuran?.value || 'A4');
                                },
                            }}
                        />
                        {errors.content_surat && (
                            <div className="text-danger mt-2">
                                {errors.content_surat}
                            </div>
                        )}

                        <div className="row mt-4">
                            {progressAttachment.map((item, index) => (
                                <div className="col-md-4" key={index}>
                                    <div
                                        className={`card shadow-none border overflow-hidden mb-2 ${
                                            item.status !== "uploaded"
                                                ? "bg-body"
                                                : ""
                                        }`}
                                    >
                                        <div className="card-body position-relative py-2 ps-3 pe-5">
                                            <div className="d-flex align-items-center gap-2 justify-content-between">
                                                <div className="fw-medium text-dark text-truncate">
                                                    {item.name}
                                                </div>
                                                <div>
                                                    {item.file_size && (
                                                        <div className="text-muted fs-12 text-nowrap">
                                                            (
                                                            {item.file_size
                                                                ? `${(
                                                                      item.file_size /
                                                                      1024
                                                                  ).toFixed(
                                                                      2
                                                                  )} KB`
                                                                : ""}
                                                            )
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                            {item.status === "uploaded" ? (
                                                <button
                                                    type="button"
                                                    onClick={() => {
                                                        setAttachmentFile(
                                                            (currentFiles) =>
                                                                currentFiles.filter(
                                                                    (file) =>
                                                                        file.id !==
                                                                        item.id
                                                                )
                                                        );
                                                        setProgressAttachment(
                                                            (currentProgress) =>
                                                                currentProgress.filter(
                                                                    (
                                                                        progress
                                                                    ) =>
                                                                        progress.id !==
                                                                        item.id
                                                                )
                                                        );
                                                    }}
                                                    className="avatar-text avatar-sm position-absolute bg-danger text-white"
                                                    style={{
                                                        top: "10px",
                                                        right: "8px",
                                                        padding: "8px",
                                                    }}
                                                >
                                                    <i className="feather feather-x fs-14"></i>
                                                </button>
                                            ) : (
                                                <div
                                                    className="spinner-border text-primary"
                                                    role="status"
                                                >
                                                    <span className="visually-hidden">
                                                        Loading...
                                                    </span>
                                                </div>
                                            )}

                                            {parseInt(item.progress) < 100 ? (
                                                <div className="position-relative pt-2 pb-2">
                                                    <div
                                                        className="text-end position-absolute"
                                                        style={{
                                                            top: "-2px",
                                                            right: "-40px",
                                                        }}
                                                    >
                                                        {item.progress}%
                                                    </div>
                                                    <div
                                                        className="progress"
                                                        role="progressbar"
                                                        aria-label="Basic example"
                                                        aria-valuenow="100"
                                                        aria-valuemin="0"
                                                        aria-valuemax="100"
                                                        style={{
                                                            height: "5px",
                                                            borderRadius: "5px",
                                                        }}
                                                    >
                                                        <div
                                                            className="progress-bar"
                                                            style={{
                                                                width: `${item.progress}%`,
                                                                borderRadius:
                                                                    "5px",
                                                            }}
                                                        ></div>
                                                    </div>
                                                </div>
                                            ) : (
                                                ""
                                            )}
                                        </div>
                                    </div>
                                    <div className="form-check">
                                        <input
                                            onChange={(e) => {
                                                setAttachmentFile(
                                                    (currentFiles) =>
                                                        currentFiles.map(
                                                            (file) => {
                                                                if (
                                                                    file.id ===
                                                                    item.id
                                                                ) {
                                                                    return {
                                                                        ...file,
                                                                        data: {
                                                                            ...file.data,
                                                                            required_signature:
                                                                                e
                                                                                    .target
                                                                                    .checked,
                                                                        },
                                                                    };
                                                                }
                                                                return file;
                                                            }
                                                        )
                                                );
                                            }}
                                            className="form-check-input"
                                            type="checkbox"
                                            id={`requiredSignature${item.id}`}
                                            checked={
                                                attachmentFile.find(
                                                    (file) =>
                                                        file.id === item.id
                                                )?.data?.required_signature ??
                                                false
                                            }
                                        />
                                        <label
                                            className="form-check-label fw-bold text-primary italic"
                                            htmlFor={`requiredSignature${item.id}`}
                                        >
                                            Dibutuhkan Tanda Tangan
                                        </label>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                    <div className="px-4 py-3 d-flex align-items-center justify-content-end border-top">
                        <div className="d-flex align-items-center gap-3">
                            <input
                                type="file"
                                className="d-none"
                                id="attachment_file"
                                multiple
                                onChange={handleAttachmentFile}
                                accept="application/pdf, image/*, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .txt"
                            />
                            <label
                                htmlFor="attachment_file"
                                className="btn btn-icon"
                                data-bs-toggle="tooltip"
                                data-bs-trigger="hover"
                                title="Upload Attachments"
                            >
                                <i className="feather-paperclip"></i>
                            </label>
                            <button
                                type="button"
                                onClick={() => {
                                    if (loadingSubmit) return;
                                    handleSubmit();
                                }}
                                style={{ fontSize: "14px" }}
                                className="btn btn-primary"
                                data-bs-toggle="tooltip"
                                data-bs-trigger="hover"
                                title="Kirim Email"
                                disabled={loadingSubmit}
                            >
                                {loadingSubmit ? (
                                    <i className="fas fa-spinner fa-spin me-2"></i>
                                ) : (
                                    <i className="fas fa-paper-plane pe-2"></i>
                                )}
                                {data_surat?.id ?? null
                                    ? "Simpan Revisi"
                                    : "Kirim"}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CreateEditSuraKeluar;
