<?php

use Carbon\Carbon;
use App\Models\Surat;
use App\Models\Setting;
use App\Models\JenisSurat;
use App\Models\UnitBisnis;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;

function upload_file($file, $path, $name){
    $filename = $name . time(). rand(1,9999) .'.' . $file->getClientOriginalExtension();
    $destinationPath = 'uploads/' . $path;

    if (!File::isDirectory($destinationPath)) {
        File::makeDirectory($destinationPath, 0755, true, true);
    }

    $file->move($destinationPath, $filename);

    return $destinationPath . '/' . $filename;
}

function type_surat($type) {
    $name = '';
    switch ($type) {
        case Surat::TYPE_SURAT['surat_keluar']:
            $name = 'Surat';
            break;
        case Surat::TYPE_SURAT['reply']:
            $name = '<PERSON><PERSON>an';
            break;
        case Surat::TYPE_SURAT['forward']:
            $name = 'Disposisi';
            break;
        case Surat::TYPE_SURAT['revision']:
            $name = 'Revisi';
            break;
    }

    return $name;
}

function status_surat($status) {
    $badge = '';
    $icon = '';
    switch ($status) {
        case Surat::STATUS_DRAFT:
            $badge = 'dark';
            $icon = 'fas fa-file-alt';
            break;
        case Surat::STATUS_DIPROSES:
            $badge = 'warning';
            $icon = 'fas fa-info-circle';
            break;
        case Surat::STATUS_DISPOSISI:
            $badge = 'primary';
            $icon = 'fas fa-share-alt';
            break;
        case Surat::STATUS_SELESAI:
            $badge = 'success';
            $icon = 'fas fa-check-circle';
            break;
        case Surat::STATUS_EXPIRED:
            $badge = 'danger';
            $icon = 'fas fa-clock';
            break;
        case Surat::STATUS_REVISI:
            $badge = 'info';
            $icon = 'fas fa-sync-alt';
            break;
    }

    return
    "<span class='border border-$badge text-$badge fw-bold' style='border-radius: 20px; font-size: 12px;padding: 6px 9px'>
    <i class='$icon pe-1 align-middle'></i>
    $status
    </span>";
}

function status_esign($status) {
    $badge = '';
    $icon = '';
    switch ($status) {
        case 'in_progress':
            $badge = 'warning';
            $icon = 'fas fa-info-circle';
            $status = 'In Progress';
            break;
        case 'completed':
            $badge = 'success';
            $icon = 'fas fa-check-circle';
            $status = 'Completed';
            break;
        case 'declined':
            $badge = 'danger';
            $icon = 'fas fa-times-circle';
            $status = 'Declined';
            break;
        case 'voided':
            $badge = 'info';
            $icon = 'fas fa-sync-alt';
            $status = 'Voided';
            break;
    }

    return
    "<span class='border border-$badge text-$badge fw-bold' style='border-radius: 20px; font-size: 12px;padding: 6px 9px'>
    <i class='$icon pe-1 align-middle'></i>
    $status
    </span>";
}

function approve_status($approve_status, $approve_status_name) {
    $badge = '';
    $icon = '';
    switch ($approve_status) {
        case 1:
            $badge = 'success';
            $icon = 'fas fa-check-circle';
            break;
        case 2:
            $badge = 'warning';
            $icon = 'fas fa-info-circle';
            break;
        case 3:
            $badge = 'danger';
            $icon = 'fas fa-times-circle';
            break;
    }

    return "<span class='border border-$badge text-$badge fw-bold' style='border-radius: 20px; font-size: 12px;padding: 6px 9px'>
    <i class='$icon pe-1 align-middle'></i>
    $approve_status_name</span>";
}


function replaceContent($status) {
    $pattern = '/(<img[^>]+src=")(\.\.\/)*(uploads\/file-manager\/[^"]+")/';
    $replacement = '${1}' . url('/') . '/$3';
    return preg_replace($pattern, $replacement, $status);
}


if (!function_exists('canPermission')) {
    function canPermission($permission)
    {
        $user = Auth::user();
        // if ($user->role == 'admin' || ($user->role == 'user' && !$non_user)) {
        //     return true;
        // } else {
            if ($user->can($permission)) {
                return true;
            } else {
                return false;
            }
        // }
    }
}

if (!function_exists('canPermissionMultiple')) {
    function canPermissionMultiple($permissions)
    {
        $user = Auth::user();
        $collect_true = [];
        foreach ($permissions as $p) {
            if ($user->can($p)) {
                $collect_true[] = true;
            }
        }

        return count($collect_true) > 0 ? true : false;
    }
}

if (!function_exists('set_active')) {
    function set_active($url, $output = 'active')
    {
        if (is_array($url)) {
            foreach ($url as $u) {
                if (Route::is($u)) {
                    return $output;
                }
            }
        } else {
            if (Route::is($url)) {
                return $output;
            }
        }
    }
}

// {nomor_urut} -> Nomor Surat
// {kode_jenis_surat} -> Kode Jenis Surat
// {kode_tipe_jenis_surat} -> Kode Tipe Jenis Surat
// {bulan} -> Bulan Aktual
// {tahun} -> Tahun Aktual

function generateNoSurat($data = [
    'jenis_surat_id' => null,
    'unit_bisnis_id' => null,
])
{

    $unit_bisnis = UnitBisnis::find($data['unit_bisnis_id']);
    if(!$unit_bisnis) {
        return [
            'no_surat' => '',
            'no_urut' => '',
            'month_actual' => '',
            'year_actual' => '',
            'tanggal_actual' => '',
        ];
    }

    $surat_latest = Surat::where([
                            'unit_bisnis_id' => $unit_bisnis->id,
                            'jenis_surat_id' => $data['jenis_surat_id'],
                        ])
                    ->orderBy('id', 'desc')
                    ->latest()
                    ->first();
    $jenis_surat_id = $data['jenis_surat_id'] ?? null;
    $jenis_surat = JenisSurat::find($jenis_surat_id);

    $setting_no_surat = $unit_bisnis->setting_no_surat ?? null;
    if(!$setting_no_surat) {
        return '';
    }

    $patterns = [
        '/\{nomor_urut\}/',
        '/\{kode_jenis_surat\}/',
        '/\{kode_tipe_jenis_surat\}/',
        '/\{tanggal\}/',
        '/\{bulan\}/',
        '/\{tahun\}/',
    ];

    $no_urut = 1;
    if ($surat_latest)
        $no_urut = $surat_latest->no_urut + 1;
    else
        $no_urut = $unit_bisnis->start_no_urut ?? 1;

    $month_actual = match(intval(date('n'))) {
        1 => 'I',
        2 => 'II',
        3 => 'III',
        4 => 'IV',
        5 => 'V',
        6 => 'VI',
        7 => 'VII',
        8 => 'VIII',
        9 => 'IX',
        10 => 'X',
        11 => 'XI',
        12 => 'XII',
        default => '',
    };

    $replacements = [
        $no_urut,
        $jenis_surat->kode,
        $jenis_surat->kode_type ?? '',
        date('d'),
        $month_actual,
        date('Y'),
    ];


    $no_surat = preg_replace($patterns, $replacements, $setting_no_surat);

    return [
        'no_surat' => $no_surat,
        'no_urut' => $no_urut,
        'month_actual' => $month_actual,
        'year_actual' => date('Y'),
        'tanggal_actual' => date('d'),
    ];
}

function generateNoSuratRevision($data = [
    'jenis_surat_id' => null,
    'no_urut' => null,
    'no_surat' => null,
    'month_actual' => null,
    'year_actual' => null,
    'tanggal_actual' => null,
    'no_urut_revision' => null,
])
{

    $unit_bisnis = Auth::user()->unitBisnis;
    $jenis_surat = JenisSurat::find($data['jenis_surat_id']);

    $setting_no_surat = $unit_bisnis->setting_no_surat ?? null;
    if(!$setting_no_surat) {
        return '';
    }

    $no_urut_surat_revision = intval($data['no_urut_revision'] ?? 0) + 1;
    $no_urut_surat = "{$data['no_urut']}.{$no_urut_surat_revision}";

    $patterns = [
        '/\{nomor_urut\}/',
        '/\{kode_jenis_surat\}/',
        '/\{kode_tipe_jenis_surat\}/',
        '/\{tanggal\}/',
        '/\{bulan\}/',
        '/\{tahun\}/',
    ];

    $replacements = [
        $no_urut_surat,
        $jenis_surat->kode,
        $jenis_surat->kode_type,
        $data['tanggal_actual'],
        $data['month_actual'],
        $data['year_actual'],
    ];

    $no_surat = preg_replace($patterns, $replacements, $setting_no_surat);

    return [
        'no_surat' => $no_surat,
        'no_urut' => $data['no_urut'],
        'no_urut_revision' => $no_urut_surat_revision,
        'month_actual' => $data['month_actual'],
        'year_actual' => $data['year_actual'],
        'tanggal_actual' => $data['tanggal_actual'],
    ];
}

function getSetting($key)
{
    $setting = Setting::where('key', $key)->first();
    return $setting;
}

function getSettingValue($key)
{
    $setting = Setting::where('key', $key)->first();
    return $setting->value ?? null;
}

function getSettingGroup($group)
{
    $setting = Setting::where('group', $group)->pluck('value', 'key');
    return $setting;
}

function replaceContentImage($content)
{
    $content = preg_replace('/src="(?:http:\/\/|https:\/\/)' . preg_quote(request()->getHost(), '/') . '\/(.*?)"/', 'src="' . public_path('$1') . '"', $content);
    return $content;
}

function parseContent($string, $surat_id = null, $options = [
    'is_pdf' => false,
], $data = [
    'kop_surat' => null,
    'perihal' => null,
    'tanggal_surat' => null,
    'no_surat' => null,
])
{
    $string = str_replace("_new-page_", '<div style="page-break-before: always;"></div>', $string) ?? '';
    $setting = getSettingGroup('default');
    $surat = null;
    if($surat_id) {
        $surat = Surat::with(['kopSurat:id,kop_surat', 'parentSurat:id,perihal'])->find($surat_id);
    }

    $tags = 'kop_surat|perihal|no_surat|tanggal_surat(?:\|[a-zA-Z\s\/\-,.]+)?';
    if (preg_match_all('/\{(' . $tags . ')\}/', $string, $matches)) {
        foreach ($matches[0] as $key => $match) {
            $match_split = explode('|', $matches[1][$key]);
            $tag = $match_split[0];

            switch ($tag) {
                case 'kop_surat':
                    $kop_surat = ($surat ?? null) ? ( $options['is_pdf'] ? ($surat->kopSurat->kop_surat_replaced ?? '') : $surat->kopSurat->kop_surat ?? '') : null;
                    $kop_surat = $data['kop_surat'] ?? $kop_surat;
                    $component = '<div style="height: ' . ($setting['height_kop_surat'] ?? '150') . 'px;">' .
                                  $kop_surat.
                                  '</div>';
                    $replacement = $component;
                    break;
                case 'perihal':
                    $replacement = $surat->parentSurat->perihal ?? $surat->perihal ?? $data['perihal'] ?? '';
                    break;
                case 'no_surat':
                    $replacement = $surat->no_surat ?? $data['no_surat'] ?? '';
                    break;
                case 'tanggal_surat':
                    $date = $surat->tanggal_surat ?? now();
                    $replacement = Carbon::parse($date)->translatedFormat($match_split[1] ?? 'd F Y') ?? 'Invalid Date';
                    break;
                default:
                    $replacement = $match;
                    break;
            }
            $string = str_replace($match, $replacement, $string);
        }
    }

    return $string;
}
