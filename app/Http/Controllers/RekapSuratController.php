<?php

namespace App\Http\Controllers;

use App\Models\Surat;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Yajra\DataTables\DataTables;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;

class RekapSuratController extends Controller
{
    public function index()
    {
        return view('backoffice.rekap-surat.index');
    }

    public function dataTable(Request $request)
    {
        $unit_bisnis_ids = Auth::user()->user_unit_bisnis_data->pluck('id')->toArray();
        $data = Surat::select(
            'id',
            'jenis_surat',
            'perihal',
            'kop_surat_id',
            'dikirim_oleh_id',
            'unit_bisnis_id',
            'content_surat_id',
            'status',
            'created_by_id',
            'created_at',
            'parent_surat_id',
            'no_surat'
        )->with([
            'tandaTanganSurat',
            'tujuanSurat',
            'pengirim:id,nama,unit_bisnis_id', 
            'unitBisnisSurat', 
            'unitBisnis', 
            'childSurat:id,parent_surat_id,created_at,type,perihal', 
            'parentSurat:id,no_surat', 
            'readSurat:id,surat_id',
            'childSurat.readSurat:id,surat_id',
            'childSurat.tujuanSurat',
        ])
            ->where('parent_surat_id', null)
            ->whereHas('unitBisnis', function ($query) use ($unit_bisnis_ids) {
                $query->whereIn('id', $unit_bisnis_ids);
            })
            ->filter($request)
            ->orderBy('created_at', 'desc');

        return DataTables::of($data)
            ->addindexColumn()
            ->addColumn('action', function ($data) use ($request) {
                $action = "<a class='avatar-text avatar-md' href='" . route('surat-masuk.show', $data->parent_surat_id ? $data->parent_surat_id : $data->id) . "'>
                                <i class='feather feather-eye'></i>
                            </a>";

                $action .= "<a class='avatar-text avatar-md' target='_blank' href='" . route('renderPdf', $data->id) . "'>
                                <i class='fas fa-file-pdf text-danger'></i>
                            </a>";

                return "<div class='d-flex align-items-center gap-2'>
                        $action
                        </div>
                        ";
            })->addColumn('tujuan', function ($data) {
                $tujuan_surat_role_id = [];
                $tujuan_surat_user_id = [];
                $tujuan_unit_bisnis_id = [];
    
                foreach ($data->tujuanSurat as $tujuan) {
                    $tujuan_unit_bisnis_id[] = $tujuan->unitBisnis->nama ?? '';
                    if ($tujuan->role_id)
                        $tujuan_surat_role_id[$tujuan->role_id] = $tujuan->role->name ?? '';
                    else
                        $tujuan_surat_user_id[$tujuan->user_id] = $tujuan->user->email ?? '';
                }
    
                foreach ($data->childSurat as $child) {
                    $tujuan_unit_bisnis_id[] = $child->unitBisnis->nama ?? '';
                    foreach ($child->tujuanSurat as $tujuan) {
                        if ($tujuan->role_id)
                            $tujuan_surat_role_id[$tujuan->role_id] = $tujuan->role->name ?? '';
                        else
                            $tujuan_surat_user_id[$tujuan->user_id] = $tujuan->user->email ?? '';
                    }
                }
    
                $tujuan_surat_role_id = array_values(array_unique($tujuan_surat_role_id));
                $tujuan_surat_user_id = array_values(array_unique($tujuan_surat_user_id));
                $tujuan_unit_bisnis_id = array_values(array_unique(array_filter($tujuan_unit_bisnis_id)));
                return implode(', ', $tujuan_surat_role_id);
            })
            ->addColumn('signer', function ($data) {
                $signer = [];
                foreach ($data->tandaTanganSurat as $ttd) {
                    $signer[] = $ttd->user->nama;
                }

                $signer = array_unique($signer);

                return $signer ? implode(', ', $signer) : '';
            })
            ->rawColumns(['action', 'kop_surat','tujuan', 'signer'])
            ->smart(true)
            ->make(true);
    }

    public function downloadRekapExcel(Request $request)
    {
        $unit_bisnis_ids = Auth::user()->user_unit_bisnis_data->pluck('id')->toArray();
        $data = Surat::select(
            'id',
            'jenis_surat',
            'perihal',
            'kop_surat_id',
            'dikirim_oleh_id',
            'unit_bisnis_id',
            'content_surat_id',
            'status',
            'created_by_id',
            'created_at',
            'parent_surat_id',
            'no_surat'
        )->with([
            'tujuanSurat',
            'pengirim:id,nama,unit_bisnis_id', 
            'unitBisnisSurat', 
            'unitBisnis', 
            'childSurat:id,parent_surat_id,created_at,type,perihal', 
            'parentSurat:id,no_surat', 
            'readSurat:id,surat_id',
            'childSurat.readSurat:id,surat_id',
            'childSurat.tujuanSurat',
        ])
            ->where('parent_surat_id', null)
            ->whereHas('unitBisnis', function ($query) use ($unit_bisnis_ids) {
                $query->whereIn('id', $unit_bisnis_ids);
            })
            ->filter($request)
            ->orderBy('created_at', 'desc')
            ->get();

        $excelData = [];
        foreach ($data as $key => $value) {
            $excelData[$key]['Tanggal'] = $value->created_at->format('d F Y H:i');
            $excelData[$key]['No Surat'] = $value->no_surat;
            $excelData[$key]['Jenis Surat'] = $value->jenis_surat;
            $excelData[$key]['Perihal'] = $value->perihal;
            $excelData[$key]['Dikirim Oleh'] = $value->pengirim->nama ?? '';
            $excelData[$key]['Tujuan'] = '';
            $tujuan_surat_role_id = [];
            $tujuan_surat_user_id = [];
            $tujuan_unit_bisnis_id = [];

            foreach ($value->tujuanSurat as $tujuan) {
                $tujuan_unit_bisnis_id[] = $tujuan->unitBisnis->nama ?? '';
                if ($tujuan->role_id)
                    $tujuan_surat_role_id[$tujuan->role_id] = $tujuan->role->name ?? '';
                else
                    $tujuan_surat_user_id[$tujuan->user_id] = $tujuan->user->email ?? '';
            }

            foreach ($value->childSurat as $child) {
                $tujuan_unit_bisnis_id[] = $child->unitBisnis->nama ?? '';
                foreach ($child->tujuanSurat as $tujuan) {
                    if ($tujuan->role_id)
                        $tujuan_surat_role_id[$tujuan->role_id] = $tujuan->role->name ?? '';
                    else
                        $tujuan_surat_user_id[$tujuan->user_id] = $tujuan->user->email ?? '';
                }
            }

            $tujuan_surat_role_id = array_values(array_unique($tujuan_surat_role_id));
            $tujuan_surat_user_id = array_values(array_unique($tujuan_surat_user_id));
            $tujuan_unit_bisnis_id = array_values(array_unique(array_filter($tujuan_unit_bisnis_id)));
            $excelData[$key]['Tujuan'] = implode(', ', $tujuan_surat_role_id);

            $signer = [];
            foreach ($value->tandaTanganSurat as $ttd) {
                $signer[] = $ttd->user->nama;
            }
            $signer = array_unique($signer);
            $excelData[$key]['Penandatangan'] = $signer ? implode(', ', $signer) : '';
        }

        return Excel::download(new \App\Exports\SuratExport($excelData, function($excel) {
            $excel->sheet('Sheet1', function($sheet) {
                $sheet->row(1, function($row) {
                    $row->setBackground('#FFFF00');
                    $row->setFontWeight('bold');
                    $row->setFontSize(12);
                    $row->setHeight(25); 
                });
                $sheet->setAutoSize(true);
            });
        }), 'Rekap_Surat_' . date('dmY') . '.xlsx');
    }
}
