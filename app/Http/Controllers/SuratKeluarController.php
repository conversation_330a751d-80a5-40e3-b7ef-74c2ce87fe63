<?php

namespace App\Http\Controllers;

use PDF;
use Mpdf\Mpdf;
use Carbon\Carbon;
use App\Models\Surat;
use App\Models\KopSurat;
use Milon\Barcode\DNS1D;
use Milon\Barcode\DNS2D;
use App\Models\ContentSurat;
use App\Models\HistorySurat;
use Illuminate\Http\Request;
use App\Models\UnitBisnisSurat;
use App\Models\TandaTanganSurat;
use Illuminate\Support\Facades\DB;
use App\Http\Services\SuratService;
use App\Models\AttachmentFileSurat;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;

class SuratKeluarController extends Controller
{
    protected $suratService;

    public function __construct(SuratService $suratService)
    {
        $this->suratService = $suratService;
    }

    public function index()
    {
        return view('backoffice.surat-keluar.index');
    }

    public function create()
    {
        return view('backoffice.surat-keluar.create-update');
    }

    public function show($id)
    {
        $data = $this->suratService->detailData($id);
        return view('backoffice.surat-keluar.detail', compact('id', 'data'));
    }

    public function store(Request $request)
    {
        $validasi = [
            'jenis_surat' => 'required',
            'content_surat' => 'required',
            'kepada' => 'required|array',
            'kop_surat' => 'required',
            'perihal' => 'required',
        ];

        $request->validate($validasi);

        try {
            DB::transaction(function () use ($request, &$surat) {
                $id = $request->id;
                $perihal = $request->perihal;
                $jenis_surat_id = $request->jenis_surat['value'] ?? '';

                $generate_surat = generateNoSurat([
                    'jenis_surat_id' => $jenis_surat_id,
                    'unit_bisnis_id' => $request->unit_bisnis_id['value'] ?? '',
                ]);

                $surat = Surat::create([
                    'no_surat' => $generate_surat['no_surat'],
                    'no_urut' => $generate_surat['no_urut'],
                    'month_no_surat' => $generate_surat['month_actual'],
                    'year_no_surat' => $generate_surat['year_actual'],
                    'tanggal_no_surat' => $generate_surat['tanggal_actual'],
                    'jenis_surat' => $request->jenis_surat['jenis_surat'] ?? '',
                    'jenis_surat_id' => $jenis_surat_id,
                    'perihal' => $perihal,
                    'kop_surat_id' => $request->kop_surat['value'] ?? '',
                    'status' => Surat::STATUS_DIPROSES,
                    'dikirim_oleh_id' => Auth::id(),
                    'created_by_id' => Auth::id(),
                    'parent_surat_id' => $request->surat_id ?? null,
                    'unit_bisnis_id' => $request->unit_bisnis_id['value'] ?? '',
                    'dikirim_oleh_role_id' => Auth::user()->role_id,
                    'type' => 1, // Type Surat Keluar
                    'ukuran_kertas' => $request->ukuran['value'] ?? 'A4',
                ]);

                UnitBisnisSurat::create([
                    'surat_id' => $surat->id,
                    'unit_bisnis_id' => $request->unit_bisnis_id['value'] ?? '',
                    'surat_parent_id' => $surat->id,
                ]);

                $content_surat = replaceContent($request->content_surat);
                $original_content = $request->original_content;
                $full_content = $content_surat;

                $content_surat = ContentSurat::create([
                    'surat_id' => $surat->id,
                    'content' => $content_surat,
                    'original_content' => $original_content,
                    'full_content' => $full_content,
                    'created_by_id' => Auth::id(),
                    'no_surat' => $generate_surat['no_surat'],
                ]);

                $tujuan_surat = array_map(function ($item) {
                    $is_user_roles = $item['user_roles'] ?? false;
                    return [
                        'role_id' => $is_user_roles ? $item['value'] : null,
                        'user_id' => $is_user_roles ? null : $item['value'],
                        'created_by_id' => Auth::id(),
                    ];
                }, $request->kepada);

                $this->suratService->saveTujuanSurat($surat, $tujuan_surat);

                $this->suratService->saveAttachment($surat, $request->attachment);

                $surat->content_surat_id = $content_surat->id;
                $surat->save();

                if ($id) {
                } else {
                    $message = 'Surat No ' . $surat->no_surat . ' Dibuat oleh ' . (Auth::user()->role->name ?? '') . ' - ' . (Auth::user()->nama ?? '') . ' pada ' . date('d-m-Y H:i:s');
                    $this->suratService->createLogHistory($surat->id, [
                        'status' => $message,
                        'type' => 1,
                        'unit_bisnis_id' => $surat->unit_bisnis_id,
                    ]);

                    $tujuan_role_id = $surat->tujuanSurat->pluck('role_id')->toArray() ?? [];
                    $tujuan_user_id = $surat->tujuanSurat->pluck('user_id')->toArray() ?? [];
                    $tujuan_unit_bisnis_id = [$surat->unit_bisnis_id];

                    $this->suratService->sendNotificationSurat($surat, $message, [
                        'role_id' => $tujuan_role_id,
                        'user_id' => $tujuan_user_id,
                        'unit_bisnis_id' => $tujuan_unit_bisnis_id
                    ]);
                }

                return $surat;
            });

            $message = 'Surat berhasil dibuat, dengan nomor surat: ' . $surat->no_surat;

            return response()->json(['status' => true, 'message' => $message], 200);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }
    }

    public function reply(Request $request)
    {

        $validasi = [
            'content_surat' => 'required',
            'kepada' => 'required|array',
            'type_message' => 'required',
        ];
        $request->validate($validasi);

        try {
            DB::transaction(function () use ($request, &$surat, &$surat_parent) {
                $perihal = "Balasan: " . $request->perihal;

                $surat_parent = Surat::findOrFail($request->surat_id);
                $approve_status_name = $request->approve_status;
                $approve_status = Surat::APPROVE_STATUS[$approve_status_name] ?? null;

                $surat = Surat::create([
                    'jenis_surat' => $request->jenis_surat['jenis_surat'] ?? '',
                    'perihal' => $perihal,
                    'no_surat' => $surat_parent->no_surat,
                    'kop_surat_id' => $request->kop_surat['value'] ?? '',
                    'status' => $surat_parent->status === Surat::STATUS_DISPOSISI ? Surat::STATUS_DIPROSES : $surat_parent->status,
                    'dikirim_oleh_id' => Auth::id(),
                    'created_by_id' => Auth::id(),
                    'parent_surat_id' => $request->surat_id ?? null,
                    'unit_bisnis_id' => $surat_parent->unit_bisnis_id,
                    'dikirim_oleh_role_id' => Auth::user()->role_id,
                    'parent_content_surat_id' => $surat_parent->content_surat_id,
                    'type' => 2, // Type Reply Surat
                    'reply_to' => $request->reply_to ?? "",
                    'approve_status' => $approve_status,
                ]);

                UnitBisnisSurat::create([
                    'surat_id' => $surat->id,
                    'unit_bisnis_id' => $surat_parent->unit_bisnis_id ?? '',
                    'surat_parent_id' => $surat_parent->id,
                ]);

                $content_surat = replaceContent($request->content_surat);
                $original_content = $request->original_content;
                $full_content = $content_surat;

                $content_surat = ContentSurat::create([
                    'surat_id' => $surat->id,
                    'content' => $content_surat,
                    'original_content' => $original_content,
                    'full_content' => $full_content,
                    'created_by_id' => Auth::id(),
                ]);

                $tujuan_surat = array_map(function ($item) {
                    $is_user_roles = $item['user_roles'] ?? false;
                    return [
                        'role_id' => $is_user_roles ? $item['value'] : null,
                        'user_id' => $is_user_roles ? null : $item['value'],
                        'created_by_id' => Auth::id(),
                    ];
                }, $request->kepada);

                $this->suratService->saveTujuanSurat($surat, $tujuan_surat);

                $this->suratService->saveAttachment($surat, $request->attachment);

                $surat->content_surat_id = $content_surat->id;
                $surat->save();


                $tujuan_role_id = array_column($tujuan_surat ?? [], 'role_id') ?? [];
                $tujuan_user_id = array_column($tujuan_surat ?? [], 'user_id') ?? [];
                $tujuan_user_id = array_merge($tujuan_user_id, [$surat_parent->dikirim_oleh_id]);

                if ($approve_status) {
                    $approve_status_name = ucwords(str_replace('_', ' ', $approve_status_name));
                    $surat_parent->status_process = $approve_status_name . ' oleh ' . (Auth::user()->role->name ?? '') . ' - ' . (Auth::user()->nama ?? '');
                    $surat_parent->approve_status = $approve_status;
                    $surat_parent->save();

                    $approve_status_process = "Surat No " . $surat_parent->no_surat . " " . $surat_parent->status_process . " pada " . date('d-m-Y H:i:s');
                    $this->suratService->createLogApproval($surat_parent->id, [
                        'approval_status' => $approve_status_process,
                        'status' => $approve_status,
                        'unit_bisnis_id' => $surat_parent->unit_bisnis_id,
                    ]);

                    $this->suratService->createLogHistory($surat_parent->id, [
                        'status' => $approve_status_process,
                        'type' => 3,
                        'approval_status' => $approve_status,
                        'unit_bisnis_id' => $surat_parent->unit_bisnis_id,
                    ]);

                    $this->suratService->sendNotificationSurat($surat_parent, $approve_status_process, [
                        'role_id' => $tujuan_role_id,
                        'user_id' => $tujuan_user_id,
                        'unit_bisnis_id' => [$surat_parent->unit_bisnis_id],
                    ]);
                } else {
                    $message = "Surat No " . $surat_parent->no_surat . " Dibalas oleh " . (Auth::user()->role->name ?? '') . ' - ' . (Auth::user()->nama ?? '') . ' pada ' . date('d-m-Y H:i:s');
                    $this->suratService->createLogHistory($surat_parent->id, [
                        'status' => $message,
                        'type' => 2,
                        'unit_bisnis_id' => $surat_parent->unit_bisnis_id,
                    ]);

                    $this->suratService->sendNotificationSurat($surat_parent, $message, [
                        'role_id' => $tujuan_role_id,
                        'user_id' => $tujuan_user_id,
                        'unit_bisnis_id' => [$surat_parent->unit_bisnis_id],
                    ]);
                }

                return $surat;
            });
            $data = $this->suratService->detailData($surat->parent_surat_id);
            $message = 'Surat No Surat: ' . $surat_parent->no_surat . ' berhasil dibalas';

            return response()->json(['status' => true, 'message' => $message, 'data' => $data], 200);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }
    }

    public function disposisi(Request $request)
    {
        $validasi = [
            'forwardList' => 'required|array',
            'forwardList.*.kepada' => 'required|array',
            'forwardList.*.catatan' => 'required',
        ];
        $request->validate($validasi);

        try {
            DB::transaction(function () use ($request, &$surat, &$surat_parent, &$collectSignatures) {
                $perihal = 'Disposisi: ' . $request->perihal;

                $surat_parent = Surat::findOrFail($request->surat_id);
                $surat_parent->status = Surat::STATUS_DISPOSISI;
                $surat_parent->save();

                $surat_parent->status_process = 'Disposisi oleh ' . (Auth::user()->role->name ?? '') . ' - ' . (Auth::user()->nama ?? '');

                foreach ($request->forwardList ?? [] as $item) {
                    $surat = Surat::create([
                        'jenis_surat' => $request->jenis_surat['jenis_surat'] ?? '',
                        'perihal' => $perihal,
                        'kop_surat_id' => $request->kop_surat['value'] ?? '',
                        'status' => Surat::STATUS_DISPOSISI,
                        'dikirim_oleh_id' => Auth::id(),
                        'created_by_id' => Auth::id(),
                        'no_surat' => $surat_parent->no_surat,
                        'parent_surat_id' => $request->surat_id ?? null,
                        'unit_bisnis_id' => $item['unit_bisnis_id']['value'] ?? '',
                        'parent_content_surat_id' => $surat_parent->content_surat_id,
                        'dikirim_oleh_role_id' => Auth::user()->role_id,
                        'type' => 3, // Type Reply Surat
                    ]);

                    UnitBisnisSurat::create([
                        'surat_id' => $surat->id,
                        'unit_bisnis_id' => $item['unit_bisnis_id']['value'] ?? '',
                        'surat_parent_id' => $surat_parent->id,
                    ]);

                    $content_surat = replaceContent($item['catatan'] ?? '');
                    $full_content = $content_surat;
                    $original_content = $request->original_content;

                    $content_surat = ContentSurat::create([
                        'surat_id' => $surat->id,
                        'content' => $content_surat,
                        'original_content' => $original_content,
                        'full_content' => $full_content,
                        'created_by_id' => Auth::id(),
                    ]);

                    $tujuan_surat = array_map(function ($item) {
                        $is_user_roles = $item['user_roles'] ?? false;
                        return [
                            'role_id' => $is_user_roles ? $item['value'] : null,
                            'user_id' => $is_user_roles ? null : $item['value'],
                            'created_by_id' => Auth::id(),
                        ];
                    }, $item['kepada'] ?? []);

                    $this->suratService->saveTujuanSurat($surat, $tujuan_surat);

                    $surat->content_surat_id = $content_surat->id;
                    $surat->save();

                    $message = "Surat No " . $surat_parent->no_surat . " " . $surat_parent->status_process . " kepada " . $surat->tujuanSurat->pluck('role.name')->implode(', ') . " pada " . date('d-m-Y H:i:s');
                    $this->suratService->createLogHistory($surat_parent->id, [
                        'status' => $message,
                        'type' => 4,
                        'unit_bisnis_id' => $surat_parent->unit_bisnis_id,
                    ]);

                    $tujuan_role_id = array_column($tujuan_surat ?? [], 'role_id') ?? [];
                    $tujuan_user_id = array_column($tujuan_surat ?? [], 'user_id') ?? [];
                    $tujuan_user_id = array_merge($tujuan_user_id, [$surat_parent->dikirim_oleh_id]);
                    $this->suratService->sendNotificationSurat($surat_parent, $message, [
                        'role_id' => $tujuan_role_id,
                        'user_id' => $tujuan_user_id,
                        'unit_bisnis_id' => [$surat->unit_bisnis_id],
                    ]);
                }

                $surat_parent->approve_status = null;
                $surat_parent->save();

                $collectSignatures = [];
                if (isset($request->signature)) {
                    $signatures = $request->signature;
                    $documentComplete = $signatures['documentComplete'] ?? [];
                    foreach($documentComplete as $item) {
                        $annotationDocument = $signatures['annotations'][$item] ?? [];
                        $signatureDocument = $signatures['signatures'][$item] ?? [];
                        foreach($annotationDocument as $key => $value) {
                            $signature = $signatureDocument[$value['id']] ?? [];

                            $tanda_tangan = TandaTanganSurat::create([
                                'surat_id' => $surat_parent->id,
                                'content_surat_id' => $surat_parent->content_surat_id,
                                'unit_bisnis_id' => $surat_parent->unit_bisnis_id ?? '',
                                'user_id' => Auth::id(),
                                'role_id' => Auth::user()->role_id,
                                'posisi_x' => $value['position_x'] ?? 0,
                                'posisi_y' => $value['position_y'] ?? 0,
                                'page' => $value['page'] ?? 1,
                                'height' => $signature['height'] ?? 0,
                                'width' => $signature['width'] ?? 0,
                                'canvas_width' => $value['canvas_width'] ?? 0,
                                'canvas_height' => $value['canvas_height'] ?? 0,
                                'value_type_esign' => $signature['value_type'] ?? '',
                                'type_esign' => $signature['type'] ?? '',
                                'referable_id' => $item,
                                'referable_type' => ($value['document']['type'] ?? '') === 'surat' ? Surat::class : AttachmentFileSurat::class,
                                'document_type' => ($value['document']['type'] ?? '')
                            ]);
        
                            $barcodeGenerator = new DNS2D();
                            $url_check = route('checkSignature', $tanda_tangan->id);
                            $barcode = $barcodeGenerator->getBarcodePNG($url_check, 'QRCODE', 35, 35);
                            // $directoryPath = 'uploads/barcodes/';
                            // if (!File::isDirectory($directoryPath)) {
                            //     File::makeDirectory($directoryPath, 0755, true);
                            // }
                            // $path = $directoryPath . $tanda_tangan->id . '.png';
                            // file_put_contents($path, base64_decode($barcode));
                            // $tanda_tangan->barcode = $path;
                            // $tanda_tangan->save();
                            $tanda_tangan->barcode = 'data:image/png;base64,' . $barcode;
                            $tanda_tangan->image = $signature['signature'];
                            $tanda_tangan->save();

                            $collectSignatures[] = $tanda_tangan;
                        }
                       
                    }
                }

                return $surat;
            });

            $message = 'Surat No Surat: ' . $surat_parent->no_surat . ' berhasil disposisi';
            $data = $this->suratService->detailData($surat->parent_surat_id);

            return response()->json(['status' => true, 'message' => $message, 'data' => $data, 'signatures' => $collectSignatures], 200);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }
    }

    public function esignManual(Request $request)
    {
        $request->validate([
            'signatures' => 'required|array',
            'signatures.*.id' => 'required',
            'surat_id' => 'required',
        ]);

        Surat::findOrFail($request->surat_id);
        foreach ($request->signatures ?? [] as $signature) {
            $tanda_tangan = TandaTanganSurat::findOrFail($signature['id']);
            $tanda_tangan->image = $signature['base_64_image'];
            $tanda_tangan->save();
        }

        return response()->json(['status' => true, 'message' => 'Tanda tangan berhasil disimpan'], 200);
    }

    public function dataTable(Request $request)
    {
        $unit_bisnis_ids = Auth::user()->user_unit_bisnis_data->pluck('id')->toArray();

        $data = Surat::select(
            'id',
            'jenis_surat',
            'perihal',
            'kop_surat_id',
            'dikirim_oleh_id',
            'unit_bisnis_id',
            'content_surat_id',
            'status',
            'created_by_id',
            'created_at',
            'parent_surat_id',
            'no_surat',
            'type',
            'is_acc_complete'
        )->with(['pengirim:id,nama,unit_bisnis_id', 'unitBisnis:id,nama', 'parentSurat'])
            ->whereHas('pengirim', function ($query) {
                $query->where('role_id', Auth::user()->role_id);
            })
            ->whereIn('unit_bisnis_id', $unit_bisnis_ids)
            ->latest()
            ->filter($request);

        return DataTables::of($data)
            ->addindexColumn()
            ->addColumn('action', function ($data) {

                $action_button = '';

                if (canPermission('Surat Keluar.Revisi')) {
                    if (intval($data->type) === 1)
                        $action_button .= " <a class='avatar-text avatar-md' href='" . route('surat-keluar.revision', $data->parent_surat_id ? $data->parent_surat_id : $data->id) . "'>
                                                <i class='fas fa-pencil'></i>
                                            </a>";
                }

                $action_button .= " <a class='avatar-text avatar-md' href='" . route('surat-keluar.show', $data->parent_surat_id ? $data->parent_surat_id : $data->id) . "'>
                                <i class='feather feather-eye'></i>
                            </a>";


                $action_button .= " <a class='avatar-text avatar-md' href='" . route('surat-keluar.history', $data->parent_surat_id ? $data->parent_surat_id : $data->id) . "'>
                                        <i class='fas fa-history'></i>
                                    </a>";

                if (canPermission('Surat Keluar.Delete')) {
                    $action_button .= "<a class='avatar-text avatar-md deleteData bg-danger text-white' href='javascript:void(0)'  data-id='$data->id' data-input='" . json_encode(['id' => $data->id, 'perihal' => $data->perihal]) . "'>
                                <i class='feather feather-trash-2'></i>
                            </a>";
                }

                $action = "<div class='d-flex align-items-center gap-2'>
                                $action_button
                            </div>";

                return $action;
            })->addColumn('status_surat', function ($data) {
                if ($data->check_surat_expired) {
                    $data->status = Surat::STATUS_EXPIRED;
                }
                $return = status_surat($data->status);
                if ($data->status == Surat::STATUS_SELESAI) {
                    $return .= "<br>";
                    if (intval($data->is_acc_complete) == 1) {
                        $return .=  "<div class='d-inline-block border border-success text-success fw-bold mt-3' style='border-radius: 20px; font-size: 10px;padding: 4px 7px'>
                                            Disetujui
                                        </div>";
                    } else {
                        $return .=  "<div class='d-inline-block border border-danger text-danger fw-bold mt-3' style='border-radius: 20px; font-size: 10px;padding: 4px 7px'>
                                        Tidak Disetujui
                                    </div>";
                    }
                }
                return $return;
            })
            ->addColumn('date', function ($data) {
                return Carbon::parse($data->created_at)->translatedFormat('d F Y H:i');
            })
            ->rawColumns(['action', 'kop_surat', 'status_surat', 'date'])
            ->smart(true)
            ->make(true);
    }

    public function destroy(string $id)
    {
        try {
            $surat = Surat::findOrFail($id);
            DB::transaction(function () use ($surat) {
                $surat->delete();
            });

            return response()->json(['status' => true, 'message' => 'Data berhasil dihapus'], 200);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th->getMessage()], 500);
        }
    }

    public function previewStore(Request $request)
    {
        try {
            $validasi = [
                'content_surat' => 'required',
                'kop_surat' => 'required',
            ];

            $surat_id = $request->surat_id;

            if($surat_id) {
                unset($validasi['kop_surat']);
            }

            $request->validate($validasi);

            $kop_surat = '';
            if($surat_id) {
                $surat = Surat::select('id', 'kop_surat_id')->findOrFail($surat_id);
                $kop_surat = $surat->kop_surat_id ?? '';
            }

            Session()->put('preview', [
                'ukuran_kertas' => $request->ukuran_kertas ?? 'A4',
                'surat_id' => $surat_id,
                'content_surat' => $request->content_surat,
                'kop_surat' => $request->kop_surat ?? $kop_surat,
                'perihal' => $request->perihal,
            ]);

            $url = route('surat-keluar.preview');

            return response()->json(['status' => true, 'message' => 'Preview berhasil dibuat', 'url' => $url], 200);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => 'Gagal membuat preview: ' . $e->getMessage()], 500);
        }
    }

    public function preview()
    {
        $data = Session()->get('preview');
        $kop_surat = KopSurat::findOrFail($data['kop_surat']);

        $data['content_surat'] = parseContent($data['content_surat'], ($data['surat_id'] ?? null), [
            'is_pdf' => true,
        ], [
            'kop_surat' => $kop_surat->kop_surat_replaced,
            'perihal' => $data['perihal'] ?? '',
            'tanggal_surat' => $data['tanggal_surat'] ?? '',
            'no_surat' => $data['no_surat'] ?? '',
        ]);

        $preview = true;
        $html = View::make('backoffice.surat-masuk.preview', compact('data', 'preview', 'kop_surat'))->render();
        $cetak = 'Surat_' . preg_replace('/[^A-Za-z0-9_\-]/', '', $data['perihal'] ?? 'Unknown') . '_' . time() . '.pdf';

        $setting = getSettingGroup('default');

        $css = "
            body {
                font-family: Arial, Helvetica, sans-serif;
                font-size: 12pt;
                margin: 0;
                padding: 0;
                width: 100%;
                line-height: 1.5;
            }
            p {
                margin: 0 0 12pt 0;
                font-size: 12pt;
                line-height: 1.5;
                font-family: Arial, Helvetica, sans-serif;
            }

            table {
                width: 100%;
                border-collapse: collapse;
                font-size: 12pt;
                margin: 12pt 0;
                font-family: Arial, Helvetica, sans-serif;
            }
            th, td {
                border: 1px solid #000;
                padding: 8pt;
                text-align: left;
                font-size: 12pt;
                vertical-align: top;
                font-family: Arial, Helvetica, sans-serif;
                line-height: 1.5;
            }
            th {
                background-color: #f5f5f5;
                font-weight: bold;
            }
            table.no-border, table.no-border td, table.no-border th {
                border: none;
            }
            h1, h2, h3, h4, h5, h6 {
                font-size: 12pt;
                font-weight: bold;
                margin: 12pt 0 6pt 0;
                font-family: Arial, Helvetica, sans-serif;
                line-height: 1.5;
            }
            ul, ol {
                margin: 12pt 0;
                padding-left: 24pt;
                font-size: 12pt;
                font-family: Arial, Helvetica, sans-serif;
            }
            li {
                font-size: 12pt;
                margin: 6pt 0;
                font-family: Arial, Helvetica, sans-serif;
                line-height: 1.5;
            }
            div, span {
                font-size: 12pt;
                font-family: Arial, Helvetica, sans-serif;
            }
            strong, b {
                font-size: 12pt;
                font-family: Arial, Helvetica, sans-serif;
            }
            em, i {
                font-size: 12pt;
                font-family: Arial, Helvetica, sans-serif;
            }
        ";

        // Proper paper size handling for A4 and F4
        $format_kertas = (($data['ukuran_kertas'] ?? '') === 'F4') ? [216, 330] : [210, 297];

        $cmToMm = function($cm) { return $cm * 10; };
        
        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',
            'format' => $format_kertas,
            'default_font' => 'Arial',
            'margin_left' => $cmToMm($setting['surat_margin_left'] ?? 10),
            'margin_right' => $cmToMm($setting['surat_margin_right'] ?? 10),
            'margin_top' => $cmToMm($setting['surat_margin_top'] ?? 10),
            'margin_bottom' => $cmToMm($setting['surat_margin_bottom'] ?? 10),
            // 'margin_header' => $setting['surat_margin_header'] ?? 5,
            // 'margin_footer' => $setting['surat_margin_footer'] ?? 5,
            'tempDir' => storage_path('app/mpdf'), 
        ]);
        
        $mpdf->WriteHTML($css, \Mpdf\HTMLParserMode::HEADER_CSS);
        $mpdf->WriteHTML($html, \Mpdf\HTMLParserMode::HTML_BODY);

        return response($mpdf->Output($cetak, 'I'))->header('Content-Type', 'application/pdf');

        // $cetak = 'Surat ' . time() . '.pdf';
        // $preview = true;
        // $pdf = PDF::loadview('backoffice.surat-masuk.preview', compact('data', 'preview', 'kop_surat'))
        //     ->setPaper('A4', 'portrait');
        // return $pdf->stream($cetak);
    }

    public function history($id)
    {
        $data = HistorySurat::where('surat_id', $id)->with('user', 'role')->latest()->get();
        $surat = Surat::findOrFail($id);
        return view('backoffice.surat-keluar.history', compact('id', 'data', 'surat'));
    }

    public function revision($id)
    {
        $data = Surat::with(['contentSurat', 'attachmentFileSurat'])->findOrFail($id);
        return view('backoffice.surat-keluar.create-update', compact('id', 'data'));
    }

    public function storeRevision(Request $request, $id)
    {
        $validasi = [
            'content_surat' => 'required',
        ];
        $request->validate($validasi);

        if (!canPermission('Surat Keluar.Revisi') && !canPermission('Surat Masuk.Revisi')) { 
            return response()->json(['status' => false, 'message' => 'Anda tidak memiliki akses untuk merevisi surat'], 403);
        }
        try {
            $surat_parent = Surat::with('tujuanSurat','childSurat.tujuanSurat')->findOrFail($id);

            $tujuan_surat_role_id = [];
            $tujuan_surat_user_id = [];
            $tujuan_unit_bisnis_id = [];

            foreach ($surat_parent->tujuanSurat as $tujuan) {
                $tujuan_unit_bisnis_id[] = $tujuan->unit_bisnis_id;
                if ($tujuan->role_id)
                    $tujuan_surat_role_id[] = $tujuan->role_id;
                else
                    $tujuan_surat_user_id[] = $tujuan->user_id;
            }

            foreach ($surat_parent->childSurat as $child) {
                $tujuan_unit_bisnis_id[] = $child->unit_bisnis_id;
                foreach ($child->tujuanSurat as $tujuan) {
                    if ($tujuan->role_id)
                        $tujuan_surat_role_id[] = $tujuan->role_id;
                    else
                        $tujuan_surat_user_id[] = $tujuan->user_id;
                }
            }

            $tujuan_surat_role_id = array_unique($tujuan_surat_role_id);
            $tujuan_surat_user_id = array_unique($tujuan_surat_user_id);
            $tujuan_unit_bisnis_id = array_values(array_unique(array_filter($tujuan_unit_bisnis_id)));

            DB::transaction(function () use ($surat_parent, $tujuan_surat_role_id, $tujuan_surat_user_id, $request, &$surat, $tujuan_unit_bisnis_id) {
                $perihal = "Revisi : " . $surat_parent->perihal;

                $surat = Surat::create([
                    'jenis_surat' => $surat_parent->jenis_surat,
                    'perihal' => $perihal,
                    'kop_surat_id' => $surat_parent->kop_surat_id,
                    'status' => Surat::STATUS_REVISI,
                    'status_process' => 'Direvisi oleh ' . (Auth::user()->role->name ?? '') . ' - ' . (Auth::user()->nama ?? ''),
                    'dikirim_oleh_id' => Auth::id(),
                    'created_by_id' => Auth::id(),
                    'parent_surat_id' => $surat_parent->id,
                    'unit_bisnis_id' => $surat_parent->unit_bisnis_id,
                    'dikirim_oleh_role_id' => Auth::user()->role_id,
                    'parent_content_surat_id' => $surat_parent->content_surat_id,
                    'reply_to' => $request->reply_to,
                    'type' => Surat::TYPE_SURAT['revision'],
                ]);

                foreach($tujuan_unit_bisnis_id as $unit_bisnis_id) {
                    UnitBisnisSurat::create([
                        'surat_id' => $surat->id,
                        'unit_bisnis_id' => $unit_bisnis_id,
                        'surat_parent_id' => $surat_parent->id
                    ]);
                }

                $content_surat_revisi = "<p>
                                            <div>-- Revisi Surat --</div>
                                            <div>Revisi Oleh " . (Auth::user()->role->name ?? '') . ' - ' . (Auth::user()->nama ?? '') . " pada " . date('d-m-Y H:i:s') . "</div>
                                        </p>";

                $content_surat = replaceContent($request->content_surat);
                // $content_surat_revisi .= "<p>$content_surat</p>";

                $original_content = $content_surat;

                $content_surat = ContentSurat::create([
                    'surat_id' => $surat->id,
                    'content' => $content_surat,
                    'original_content' => $original_content,
                    'full_content' => $content_surat_revisi,
                    'created_by_id' => Auth::id(),
                ]);

                $tujuan_surat_role = array_map(function ($item) {
                    return [
                        'role_id' => $item,
                        'created_by_id' => Auth::id(),
                    ];
                }, $tujuan_surat_role_id);

                $this->suratService->saveTujuanSurat($surat, $tujuan_surat_role);

                $tujuan_surat_user = array_map(function ($item) {
                    return [
                        'user_id' => $item,
                        'created_by_id' => Auth::id(),
                    ];
                }, $tujuan_surat_user_id);
                $this->suratService->saveTujuanSurat($surat, $tujuan_surat_user);

                $surat->content_surat_id = $content_surat->id;
                $surat->save();

                $surat_parent->content_surat_id = $content_surat->id;
                $geneate_no_surat_revision = generateNoSuratRevision([
                    'jenis_surat_id' => $surat_parent->jenis_surat_id,
                    'no_urut' => $surat_parent->no_urut,
                    'no_surat' => $surat_parent->no_surat,
                    'month_actual' => $surat_parent->month_no_surat,
                    'year_actual' => $surat_parent->year_no_surat,
                    'tanggal_actual' => $surat_parent->tanggal_no_surat,
                    'no_urut_revision' => $surat_parent->no_urut_revision,
                    'unit_bisnis_id' => $surat_parent->unit_bisnis_id,
                ]);
                $surat_parent->no_surat = $geneate_no_surat_revision['no_surat'];
                $surat_parent->no_urut_revision = $geneate_no_surat_revision['no_urut_revision'] ?? null;
                $surat_parent->month_no_surat = $geneate_no_surat_revision['month_actual'] ?? '';
                $surat_parent->year_no_surat = $geneate_no_surat_revision['year_actual'] ?? '';
                $surat_parent->tanggal_no_surat = $geneate_no_surat_revision['tanggal_actual'] ?? '';
                $surat_parent->latest_revision_at = now();
                $surat_parent->save();

                $surat->no_surat = $surat_parent->no_surat;
                $surat->save();

                $this->suratService->saveAttachment($surat_parent, $request->attachment);

                $surat_parent->update([
                    'status_process' => 'Direvisi oleh ' . (Auth::user()->role->name ?? '') . ' - ' . (Auth::user()->nama ?? ''),
                    'status' => Surat::STATUS_REVISI,
                    'updated_by_id' => Auth::user()->id
                ]);

                $status_process = 'Surat No ' . $surat_parent->no_surat . ' Direvisi oleh ' . (Auth::user()->role->name ?? '') . ' - ' . (Auth::user()->nama ?? '') . ' pada ' . date('d-m-Y H:i:s');
                $this->suratService->createLogHistory($surat_parent->id, [
                    'status' => $status_process,
                    'type' => 6,
                    'unit_bisnis_id' => $surat_parent->unit_bisnis_id,
                ]);

                $tujuan_role_id = array_column($tujuan_surat_role ?? [], 'role_id') ?? [];
                $tujuan_user_id = array_column($tujuan_surat_user ?? [], 'user_id') ?? [];
                $tujuan_user_id = array_merge($tujuan_user_id, [$surat_parent->dikirim_oleh_id]);
                $this->suratService->sendNotificationSurat($surat_parent, $status_process, [
                    'role_id' => $tujuan_role_id,
                    'user_id' => $tujuan_user_id,
                    'unit_bisnis_id' => $tujuan_unit_bisnis_id,
                ]);
            });

            $message = 'Surat No Surat: ' . $surat_parent->no_surat . ' berhasil direvisi';
            $data = $this->suratService->detailData($surat->parent_surat_id);

            return response()->json(['status' => true, 'message' => $message, 'data' => $data], 200);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function listRevision(Request $request, $id)
    {
        $data = Surat::select('id', 'no_surat', 'created_at', 'created_by_id', 'dikirim_oleh_id')->with(['childSurat' => function($query) {
            $query->select('id', 'parent_surat_id', 'no_surat', 'created_at', 'created_by_id', 'dikirim_oleh_id', 'type', 'perihal')
                    ->where('type', Surat::TYPE_SURAT['revision'])
                    ->orderBy('created_at', 'desc');
        }, 'pengirim:id,nama', 'childSurat.pengirim:id,nama'])->findOrFail($id);
        return response()->json(['status' => true, 'data' => $data], 200);

    }
}
