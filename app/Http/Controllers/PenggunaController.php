<?php

namespace App\Http\Controllers;

use App\Models\Role;
use App\Models\User;
use Illuminate\Http\Request;
use App\Models\UserUnitBisnis;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Yajra\DataTables\Facades\DataTables;

class PenggunaController extends Controller
{

    public function list(Request $request)
    {
        $user = User::select('id', 'nama', 'profile_photo as image', 'email')
                            ->when($request->keyword, function($q) use ($request) {
                                $q->where('nama', 'like', '%'.$request->keyword.'%')
                                    ->orWhere('email', 'like', '%'.$request->keyword.'%');
                            })
                            ->orderBy('nama')
                            ->get();
        return response()->json($user);
    }

    public function listGroup(Request $request)
    {
        // $unit_bisnis_id = session()->get('unit_bisnis_id');
        $unit_bisnis_id = $request->unit_bisnis_id;
        $user = Role::select('id', 'name')
                            ->where('name', 'like', '%'.$request->keyword.'%')
                            ->with(['userRoles' => function($query) use ($unit_bisnis_id) {
                                $query->select('id', 'nama', 'email', 'unit_bisnis_id')
                                        ->when($unit_bisnis_id, function($q) use ($unit_bisnis_id) {
                                            $q->where('unit_bisnis_id', $unit_bisnis_id)
                                                ->orWhereHas('userUnitBisnis', function($query) use ($unit_bisnis_id) {
                                                    $query->where('unit_bisnis_id', $unit_bisnis_id);
                                                });
                                        });
                            }])
                            ->when($unit_bisnis_id, function($q) use ($unit_bisnis_id) {
                                $q->whereHas('userRoles', function($query) use ($unit_bisnis_id) {
                                    $query->where('unit_bisnis_id', $unit_bisnis_id)
                                        ->orWhereHas('userUnitBisnis', function($query) use ($unit_bisnis_id) {
                                            $query->where('unit_bisnis_id', $unit_bisnis_id);
                                        });
                                });
                            })
                            ->filterFromSurat($request)
                            ->get();
        return response()->json($user);
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('backoffice.pengguna.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('backoffice.pengguna.create-update');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $id = $request->id;

        $validator = [
            'nama' => 'required',
            'email' => [
                'required',
                'email',
                Rule::unique('users', 'email'),
                Rule::unique('contacts', 'email')->whereNull('deleted_at'),
            ],
            'unit_bisnis_ids' => 'required',
            'role_id' => 'required',
            'password' => 'required|min:8',
            'confirm_password' => 'required|same:password',
        ];

        if($id) {
            $validator['password'] = 'nullable|min:8';
            $validator['confirm_password'] = 'nullable|same:password';
            $validator['email'] = [
                'required',
                'email',
                Rule::unique('users', 'email')->ignore($id),
                Rule::unique('contacts', 'email')->ignore($id)->whereNull('deleted_at'),
            ];
        }

        $request->validate($validator);

        DB::transaction(function () use ($request, $id) {
            $password = Hash::make($request->password);

            $input = [
                'nama' => $request->nama,
                'email' => $request->email,
                'unit_bisnis_ids' => $request->unit_bisnis_ids,
                'role_id' => $request->role_id
            ];

            if(!$id) {
                $input['password'] = $password;
            }

            $user = User::updateOrCreate([
                'id' => $id,
            ], $input);


            $unit_bisnis_ids = $request->unit_bisnis_ids;
            foreach ($unit_bisnis_ids as $unit_bisnis_id) {
                UserUnitBisnis::updateOrCreate([
                    'user_id' => $user->id,
                    'unit_bisnis_id' => $unit_bisnis_id
                ]);
            }

            UserUnitBisnis::where('user_id', $user->id)
                            ->whereNotIn('unit_bisnis_id', $unit_bisnis_ids)
                            ->delete();

            $role = $request->role_id;
            if($id) {
                $user->syncRoles($role);
            } else {
                $user->assignRole($role);
            }

            return $user;
        });


        return redirect(route('pengguna.index'))->with('success', 'Pengguna berhasil disimpan');

    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data = User::with(['userUnitBisnis:id,user_id,unit_bisnis_id'])->findOrFail($id);
        return view('backoffice.pengguna.create-update', compact('data'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $user = User::findOrFail($id);
            DB::transaction(function() use ($user){
                $user->removeRole($user->role_id);
                $user->delete();
            });

            return response()->json(['status' => true,'message' => 'Data berhasil dihapus'], 200);

        } catch (\Throwable $th) {
           return response()->json(['status' => false,'message' => $th->getMessage()], 500);
        }
    }

    public function dataTable(Request $request)
    {
        $data = User::select(
            'id',
            'nama',
            'email',
            'unit_bisnis_id',
            'role_id',
        )
        ->with('userUnitBisnis', 'userUnitBisnis.unitBisnis:id,nama','role:id,name')
            ->latest()
            ->filter($request);

        return DataTables::of($data)
            ->addindexColumn()
            ->addColumn('unit_bisnis', function ($data) {
                $unit_bisnis = '';
                $unit_bisnis = '<ul style="list-style: disc !important">';
                foreach ($data->userUnitBisnis as $value) {
                    $unit_bisnis .= "<li>{$value->unitBisnis->nama}</li>";
                }
                $unit_bisnis .= '</ul>';
                return $unit_bisnis;
            })
            ->addColumn('action', function ($data) {
                $action_button = '';
                if(canPermission('Pengguna.Edit')) {
                    $action_button .= "<li>
                                            <a class='dropdown-item' href='".route('pengguna.edit', $data->id)."'>
                                                <i class='feather feather-edit-3 me-3'></i>
                                                <span>Edit</span>
                                            </a>
                                        </li>";
                }

                if(canPermission('Pengguna.Delete')) {
                    $action_button .= " <li class='dropdown-divider'></li>
                                        <li>
                                            <a class='dropdown-item deleteData' href='javascript:void(0)'  data-id='$data->id' data-input='".json_encode($data)."'>
                                                <i class='feather feather-trash-2 me-3'></i>
                                                <span>Delete</span>
                                            </a>
                                        </li>";
                }

                $action = " <div class='hstack gap-2'>
                                                <div class='dropdown dropdown-overflow'>
                                                    <a href='javascript:void(0)'
                                                        class='avatar-text avatar-md btn-dropdown'
                                                        data-bs-toggle='dropdown' data-bs-offset='0,21'>
                                                        <i class='feather feather-more-horizontal'></i>
                                                    </a>
                                                    <ul class='dropdown-menu'>
                                                        $action_button
                                                    </ul>
                                                </div>
                                            </div>";
                return $action;
            })
            ->rawColumns(['action', 'unit_bisnis'])
            ->smart(true)
            ->make(true);
    }
}
