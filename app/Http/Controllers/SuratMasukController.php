<?php

namespace App\Http\Controllers;

use PDF;
use Mpdf\Mpdf;
use Carbon\Carbon;
use App\Models\Surat;
use App\Models\KopSurat;
use App\Models\ReadSurat;
use App\Models\ContentSurat;
use App\Models\Notification;
use App\Models\RequestEsign;
use Illuminate\Http\Request;
use App\Models\UnitBisnisSurat;
use App\Models\ReadNotification;
use App\Models\TandaTanganSurat;
use Illuminate\Support\Facades\DB;
use App\Http\Services\eSignService;
use App\Http\Services\SuratService;
use App\Models\RequestEsignSigners;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\View;
use Yajra\DataTables\Facades\DataTables;

class SuratMasukController extends Controller
{

    protected $suratService;
    protected $eSignService;

    public function __construct(
        SuratService $suratService,
        eSignService $eSignService
    ) {
        $this->suratService = $suratService;
        $this->eSignService = $eSignService;
    }

    public function index()
    {
        return view('backoffice.surat-masuk.index');
    }

    public function create()
    {
        return view('backoffice.surat-masuk.create-update');
    }

    public function show($id)
    {
        $data = $this->suratService->detailData($id);
        if (!$data) {
            return redirect()->route('surat-masuk.index')->with('error', 'Data surat tidak ditemukan');
        }

        $collect_all_id_surat = $data->childSurat->pluck('type', 'id')->toArray() ?? [];
        $collect_all_id_surat[$data->id] = $data->type;

        $read_surat = ReadSurat::whereIn('surat_id', array_keys($collect_all_id_surat))
            ->pluck('surat_id')->toArray();
        $surat_not_read = array_diff_key($collect_all_id_surat, array_flip($read_surat));

        if (count($surat_not_read)) {
            foreach ($surat_not_read as $id => $type) {
                ReadSurat::create([
                    'surat_id' => $id,
                    'user_id' => Auth::id(),
                    'type' => $type,
                    'role_id' => Auth::user()->role_id,
                    'unit_bisnis_id' => Auth::user()->unit_bisnis_id,
                ]);
            }

            Notification::where('surat_id', $data->id)
                ->where('user_id', Auth::id())
                ->whereNull('read_at')
                ->update([
                    'read_at' => now(),
                    'is_read' => true,
                ]);
        }

        return view('backoffice.surat-masuk.detail', compact('id', 'data'));
    }

    public function detail($id)
    {
        $data = $this->suratService->detailData($id);
        return response()->json(['status' => true, 'data' => $data], 200);
    }

    public function previewSurat($id)
    {
        $data = $this->suratService->detailData($id);
        return view('backoffice.surat-masuk.preview', compact('id', 'data'));
    }

    public function renderPdf(Request $request, $id)
    {
        try {
            $data = $this->suratService->detailData($id);
            $revision = filter_var($request->revision, FILTER_VALIDATE_BOOLEAN);

            if ($revision && isset($data->childSurat) && $data->childSurat->count() > 0) {
                $data->contentSurat = ContentSurat::where('surat_id', $data->id)->first();
            }

            if (isset($data->requestEsign)) {
                $response = $this->eSignService->fetch('get', "documents/{$data->requestEsign->request_id}/download", [], [
                    'is_response' => true
                ]);
                return $response;
            }

            $not_show_tanggal_surat = filter_var($request->not_show_tanggal_surat, FILTER_VALIDATE_BOOLEAN);
            $kop_surat = isset($request->kop_surat_id) ? KopSurat::find($request->kop_surat_id) : null;

            if ($request->kop_surat_id) {
                $data['content_replace_pdf'] = parseContent($data->contentSurat->content ?? '', $data->id, [
                    'is_pdf' => true
                ], [
                    'kop_surat' => $kop_surat->kop_surat_replaced ?? '',
                    'perihal' => $data->perihal ?? '',
                ]);
            } else {
                $data['content_replace_pdf'] = $data->contentSurat->content_replace_pdf ?? '';
            }

            $html = View::make('backoffice.surat-masuk.preview', compact('data', 'not_show_tanggal_surat'))->render();
            $cetak = 'Surat_' . preg_replace('/[^A-Za-z0-9_\-]/', '', $data->perihal ?? 'Unknown') . '_' . time() . '.pdf';

            $setting = getSettingGroup('default');

            $css = "
                body { 
                    font-family: Arial, 
                    sans-serif; 
                    margin: 0; 
                    padding: 0; 
                    width: 100%; 
                }
                p { margin: 0; }

                table { 
                    width: 100%; 
                    border-collapse: collapse; 
                }
                th, td { 
                    border: 1px solid #ddd; 
                    padding: 8px; 
                    text-align: left; 
                }
            ";

            $format_kertas = ($data->ukuran_kertas === 'F4') ? [210, 330] : 'A4';

            $cmToMm = function($cm) { return $cm * 10; };
            
            $mpdf = new \Mpdf\Mpdf([
                'mode' => 'utf-8',
                'format' => $format_kertas,
                'default_font' => 'Arial',
                'margin_left' => $cmToMm($setting['surat_margin_left'] ?? 10),
                'margin_right' => $cmToMm($setting['surat_margin_right'] ?? 10),
                'margin_top' => $cmToMm($setting['surat_margin_top'] ?? 10),
                'margin_bottom' => $cmToMm($setting['surat_margin_bottom'] ?? 10),
                // 'margin_header' => $setting['surat_margin_header'] ?? 5,
                // 'margin_footer' => $setting['surat_margin_footer'] ?? 5,
                'tempDir' => storage_path('app/mpdf'),
            ]);

            $mpdf->WriteHTML($css, \Mpdf\HTMLParserMode::HEADER_CSS);
            $mpdf->WriteHTML($html, \Mpdf\HTMLParserMode::HTML_BODY);

            if ($request->has('download')) {
                return response()->streamDownload(function () use ($mpdf) {
                    echo $mpdf->Output('', 'S');
                }, $cetak);
            }

            if ($request->has('base64')) {
                $output = $mpdf->Output('', 'S');
                return response()->json([
                    'base64' => base64_encode($output),
                    'filename' => $cetak,
                ]);
            }

            return response($mpdf->Output($cetak, 'I'))->header('Content-Type', 'application/pdf');
        } catch (\Mpdf\MpdfException $e) {
            return response()->json([
                'error' => 'Gagal membuat PDF: ' . $e->getMessage(),
            ], 500);
        }
    }


    public function dataTable(Request $request)
    {
        $unit_bisnis_ids = Auth::user()->user_unit_bisnis_data->pluck('id')->toArray();
        $data = Surat::select(
            'id',
            'jenis_surat',
            'perihal',
            'kop_surat_id',
            'dikirim_oleh_id',
            'unit_bisnis_id',
            'content_surat_id',
            'status',
            'created_by_id',
            'created_at',
            'parent_surat_id',
            'no_surat',
            'is_acc_complete',
            'reply_to'
        )->with([
            'pengirim:id,nama,unit_bisnis_id',
            'unitBisnisSurat',
            'unitBisnis',
            'childSurat' => function ($query) {
                $query->select(['id', 'parent_surat_id', 'created_at', 'type', 'perihal', 'reply_to']);
            },
            'parentSurat:id,no_surat',
            'readSurat:id,surat_id',
            'childSurat.readSurat:id,surat_id',
            'childSurat.replySurat',
        ])
            ->where('parent_surat_id', null)
            ->where(function ($query) use ($request, $unit_bisnis_ids) {
                if (canPermission('Surat Kadaluarsa.List') && $request->surat_expired) {
                    return $query;
                } else {
                    $query->where(function ($query) use ($unit_bisnis_ids) {
                        $query->whereHas('tujuanSurat', function ($q) {
                            $q->where('role_id', auth()->user()->role_id)
                                ->orWhere('user_id', auth()->user()->id);
                        })->where(function ($q) use ($unit_bisnis_ids) {
                            $q->whereHas('unitBisnis', function ($q) use ($unit_bisnis_ids) {
                                $q->whereIn('unit_bisnis_id', $unit_bisnis_ids);
                            })->orWhere('unit_bisnis_id', auth()->user()->unit_bisnis_id);
                        });
                    })
                        ->where(function ($query) {
                            $query->whereNotNull('parent_surat_id')
                                ->orWhereHas('childSurat')
                                ->orWhereDate('created_at', '>', now()->subDays(2));
                        })
                        ->orWhere(function ($query) use ($unit_bisnis_ids) {
                            $query->whereHas('childSurat', function ($q) use ($unit_bisnis_ids) {
                                $q->whereHas('tujuanSurat', function ($q) {
                                    $q->where('role_id', auth()->user()->role_id)
                                        ->orWhere('user_id', auth()->user()->id);
                                })->where(function ($q) use ($unit_bisnis_ids) {
                                    $q->whereHas('unitBisnis', function ($q) use ($unit_bisnis_ids) {
                                        $q->whereIn('unit_bisnis_id', $unit_bisnis_ids);
                                    })->orWhere('unit_bisnis_id', auth()->user()->unit_bisnis_id);
                                });
                            });
                        });
                }
            })
            ->filter($request)
            ->orderBy(DB::raw('COALESCE((SELECT MAX(created_at) FROM surats AS child WHERE child.parent_surat_id = surats.id), surats.created_at)'), 'desc');

        return DataTables::of($data)
            ->addindexColumn()
            ->addColumn('action', function ($data) use ($request) {
                $action = "<a class='avatar-text avatar-md' href='" . route('surat-masuk.show', $data->parent_surat_id ? $data->parent_surat_id : $data->id) . "'>
                                <i class='feather feather-eye'></i>
                            </a>";

                if (canPermission(('Surat Kadaluarsa.List')) && $request->surat_expired) {
                    $action_button = "";
                    if (canPermission('Surat Kadaluarsa.Complete') && $request->surat_expired) {
                        $action_button .= " <li>
                                        <div role='button' class='dropdown-item completeSurat' data-id='$data->id' data-input='" . json_encode(['no_surat' => $data->no_surat]) . "'>
                                            <i class='feather feather-check-circle me-2'></i>
                                            <span>Selesaikan Surat</span>
                                        </div>
                                    </li>";
                    }

                    if (canPermission('Surat Kadaluarsa.Reminder') && $request->surat_expired) {
                        $action_button .= "
                                             <li class='dropdown-divider'></li>
                                            <li>
                                                 <div role='button' class='dropdown-item reminderSurat' data-id='$data->id' data-input='" . json_encode(['no_surat' => $data->no_surat]) . "'>
                                                    <i class='feather feather-bell me-2'></i>
                                                    <span>Ingatkan Surat</span>
                                                </div>
                                            </li>";
                    }

                    $action .= "
                                    <div class='dropdown dropdown-overflow'>
                                        <a href='javascript:void(0)'
                                            class='avatar-text avatar-md btn-dropdown'
                                            data-bs-toggle='dropdown' data-bs-offset='0,21'>
                                            <i class='feather feather-more-horizontal'></i>
                                        </a>
                                        <ul class='dropdown-menu'>
                                            $action_button
                                        </ul>
                                    </div>
                    ";
                }

                return "<div class='d-flex align-items-center gap-2'>
                        $action
                        </div>
                        ";
            })->addColumn('status_surat', function ($data) {
                if ($data->check_surat_expired) {
                    $data->status = Surat::STATUS_EXPIRED;
                }
                $return = status_surat($data->status);
                if ($data->status == Surat::STATUS_SELESAI) {
                    $return .= "<br>";
                    if (intval($data->is_acc_complete) == 1) {
                        $return .=  "<div class='d-inline-block border border-success text-success fw-bold mt-3' style='border-radius: 20px; font-size: 10px;padding: 4px 7px'>
                                            Disetujui
                                        </div>";
                    } else {
                        $return .=  "<div class='d-inline-block border border-danger text-danger fw-bold mt-3' style='border-radius: 20px; font-size: 10px;padding: 4px 7px'>
                                        Tidak Disetujui
                                    </div>";
                    }
                }
                return $return;
            })
            ->addColumn('read_surat', function ($data) {
                $read_surat = $data->readSurat;
                $total_child_surat = $data->childSurat->count() ?? 0;
                $total_read_child_surat = $data->childSurat->sum(function ($item) {
                    return $item->readSurat ? 1 : 0;
                }) ?? 0;
                $read_all_surat = ($total_child_surat - $total_read_child_surat) + ($read_surat ? 0 : 1);
                return $read_all_surat == 0;
            })
            ->addColumn('last_updated', function ($data) {
                $last_child_data = $data->childSurat->sortByDesc('created_at')->first();
                $last_updated = $last_child_data ? $last_child_data->created_at : '';

                $last_type_surat = type_surat($last_child_data->type ?? 0);

                if ($last_updated) {
                    $last_updated = Carbon::parse($last_updated)->translatedFormat('d F Y H:i');
                    $last_updated = "<span class='fw-bold'>$last_child_data->perihal</span>
                                    <br>
                                    <span class='text-muted'>$last_updated</span>";
                } else
                    $last_updated = '<span class="text-danger fw-bold">Belum ada perubahan</span>';


                return $last_updated;
            })
            ->addColumn('date', function ($data) {
                return Carbon::parse($data->created_at)->translatedFormat('d F Y H:i');
            })
            ->rawColumns(['action', 'kop_surat', 'status_surat', 'last_updated', 'date'])
            ->smart(true)
            ->make(true);
    }

    public function completeSurat($id, Request $request)
    {
        if (!canPermission('Surat Masuk.Complete')) {
            return response()->json(['status' => false, 'message' => 'Anda tidak memiliki akses untuk menyelesaikan surat'], 403);
        }
        try {
            $surat_parent = Surat::with('childSurat.tujuanSurat')->findOrFail($id);

            $tujuan_surat_role_id = [];
            $tujuan_surat_user_id = [];
            $tujuan_unit_bisnis_id = [];

            foreach ($surat_parent->tujuanSurat as $tujuan) {
                $tujuan_unit_bisnis_id[] = $tujuan->unit_bisnis_id;
                if ($tujuan->role_id)
                    $tujuan_surat_role_id[] = $tujuan->role_id;
                else
                    $tujuan_surat_user_id[] = $tujuan->user_id;
            }

            foreach ($surat_parent->childSurat as $child) {
                $tujuan_unit_bisnis_id[] = $child->unit_bisnis_id;
                foreach ($child->tujuanSurat as $tujuan) {
                    if ($tujuan->role_id)
                        $tujuan_surat_role_id[] = $tujuan->role_id;
                    else
                        $tujuan_surat_user_id[] = $tujuan->user_id;
                }
            }

            $tujuan_surat_role_id = array_unique($tujuan_surat_role_id);
            $tujuan_surat_user_id = array_unique($tujuan_surat_user_id);
            $tujuan_unit_bisnis_id = array_values(array_unique(array_filter($tujuan_unit_bisnis_id)));

            DB::transaction(function () use ($surat_parent, $tujuan_surat_role_id, $tujuan_surat_user_id, &$surat, $tujuan_unit_bisnis_id, $request) {
                $perihal = "Penyelesaian Surat : " . $surat_parent->perihal;

                $surat = Surat::create([
                    'jenis_surat' => $surat_parent->jenis_surat,
                    'perihal' => $perihal,
                    'kop_surat_id' => $surat_parent->kop_surat_id,
                    'parent_content_surat_id' => $surat_parent->content_surat_id,
                    'no_surat' => $surat_parent->no_surat,
                    'status' => Surat::STATUS_SELESAI,
                    'status_process' => 'Diselesaikan oleh ' . (Auth::user()->role->name ?? '') . ' - ' . (Auth::user()->nama ?? ''),
                    'dikirim_oleh_id' => Auth::id(),
                    'created_by_id' => Auth::id(),
                    'parent_surat_id' => $surat_parent->id,
                    'unit_bisnis_id' => $surat_parent->unit_bisnis_id,
                    'dikirim_oleh_role_id' => Auth::user()->role_id,
                    'type' => 2,
                ]);

                $is_acc = $request->is_acc ?? false;
                $status_process = 'Surat No ' . $surat->no_surat . ' Diselesaikan ['.($is_acc ? "Disetujui" : "Tidak Disetujui").'] oleh ' . (Auth::user()->role->name ?? '') . ' - ' . (Auth::user()->nama ?? '') . ' pada ' . date('d-m-Y H:i:s');

                foreach ($tujuan_unit_bisnis_id as $unit_bisnis_id) {
                    UnitBisnisSurat::create([
                        'surat_id' => $surat->id,
                        'unit_bisnis_id' => $unit_bisnis_id,
                        'surat_parent_id' => $surat_parent->id,
                    ]);

                    $this->suratService->createLogHistory($surat_parent->id, [
                        'status' => $status_process,
                        'type' => 5,
                        'unit_bisnis_id' => $unit_bisnis_id,
                    ]);
                }

                $this->suratService->createLogApproval($surat_parent->id, [
                    'approval_status' => $status_process,
                    'status' => 4,
                    'unit_bisnis_id' => $unit_bisnis_id,
                ]);


                $catatan = $request->catatan ?? '';
                if (!empty($catatan)) {
                    $catatan = "<strong>Catatan Penyelesaian Surat:</strong><br>" . $catatan;
                }
                $content_surat = "<p>
                                    Surat telah Diselesaikan oleh " . (Auth::user()->role->name ?? '') . ' - ' . (Auth::user()->nama ?? '') . " pada " . date('d-m-Y H:i:s') . "
                                    <div class='py-4'>
                                        $catatan
                                    </div>
                                </p>";

                $original_content = $surat_parent->contentSurat->content ?? '';
                $full_content = $content_surat;
                $content_surat = ContentSurat::create([
                    'surat_id' => $surat->id,
                    'content' => $content_surat,
                    'original_content' => $original_content,
                    'full_content' => $full_content,
                    'created_by_id' => Auth::id(),
                ]);

                $tujuan_surat_role = array_map(function ($item) {
                    return [
                        'role_id' => $item,
                        'created_by_id' => Auth::id(),
                    ];
                }, $tujuan_surat_role_id);

                $this->suratService->saveTujuanSurat($surat, $tujuan_surat_role);

                $tujuan_surat_user = array_map(function ($item) {
                    return [
                        'user_id' => $item,
                        'created_by_id' => Auth::id(),
                    ];
                }, $tujuan_surat_user_id);
                $this->suratService->saveTujuanSurat($surat, $tujuan_surat_user);


                $surat->content_surat_id = $content_surat->id;
                $surat->save();


                $surat_parent->update([
                    'status_process' => 'Diselesaikan oleh ' . (Auth::user()->role->name ?? '') . ' - ' . (Auth::user()->nama ?? ''),
                    'status' => Surat::STATUS_SELESAI,
                    'updated_by_id' => Auth::user()->id,
                    'is_acc_complete' => $request->is_acc ?? 0,
                    'catatan_complete' => $request->catatan ?? '',
                ]);


                $tujuan_role_id = array_column($tujuan_surat_role ?? [], 'role_id') ?? [];
                $tujuan_user_id = array_column($tujuan_surat_user ?? [], 'user_id') ?? [];
                $tujuan_user_id = array_merge($tujuan_user_id, [$surat_parent->dikirim_oleh_id]);
                $this->suratService->sendNotificationSurat($surat_parent, $status_process, [
                    'role_id' => $tujuan_role_id,
                    'user_id' => $tujuan_user_id,
                    'unit_bisnis_id' => $tujuan_unit_bisnis_id,
                ]);
            });

            $message = 'Surat No Surat: ' . $surat_parent->no_surat . ' berhasil diselesaikan';
            $data = $this->suratService->detailData($surat->parent_surat_id);

            return response()->json(['status' => true, 'message' => $message, 'data' => $data], 200);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function indexExpired()
    {
        return view('backoffice.surat-expired.index');
    }

    public function reminderSurat($id)
    {
        if (!canPermission('Surat Kadaluarsa.Reminder')) {
            return response()->json(['status' => false, 'message' => 'Anda tidak memiliki akses untuk mengingatkan surat'], 403);
        }
        try {
            $surat = Surat::with(['tujuanSurat'])->findOrFail($id);
            $status_process = 'Surat No ' . $surat->no_surat . ' Diingatkan oleh ' . (Auth::user()->role->name ?? '') . ' - ' . (Auth::user()->nama ?? '') . ' pada ' . date('d-m-Y H:i:s');
            $this->suratService->createLogHistory($surat->id, [
                'status' => $status_process,
                'type' => 6,
                'unit_bisnis_id' => $surat->unit_bisnis_id,
            ]);

            $tujuan_unit_bisnis_id = $surat->unitBisnisSurat->pluck('unit_bisnis_id')->toArray() ?? [];
            $tujuan_unit_bisnis_id = array_merge($tujuan_unit_bisnis_id, [$surat->unit_bisnis_id]);
            $tujuan_unit_bisnis_id = array_values(array_unique(array_filter($tujuan_unit_bisnis_id)));
            $tujuan_surat_role_id = $surat->tujuanSurat->pluck('role_id')->toArray() ?? [];
            $tujuan_surat_user_id = $surat->tujuanSurat->pluck('user_id')->toArray() ?? [];
            $message = 'Surat No ' . $surat->no_surat . ' Belum Diproses sejak ' . date('d-m-Y H:i:s') . ' Mohon segera diproses';

            $this->suratService->sendNotificationSurat($surat, $message, [
                'role_id' => $tujuan_surat_role_id,
                'user_id' => $tujuan_surat_user_id,
                'unit_bisnis_id' => $tujuan_unit_bisnis_id,
            ]);

            return response()->json(['status' => true, 'message' => 'Surat berhasil diingatkan'], 200);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function readSurat(Request $request, $id)
    {
        $data = TandaTanganSurat::with('surat', 'surat.jenisSurat', 'surat.unitBisnis', 'user', 'referable', "surat.history", "surat.history.user:id,nama,email")->findOrFail($id);

        return view('backoffice.surat-masuk.signature', compact('data'));
    }


    public function storeEsign(Request $request, $id)
    {
        try {
            $surat_parent = Surat::findOrFail($id);

            $requestRender = new Request([
                'base64' => true,
            ]);
            $document = $this->renderPdf($requestRender, $surat_parent->id);

            $signers = [];
            foreach ($request->annotations ?? [] as $annotation) {
                $signer = $annotation['signer'] ?? [];
                unset($annotation['signer']);
                $signers[] = [
                    'name' => $signer['nama'] ?? '',
                    'email' => $signer['email'] ?? '',
                    'annotations' => [
                        [

                            'type_of' => 'signature',
                            'element_width' => intval($annotation['element_width'] ?? 0),
                            'element_height' => intval($annotation['element_height'] ?? 0),
                            ...$annotation,

                        ]
                    ],
                ];
            }

            $data = [
                'doc' => $document['base64'],
                'filename' => $document['filename'],
                'signers' => $signers,
                'signing_order' => false,
                // 'signing_url' => true,
                'callback_url' => route('e-sign.webhook-signing'),
            ];

            $url_api = 'documents/request_psre_sign';
            if (getSettingValue('type_esign') == 'global') {
                $url_api = 'documents/request_global_sign';
                $data['signing_url'] = true;
            }
            $response = $this->eSignService->fetch('post', $url_api, $data);
            if (!($response['data']['id'] ?? ''))
                return response()->json(['status' => false, 'message' => $response['data']['message'] ?? 'Tidak bisa memproses permintan eSign, silahkan coba kembali'], 500);
            if (getSettingValue('type_esign') == 'psre')
                $response = $this->eSignService->fetch('post', "documents/{$response['data']['id']}/generate_signing_url", []);

            DB::transaction(function () use ($surat_parent, $response, $request) {
                $data = $response['data'];
                $attribute = $data['attributes'] ?? null;
                if (!$attribute)
                    return response()->json(['status' => false, 'message' => 'Tidak bisa memproses permintan, silahkan coba kembali'], 500);
                $request_esign = RequestEsign::create([
                    'surat_parent_id' => $surat_parent->id,
                    'surat_id' => $request->surat_id,
                    'request_id' => $data['id'],
                    'type' => $data['type'] ?? '',
                    'category' => $attribute['category'],
                    'doc_url' => $attribute['doc_url'],
                    'filename' => $attribute['filename'],
                    'signing_status' => $attribute['signing_status'],
                    'request_created_at' => $attribute['created_at'],
                    'request_updated_at' => $attribute['updated_at'],
                    'request_by_user_id' => Auth::id(),
                ]);

                $signers = $attribute['signers'] ?? [];
                foreach ($signers as $signer) {
                    RequestEsignSigners::create([
                        'request_esign_id' => $request_esign->id,
                        'request_id' => $request_esign->request_id,
                        'name' => $signer['name'],
                        'email' => $signer['email'],
                        'phone' => $signer['phone'],
                        'order' => $signer['order'],
                        'signing_url' => $signer['signing_url'],
                        'status' => $signer['status'],
                        'signed_at' => $signer['signed_at'],
                    ]);
                }
            });

            return response()->json([
                'status' => true,
                'data' => $response['data'],
                'message' => 'Permintaan tanda tangan berhasil dibuat'
            ], 200);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()], 500);
        }
    }
}
