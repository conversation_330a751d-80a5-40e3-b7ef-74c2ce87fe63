<?php

namespace App\Http\Controllers;

use PDF;
use Imagick;
use Dompdf\Dompdf;
use Dompdf\Options;
use App\Models\KopSurat;
use Illuminate\Http\Request;

use Illuminate\Support\Facades\DB;
use Spa<PERSON>\Browsershot\Browsershot;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\View;

use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;

class KopSuratController extends Controller
{

    public function list(Request $request)
    {
        $user = KopSurat::select('id', 'nama',)
            ->where('nama', 'like', '%' . $request->keyword . '%')
            ->filter($request)
            ->get();
        return response()->json($user);
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('backoffice.kop-surat.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('backoffice.kop-surat.create-update');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $id = $request->id;

        $validator = [
            'nama' => 'required|unique:kop_surats,nama',
            'kop_surat' => 'required'
        ];

        if ($id) {
            $validator['nama'] = 'required|unique:kop_surats,nama,' . $id;
        }

        $request->validate($validator);

        DB::transaction(function () use ($request, $id) {
            $input = $request->all();

            $input['unit_bisnis_id'] = json_encode($input['unit_bisnis_id']);

            $kop_surat = KopSurat::updateOrCreate([
                'id' => $id,
            ],  $input);

            $pdfPath = $this->generatePDFPreview($kop_surat);
            $imagePath = $this->convertPdfToImage($pdfPath['pdfPath'], $pdfPath['fileName']);

            // $url = route('kop-surat.preview', $kop_surat->id);
            // $path = public_path('uploads/preview-page/' . $kop_surat->id . '.jpg');

            // if (!file_exists(public_path('uploads/preview-page'))) {
            //     mkdir(public_path('uploads/preview-page'), 0755, true);
            // }

            // Browsershot::url($url)
            //     ->setOption('args', ['--no-sandbox'])
            //     ->save($path);

            $kop_surat->preview_page = $imagePath;
            $kop_surat->save();

            return $kop_surat;
        });



        return redirect(route('kop-surat.index'))->with('success', 'Kop Surat berhasil disimpan');
    }


    function generatePDFPreview($data)
    {
        $html = View::make('backoffice.kop-surat.preview', compact('data'))->render();

        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isRemoteEnabled', true);
        $dompdf = new Dompdf($options);
        
        $dompdf->loadHtml($html);
        $dompdf->setPaper('F4', 'landscape');
        $dompdf->render();
        
        $pdfOutput = $dompdf->output();
        $fileName = 'kop-surat-' . $data->id;
        $pdfPath = 'uploads/kop-surat/' . $fileName . '.pdf';

        if (!File::isDirectory(public_path('uploads/kop-surat'))) {
            File::makeDirectory(public_path('uploads/kop-surat'), 0755, true, true);
        }

        file_put_contents(public_path($pdfPath), $pdfOutput);
    
        return [
            'pdfPath' => $pdfPath,
            'fileName' => $fileName
        ];
    }


    function convertPdfToImage($pdfPath, $fileName)
    {
        $setting = getSettingGroup('default');
        $imagick = new Imagick();
        $imagick->setResolution(200, $setting['height_kop_surat'] ?? 150);
        $imagick->readImage($pdfPath . '[0]');
        $imagick->setImageFormat('png');

        $imagePath = 'uploads/kop-surat/' . $fileName . '.png';
        $path = public_path($imagePath);
        $imagick->writeImage($path);

        return $imagePath;
    }

    public function preview(Request $request, $id)
    {
        $data = KopSurat::findOrFail($id);
        if($request->preview_pdf) {
            $pdf = PDF::loadview('backoffice.kop-surat.preview', compact('data'))
                    ->setPaper('F4', 'portrait');

            $cetak = 'Kop Surat ' . time() . '.pdf';
            return $pdf->stream($cetak);
        }

        return view('backoffice.kop-surat.preview', compact('data'));
    }

    public function previewStore(Request $request)
    {
        $data['kop_surat'] = replaceContentImage($request->kop_surat);

        $cetak = 'Kop Surat ' . time() . '.pdf';
        $preview = true;
        $setting = getSettingGroup('default');
        $pdf = PDF::loadview('backoffice.kop-surat.preview', compact('data'))
            ->setPaper('F4', 'portrait')
            ->setOption('margin-top', $setting['surat_margin_top'] ?? 10)
            ->setOption('margin-right', $setting['surat_margin_right'] ?? 10)
            ->setOption('margin-bottom', $setting['surat_margin_bottom'] ?? 10)
            ->setOption('margin-left', $setting['surat_margin_left'] ?? 10);

        $output = $pdf->output();
        $base64Pdf = base64_encode($output);
        return $base64Pdf;

    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data = KopSurat::findOrFail($id);
        return view('backoffice.kop-surat.create-update', compact('data'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $kop_surat = KopSurat::findOrFail($id);
            DB::transaction(function () use ($kop_surat) {
                $kop_surat->delete();
            });

            return response()->json(['status' => true, 'message' => 'Data berhasil dihapus'], 200);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th->getMessage()], 500);
        }
    }


    public function dataTable(Request $request)
    {
        $data = KopSurat::select(
            'id',
            'nama',
            'kop_surat',
            'preview_page'
        )
            ->latest()
            ->filter($request);

        return DataTables::of($data)
            ->addindexColumn()
            ->addColumn('action', function ($data) {

                $action_button = '';

                if (canPermission('Kop Surat.Edit')) {
                    $action_button .= "<li>
                                        <a class='dropdown-item' href='" . route('kop-surat.edit', $data->id) . "'>
                                            <i class='feather feather-edit-3 me-3'></i>
                                            <span>Edit</span>
                                        </a>
                                    </li>";
                }

                if (canPermission('Kop Surat.Delete')) {
                    $action_button .= "
                                    <li class='dropdown-divider'></li>
                                    <li>
                                        <a class='dropdown-item deleteData' href='javascript:void(0)'  data-id='$data->id' data-input='" . json_encode(['id' => $data->id, 'nama' => $data->nama]) . "'>
                                            <i class='feather feather-trash-2 me-3'></i>
                                            <span>Delete</span>
                                        </a>
                                    </li>";
                }


                $action = " <div class='hstack gap-2'>
                                                <div class='dropdown dropdown-overflow'>
                                                    <a href='javascript:void(0)'
                                                        class='avatar-text avatar-md btn-dropdown'
                                                        data-bs-toggle='dropdown' data-bs-offset='0,21'>
                                                        <i class='feather feather-more-horizontal'></i>
                                                    </a>
                                                    <ul class='dropdown-menu'>
                                                        $action_button
                                                    </ul>
                                                </div>
                                            </div>";
                return $action;
            })->addColumn('kop_surat', function ($data) {
                if($data->preview_page) {
                    return "<img src='" . asset($data->preview_page) . "' class='img-thumbnail' alt='Kop Surat' style='width: 400px; height: 80px; object-fit: cover; object-position: top;'>";
                }
            })
            ->rawColumns(['action', 'kop_surat'])
            ->smart(true)
            ->make(true);
    }
}
