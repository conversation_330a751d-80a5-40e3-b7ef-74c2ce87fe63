<?php

namespace App\Http\Controllers;

use App\Models\JenisSurat;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\Facades\DataTables;

class JenisSuratController extends Controller
{
    public function list(Request $request)
    {
        $unit_bisnis_id = $request->unit_bisnis_id;
        $user = JenisSurat::select('id', 'jenis_surat',)
            ->where('jenis_surat', 'like', '%' . $request->keyword . '%')
            ->when($unit_bisnis_id, function ($q) use ($unit_bisnis_id) {
                $q->whereJsonContains('unit_bisnis_id', $unit_bisnis_id);
            })
            ->get();
        return response()->json($user);
    }

    public function index()
    {
        return view('backoffice.jenis-surat.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $id = $request->jenis_surat_id;
        $request->validate([
            'jenis_surat' => $request->jenis_surat_id ? 'required|unique:jenis_surats,jenis_surat,' . $request->jenis_surat_id : 'required|unique:jenis_surats,jenis_surat',
            'type' => 'required',
        ]);

        $input = $request->all();

        try {
            DB::transaction(function () use ($input, $id, $request) {
                $input['created_by_id'] = Auth::user()->id;
                $input['unit_bisnis_id'] = json_encode($input['unit_bisnis_id']);
                JenisSurat::updateOrCreate(['id' => $id], $input);
            });

            return response()->json(['status' => true, 'message' => 'Data berhasil disimpan'], 200);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $jenis_surat = JenisSurat::findOrFail($id);
            DB::transaction(function () use ($jenis_surat) {
                $jenis_surat->delete();
            });

            return response()->json(['status' => true, 'message' => 'Data berhasil dihapus'], 200);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th->getMessage()], 500);
        }
    }

    public function dataTable(Request $request)
    {
        $data = JenisSurat::select(
            'id',
            'jenis_surat',
            'kode',
            'type',
            'unit_bisnis_id',
            'created_at',
            'kode_type'
        )
        ->filter($request)
            ->latest();

        return DataTables::of($data)
            ->addindexColumn()
            ->addColumn('action', function ($data) {
                $action_button = "";
                if (canPermission('Jenis Surat.Edit')) {
                    $action_button .= " <li>
                                    <a class='dropdown-item editInput' href='javascript:void(0)' data-id='$data->id' data-input='" . json_encode($data) . "'>
                                        <i class='feather feather-edit-3 me-3'></i>
                                        <span>Edit</span>
                                    </a>
                                </li>";
                }

                if (canPermission('Jenis Surat.Delete')) {
                    $action_button .= "
                                <li class='dropdown-divider'></li>
                                <li>
                                    <a class='dropdown-item deleteData' href='javascript:void(0)' data-id='$data->id' data-input='" . json_encode($data) . "'>
                                        <i class='feather feather-trash-2 me-3'></i>
                                        <span>Delete</span>
                                    </a>
                                </li>";
                }


                $action = " <div class='hstack gap-2'>
                                                <div class='dropdown dropdown-overflow'>
                                                    <a href='javascript:void(0)'
                                                        class='avatar-text avatar-md btn-dropdown'
                                                        data-bs-toggle='dropdown' data-bs-offset='0,21'>
                                                        <i class='feather feather-more-horizontal'></i>
                                                    </a>
                                                    <ul class='dropdown-menu'>
                                                        $action_button
                                                    </ul>
                                                </div>
                                            </div>";
                return $action;
            })
            ->rawColumns(['action'])
            ->smart(true)
            ->make(true);
    }
}
