<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Mail\SendEmailEsign;
use Illuminate\Http\Request;
use App\Models\DocumentEsign;
use Illuminate\Support\Facades\DB;
use Spatie\Browsershot\Browsershot;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\View;
use Yajra\DataTables\Facades\DataTables;

class UploadFileEsignController extends Controller
{
    public function index()
    {
        return view('backoffice.upload-document-esign.index');
    }

    public function create()
    {
        return view('backoffice.upload-document-esign.create-update');
    }

    public function store(Request $request)
    {
        try {
            $request->validate([
                'file' => 'required|mimes:pdf|max:12288',
                'signers' => 'required|array',
                'signers.*.nama' => 'required|string',
                'signers.*.email' => 'required|email',
                'signers.*.value' => 'required|string',
                'annotations' => 'required|array',
                'annotations.*.position_x' => 'required',
                'annotations.*.position_y' => 'required',
                'annotations.*.element_width' => 'required',
                'annotations.*.element_height' => 'required',
                'annotations.*.canvas_width' => 'required',
                'annotations.*.canvas_height' => 'required',
                'annotations.*.page' => 'required',
            ]);

            DB::transaction(function () use ($request) {
                $document_file = $request->file('file');
                $upload_path = upload_file($document_file, 'documents-esign', 'document-esign');
                $documentEsign = DocumentEsign::create([
                    'document_name' => $document_file->getClientOriginalName(),
                    'document_type' => $document_file->getClientMimeType(),
                    'document_size' => "-",
                    'document_extension' => $document_file->getClientOriginalExtension(),
                    'document_path' => $upload_path,
                    'status' => 'IN PROGRESS',
                    'unit_bisnis_id' => auth()->user()->unit_bisnis_id,
                    'created_by_user_id' => auth()->id(),
                ]);

                $signers = [];
                foreach ($request->signers as $signer) {
                    $signers[] = [
                        'document_esign_id' => $documentEsign->id,
                        'contact_id' => $signer['value'],
                        'nama' => $signer['nama'],
                        'email' => $signer['email'],
                    ];
                }
                $documentEsign->signers()->createMany($signers);

                $annotations = [];
                foreach ($request->annotations as $annotation) {
                    $signer = $documentEsign->signers()->where('contact_id', $annotation['signer']['value'])->first();

                    $data_encrypt = encrypt([
                        'document_esign_id' => $documentEsign->id,
                        'signer_document_esign_id' => $signer ? $signer['id'] : null,
                        'email' => $signer ? $signer['email'] : null,
                    ]);

                    $signin_url = route('documentEsign', $documentEsign->id) . "?data=$data_encrypt";
                    $annotations[] = [
                        'document_esign_id' => $documentEsign->id,
                        'signer_document_esign_id' => $signer ? $signer['id'] : null,
                        'position_x' => $annotation['position_x'],
                        'position_y' => $annotation['position_y'],
                        'element_width' => $annotation['element_width'],
                        'element_height' => $annotation['element_height'],
                        'canvas_width' => $annotation['canvas_width'],
                        'canvas_height' => $annotation['canvas_height'],
                        'page' => $annotation['page'],
                        'status' => 'PENDING',
                        'signing_url' => $signin_url,
                    ];
                }
                $documentEsign->annotations()->createMany($annotations);


                foreach ($documentEsign->signers as $signer) {
                    $data = [
                        'document_name' => $documentEsign->document_name,
                        'document_path' => $documentEsign->document_path,
                        'signing_url' => $signer->annotations->first()->signing_url ?? '',
                        'from' => auth()->user()->name,
                    ];
                    $subject = "Anda mendapat dokumen yang perlu ditandatangani $documentEsign->document_name dari " . auth()->user()->name;
                    Mail::to($signer->email)->send(new SendEmailEsign($data, $subject));
                }
            });

            return response()->json([
                'status' => true,
                'message' => 'Document E-Signature berhasil diupload, dan tunggu konfirmasi dari pihak yang ditunjuk untuk menandatangani dokumen ini.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function documentEsign($id, Request $request)
    {
        try {
            $data = decrypt($request->data);
        } catch (\Exception $e) {
            return redirect('/')->with('error', 'Invalid data provided.');
        }

        $documentEsign = DocumentEsign::findOrFail($id);
        $signer = $documentEsign->signers()->where('id', $data['signer_document_esign_id'])->first();
        if (!$signer) {
            return redirect('/');
        }

        $annotations = $documentEsign->annotations()->where('signer_document_esign_id', $signer->id)->get();

        return view('backoffice.upload-document-esign.document', compact('documentEsign', 'signer', 'annotations'));
    }

    public function dataTable(Request $request)
    {
        $unit_bisnis_ids = Auth::user()->user_unit_bisnis_data->pluck('id')->toArray();

        $data = DocumentEsign::with(['signers', 'annotations'])
            // ->whereHas('signers', function ($query) use ($unit_bisnis_ids) {
            //     $query->whereIn('unit_bisnis_id', $unit_bisnis_ids);
            // })
            ->orderBy('created_at', 'desc');

        return DataTables::of($data)
            ->addindexColumn()
            ->addColumn('action', function ($data) {

                $action_button = '';

                $action_button .= " <a class='avatar-text avatar-md' href='" . route('surat-keluar.show', $data->parent_surat_id ? $data->parent_surat_id : $data->id) . "'>
                                <i class='feather feather-eye'></i>
                            </a>";

                if (canPermission('Surat Keluar.Delete')) {
                    $action_button .= "<a class='avatar-text avatar-md deleteData bg-danger text-white' href='javascript:void(0)'  data-id='$data->id' data-input='" . json_encode(['id' => $data->id, 'perihal' => $data->perihal]) . "'>
                                <i class='feather feather-trash-2'></i>
                            </a>";
                }

                $action = "<div class='d-flex align-items-center gap-2'>
                                $action_button
                            </div>";

                return $action;
            })
            ->addColumn('created_at', function ($data) {
                return Carbon::parse($data->created_at)->translatedFormat('d F Y H:i');
            })
            ->rawColumns(['action', 'kop_surat', 'status_surat'])
            ->smart(true)
            ->make(true);
    }

    public function signature(Request $request)
    {
        $document_id = $request->document_id;
        $html = View::make('signature', compact('document_id'))->render();
        return $html;
        
    }
}
