<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\Facades\DataTables;

class ContactController extends Controller
{
    public function list(Request $request)
    {
        $user = Contact::select('id', 'nama', 'email')
            ->when($request->keyword, function ($q) use ($request) {
                $q->where('nama', 'like', '%' . $request->keyword . '%')
                    ->orWhere('email', 'like', '%' . $request->keyword . '%');
            })
            ->orderBy('nama')
            ->get();
        return response()->json($user);
    }

    public function index()
    {
        return view('backoffice.contact.index');
    }

    public function store(Request $request)
    {
        $id = $request->kontak_id;
        $request->validate([
            'nama' => 'required',
            'email' => [
                'required',
                'email',
                'max:255',
                'unique:contacts,email,' . $id . ',id,deleted_at,NULL',
                'unique:users,email,' . $id . ',id',
            ],
        ]);

        $input = $request->all();

        try {
            DB::transaction(function () use ($input, $id) {
                Contact::updateOrCreate(['id' => $id], $input);
            });

            return response()->json(['status' => true, 'message' => 'Data berhasil disimpan'], 200);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th->getMessage()], 500);
        }
    }

    public function destroy(string $id)
    {
        try {
            $contact = Contact::findOrFail($id);
            DB::transaction(function () use ($contact) {
                $contact->delete();
            });

            return response()->json(['status' => true, 'message' => 'Data berhasil dihapus'], 200);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th->getMessage()], 500);
        }
    }

    public function dataTable(Request $request)
    {
        $data = Contact::select(
            'id',
            'nama',
            'email',
            'keterangan',
        )
            ->filter($request)
            ->latest();

        return DataTables::of($data)
            ->addindexColumn()
            ->addColumn('action', function ($data) {
                $action_button = "";
                if (canPermission('Kontak.Edit')) {
                    $action_button .= " <li>
                                    <a class='dropdown-item editInput' href='javascript:void(0)' data-id='$data->id' data-input='" . json_encode($data) . "'>
                                        <i class='feather feather-edit-3 me-3'></i>
                                        <span>Edit</span>
                                    </a>
                                </li>";
                }

                if (canPermission('Kontak.Delete')) {
                    $action_button .= "
                                <li class='dropdown-divider'></li>
                                <li>
                                    <a class='dropdown-item deleteData' href='javascript:void(0)' data-id='$data->id' data-input='" . json_encode($data) . "'>
                                        <i class='feather feather-trash-2 me-3'></i>
                                        <span>Delete</span>
                                    </a>
                                </li>";
                }


                $action = " <div class='hstack gap-2'>
                                                <div class='dropdown dropdown-overflow'>
                                                    <a href='javascript:void(0)'
                                                        class='avatar-text avatar-md btn-dropdown'
                                                        data-bs-toggle='dropdown' data-bs-offset='0,21'>
                                                        <i class='feather feather-more-horizontal'></i>
                                                    </a>
                                                    <ul class='dropdown-menu'>
                                                        $action_button
                                                    </ul>
                                                </div>
                                            </div>";
                return $action;
            })
            ->rawColumns(['action'])
            ->smart(true)
            ->make(true);
    }
}
