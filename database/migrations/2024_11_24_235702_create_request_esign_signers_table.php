<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('request_esign_signers', function (Blueprint $table) {
            $table->uuid('id');
            $table->uuid('request_esign_id');
            $table->uuid('request_id');
            $table->string('name');
            $table->string('email');
            $table->string('phone')->nullable();
            $table->string('order')->nullable();
            $table->text('signing_url')->nullable();
            $table->string('status')->nullable();
            $table->string('signed_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('request_esign_signers');
    }
};
