<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('request_esigns', function (Blueprint $table) {
            $table->uuid('id');
            $table->uuid('surat_id');
            $table->uuid('request_id');
            $table->string('type');
            $table->string('category')->nullable();
            $table->text('doc_url')->nullable();
            $table->string('filename')->nullable();
            $table->string('signing_status')->nullable();
            $table->string('request_created_at')->nullable();
            $table->string('request_updated_at')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('request_esigns');
    }
};
