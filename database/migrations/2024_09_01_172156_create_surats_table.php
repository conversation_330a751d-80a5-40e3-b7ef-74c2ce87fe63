<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('surats', function (Blueprint $table) {
            $table->uuid('id');
            $table->string('jenis_surat')->nullable();
            $table->text('perihal')->nullable();
            $table->uuid('kop_surat_id')->nullable();
            $table->uuid('dikirim_oleh_id')->nullable();
            $table->uuid('dikirim_oleh_role_id')->nullable();
            $table->uuid('unit_bisnis_id')->nullable();
            $table->uuid('content_surat_id')->nullable();
            $table->uuid('parent_surat_id')->nullable();
            $table->string('status')->nullable()->comment('0: Draft, 1: <PERSON><PERSON><PERSON><PERSON>, 2: <PERSON><PERSON><PERSON><PERSON>, 3: Seles<PERSON>');
            $table->string('status_proses')->nullable();

            $table->uuid('created_by_id')->nullable();
            $table->uuid('updated_by_id')->nullable();
            $table->uuid('deleted_by_id')->nullable();

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('surats');
    }
};
