<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('approval_history_surats', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('surat_id');
            $table->uuid('user_id')->nullable();
            $table->uuid('role_id')->nullable();
            $table->uuid('unit_bisnis_id')->nullable();
            $table->longText('approval_status')->nullable();
            $table->integer('status')->nullable()->comment('1: Disetujui, 2: Disetujui dengan pertimbangan, 3: Tidak Disetujui');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('approval_history_surats');
    }
};
