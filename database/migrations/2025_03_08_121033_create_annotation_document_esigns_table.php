<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('annotation_document_esigns', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('document_esign_id');
            $table->uuid('signer_document_esign_id')->nullable();

            $table->integer('position_x')->nullable();
            $table->integer('position_y')->nullable();
            $table->integer('element_width')->nullable();
            $table->integer('element_height')->nullable();
            $table->integer('canvas_width')->nullable();
            $table->integer('canvas_height')->nullable();
            $table->integer('page')->nullable();

            $table->longText('annotation')->nullable();
            $table->string('status')->nullable();
            $table->longText('signing_url')->nullable();
            $table->string('signed_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('annotation_document_esigns');
    }
};
