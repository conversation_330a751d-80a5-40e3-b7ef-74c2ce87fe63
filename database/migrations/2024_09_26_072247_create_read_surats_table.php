<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('read_surats', function (Blueprint $table) {
            $table->uuid('id');
            $table->uuid('surat_id');
            $table->uuid('user_id');
            $table->integer('type')->nullable()->comment('1: Created, 2: Reply, 3: Approval, 4: Disposisi');
            $table->uuid('role_id')->nullable();
            $table->uuid('unit_bisnis_id')->nullable();
            $table->uuid('content_surat_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('read_surats');
    }
};
