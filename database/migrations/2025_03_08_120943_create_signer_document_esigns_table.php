<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('signer_document_esigns', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('document_esign_id');
            $table->uuid('contact_id');
            $table->longText('signing_url')->nullable();
            $table->string('nama');
            $table->string('email');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('signer_document_esigns');
    }
};
