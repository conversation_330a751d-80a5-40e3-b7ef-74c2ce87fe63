<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('document_esigns', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('document_name');
            $table->string('document_type');
            $table->string('document_size')->nullable();
            $table->string('document_extension');
            $table->longText('document_path');
            $table->string('status');
            $table->uuid('unit_bisnis_id');
            $table->uuid('created_by_user_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('document_esigns');
    }
};
