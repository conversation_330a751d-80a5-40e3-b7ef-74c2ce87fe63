<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tanda_tangan_surats', function (Blueprint $table) {
            $table->uuid('id');
            $table->uuid('user_id');
            $table->uuid('role_id')->nullable();
            $table->uuid('content_surat_id')->nullable();
            $table->uuid('unit_bisnis_id')->nullable();
            $table->uuid('surat_id')->nullable();
            $table->string('posisi_x')->nullable();
            $table->string('posisi_y')->nullable();
            $table->string('height')->nullable();
            $table->string('width')->nullable();
            $table->uuid('disposisi_id')->nullable();
            $table->bigInteger('page')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tanda_tangan_surats');
    }
};
