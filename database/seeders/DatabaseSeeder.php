<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

use App\Models\User;
use App\Models\JenisSurat;
use App\Models\Permission;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // \App\Models\User::factory(10)->create();

        // User::create([
        //     'nama' => 'Admin',
        //     'email' => '<EMAIL>',
        //     'password' => bcrypt('password'),
        // ]);
        // JenisSurat::create([
        //     'jenis_surat' => '<PERSON><PERSON><PERSON><PERSON>',
        //     'created_by_id' => '9cd4f19c-a200-466b-9d5c-5b5929f432f0',
        // ]);

        // $permission = [
        //     'Kop Surat.List',
        //     'Kop Surat.Create',
        //     'Kop Surat.Edit',
        //     'Kop Surat.Delete',

        //     'Unit Bisnis.List',
        //     'Unit Bisnis.Create',
        //     'Unit Bisnis.Edit',
        //     'Unit Bisnis.Delete',

        //     'Pengguna.List',
        //     'Pengguna.Create',
        //     'Pengguna.Edit',
        //     'Pengguna.Delete',

        //     'Surat Keluar.List',
        //     'Surat Keluar.Create',
        //     'Surat Keluar.Revisi',
        //     'Surat Keluar.Delete',

        //     'Role & Permission.List',
        //     'Role & Permission.Create',
        //     'Role & Permission.Edit',
        //     'Role & Permission.Delete',

        // ];

        // $permission = [
        //     'Disposisi.Checker',
        //     'Disposisi.Approver',
        //     'Disposisi.Corsec',
        //     'Disposisi.Full_Akses',

        //     'Surat Masuk.List',
        //     'Surat Masuk.Complete',

        //     'Surat Masuk.Reply',
        //     'Surat Masuk.Approve',

        // ];

        // $permission = [
        //     'Surat Keluar.Full_Akses',
        // ];

        $permission = [
            'Surat Kadaluarsa.List',
            'Surat Kadaluarsa.Reminder',
            'Surat Kadaluarsa.Complete',
        ];

        foreach ($permission as $key => $value) {
            Permission::create(['name' => $value, 'guard_name' => 'web']);
        }
    }
}
