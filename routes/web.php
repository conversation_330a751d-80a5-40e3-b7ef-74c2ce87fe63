<?php

use App\Models\Surat;
use App\Models\ReadSurat;
use App\Models\SuratMasuk;
use App\Mail\SendEmailEsign;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Route;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Request;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\EsignerController;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use App\Http\Controllers\KopSuratController;
use App\Http\Controllers\PenggunaController;
use App\Http\Controllers\JenisSuratController;
use App\Http\Controllers\RekapSuratController;
use App\Http\Controllers\SuratMasukController;
use App\Http\Controllers\UnitBisnisController;
use App\Http\Controllers\FileManagerController;
use App\Http\Controllers\SuratKeluarController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\ProfileCustomController;
use App\Http\Controllers\DefaultSettingController;
use App\Http\Controllers\RolePermissionController;
use App\Http\Controllers\UploadFileEsignController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return redirect(route('login'));
});

Route::get('/dashboard', function () {
    $unit_bisnis_ids = Auth::user()->user_unit_bisnis_data->pluck('id')->toArray();

    $surat_masuk_count  = new Surat();
    // if(!canPermission('Dashboard.Full_Akses'))
        $surat_masuk_count = $surat_masuk_count->where(function ($query) use ($unit_bisnis_ids) {
            $query->where(function($query) use ($unit_bisnis_ids) {
                $query->whereHas('tujuanSurat', function ($q) use ($unit_bisnis_ids) {
                    $q->where('role_id', auth()->user()->role_id)
                      ->orWhere('user_id', auth()->user()->id);
                })->where(function ($q) use ($unit_bisnis_ids) {
                    $q->whereHas('unitBisnis', function ($q) use ($unit_bisnis_ids) {
                        $q->whereIn('unit_bisnis_id', $unit_bisnis_ids);
                    })->orWhere('unit_bisnis_id', auth()->user()->unit_bisnis_id);
                })->where(function($query) {
                    $query->whereNotNull('parent_surat_id')
                          ->orWhereHas('childSurat')
                          ->orWhereDate('created_at', '>', now()->subDays(2));
                });
            });
        });
    $surat_masuk_count = $surat_masuk_count->count();

    $surat_masuk_read = ReadSurat::where('user_id', Auth::id())->count();
    $surat_masuk_unread_count = $surat_masuk_count - $surat_masuk_read;


    $surat_kadaluarsa_count = Surat::where('parent_surat_id', null)
                                    ->whereDoesntHave('childSurat')
                                    ->where('created_at', '<=', now()->subDays(2))
                                    ->count();

    $disposisi_count = Surat::
    where('type', 3);
    // if(!canPermission('Dashboard.Full_Akses'))
        $disposisi_count = $disposisi_count->where(function ($query) use ($unit_bisnis_ids) {
            $query->where(function($query) use ($unit_bisnis_ids) {
                $query->whereHas('tujuanSurat', function ($q) use ($unit_bisnis_ids) {
                    $q->where('role_id', auth()->user()->role_id)
                      ->orWhere('user_id', auth()->user()->id);
                })->where(function ($q) use ($unit_bisnis_ids) {
                    $q->whereHas('unitBisnis', function ($q) use ($unit_bisnis_ids) {
                        $q->whereIn('unit_bisnis_id', $unit_bisnis_ids);
                    })->orWhere('unit_bisnis_id', auth()->user()->unit_bisnis_id);
                });
            });
        });

    $disposisi_count = $disposisi_count->count();

    return view('dashboard', compact('surat_masuk_count', 'surat_kadaluarsa_count', 'disposisi_count', 'surat_masuk_unread_count'));
})->middleware(['auth', 'verified'])->name('dashboard');

Route::get('email-content', function () {
    return view('backoffice.email.surat-notification');
});

Route::get('surat-masuk/render-pdf/{id}', [SuratMasukController::class, 'renderPdf'])->name('renderPdf');

Route::group(['middleware' => ['auth']], function () {
    Route::group(['controller' => ProfileCustomController::class], function () {
        Route::get('/profile', 'edit')->name('profile.edit');
        Route::patch('/profile', 'update')->name('profile.update');
    });

    Route::group(['controller' => PenggunaController::class, 'prefix' => 'pengguna', 'as' => 'pengguna.'], function () {
        Route::get('/', 'index')->name('index');
        Route::get('/create', 'create')->name('create');
        Route::post('/', 'store')->name('store');
        Route::get('/{id}/edit', 'edit')->name('edit');
        Route::put('/{id}', 'update')->name('update');
        Route::delete('/{id}', 'destroy')->name('destroy');

        Route::post('/dataTable', 'dataTable')->name('dataTable');

        Route::get('/list-group', 'listGroup')->name('list');
    });

    Route::group(['controller' => UnitBisnisController::class, 'prefix' => 'unit-bisnis', 'as' => 'unit-bisnis.'], function () {
        Route::get('/', 'index')->name('index');
        Route::get('/create', 'create')->name('create');
        Route::post('/', 'store')->name('store');
        Route::get('/{id}', 'show')->name('show');
        Route::get('/{id}/edit', 'edit')->name('edit');
        Route::put('/{id}', 'update')->name('update');
        Route::delete('/{id}', 'destroy')->name('destroy');

        Route::post('/dataTable', 'dataTable')->name('dataTable');

        Route::post('change-session', 'changeSession')->name('changeSession');
    });

    Route::group(['controller' => JenisSuratController::class, 'prefix' => 'jenis-surat', 'as' => 'jenis-surat.'], function () {
        Route::get('/', 'index')->name('index');
        Route::get('/list', 'list')->name('list');
        Route::get('/create', 'create')->name('create');
        Route::post('/', 'store')->name('store');
        Route::get('/{id}', 'show')->name('show');
        Route::get('/{id}/edit', 'edit')->name('edit');
        Route::put('/{id}', 'update')->name('update');
        Route::delete('/{id}', 'destroy')->name('destroy');

        Route::post('/dataTable', 'dataTable')->name('dataTable');
    });

    Route::group(['controller' => ContactController::class, 'prefix' => 'kontak', 'as' => 'kontak.'], function () {
        Route::get('/', 'index')->name('index');
        Route::get('/list', 'list')->name('list');
        Route::get('/create', 'create')->name('create');
        Route::post('/', 'store')->name('store');
        Route::get('/{id}', 'show')->name('show');
        Route::get('/{id}/edit', 'edit')->name('edit');
        Route::put('/{id}', 'update')->name('update');
        Route::delete('/{id}', 'destroy')->name('destroy');

        Route::post('/dataTable', 'dataTable')->name('dataTable');
    });

    Route::group(['controller' => KopSuratController::class, 'prefix' => 'kop-surat', 'as' => 'kop-surat.'], function () {
        Route::get('/', 'index')->name('index');
        Route::get('/create', 'create')->name('create');
        Route::post('/', 'store')->name('store');
        Route::post('/preview', 'previewStore')->name('kopSuratStore');
        Route::get('/{id}', 'show')->name('show');
        Route::get('/{id}/edit', 'edit')->name('edit');
        Route::put('/{id}', 'update')->name('update');
        Route::delete('/{id}', 'destroy')->name('destroy');

        Route::post('/dataTable', 'dataTable')->name('dataTable');
        Route::get('preview/{id}', 'preview')->name('preview');
    });

    Route::group(['controller' => FileManagerController::class, 'prefix' => 'file-manager', 'as' => 'file-manager.'], function () {
        Route::post('upload', 'uploadFile')->name('upload');
    });


    Route::group(['controller' => SuratMasukController::class, 'prefix' => 'surat-masuk', 'as' => 'surat-masuk.'], function () {
        Route::get('/', 'index')->name('index');
        Route::get('/{id}/detail', 'show')->name('show');
        Route::get('/{id}', 'detail')->name('detail');

        Route::post('/{id}/complete', 'completeSurat')->name('completeSurat');

        Route::post('/dataTable', 'dataTable')->name('dataTable');

        Route::post('/{id}/e-sign', 'storeEsign')->name('storeEsign');
    });

    Route::group(['controller' => SuratMasukController::class, 'prefix' => 'surat-expired', 'as' => 'surat-expired.'], function () {
        Route::get('/', 'indexExpired')->name('index');
        Route::post('/{id}/reminder', 'reminderSurat')->name('reminderSurat');
    });

    Route::group(['controller' => EsignerController::class, 'prefix' => 'document-esign', 'as' => 'document-esign.'], function () {
        Route::get('/', 'documentEsignIndex')->name('documentEsignIndex');
        Route::delete('/{id}', 'deleteDocument')->name('deleteDocument');
        Route::post('/{id}/resend', 'resendDocument')->name('resendDocument');
        Route::post('/list', 'documentEsignList')->name('documentEsignList');
    });

    Route::group(['controller' => RekapSuratController::class, 'prefix' => 'rekap-surat', 'as' => 'rekap-surat.'], function () {
        Route::get('/', 'index')->name('index');
        Route::get('/download-excel', 'downloadRekapExcel')->name('downloadRekapExcel');
        Route::post('/dataTable', 'dataTable')->name('dataTable');
    });

    Route::group(['controller' => UploadFileEsignController::class, 'prefix' => 'upload-document-esign', 'as' => 'upload-document-esign.'], function () {
        Route::get('/', 'index')->name('index');
        Route::get('/create', 'create')->name('create');
        Route::post('/', 'store')->name('store');
        Route::post('/dataTable', 'dataTable')->name('dataTable');
    });

    Route::group(['controller' => SuratKeluarController::class, 'prefix' => 'surat-keluar', 'as' => 'surat-keluar.'], function () {
        Route::get('/', 'index')->name('index');
        Route::get('/create', 'create')->name('create');
        Route::get('/{id}/detail', 'show')->name('show');
        Route::get('/{id}/revision', 'revision')->name('revision');
        Route::delete('/{id}', 'destroy')->name('destroy');
        Route::get('/history/{id}', 'history')->name('history');

        Route::post('/dataTable', 'dataTable')->name('dataTable');

        Route::post('/store', 'store')->name('store');
        Route::post('/{id}/revision', 'storeRevision')->name('revision');
        Route::post('/disposisi', 'disposisi')->name('disposisi');
        Route::post('/disposisi/esign-manual', 'esignManual')->name('esignManual');
        Route::post('/reply', 'reply')->name('reply');

        Route::post('/preview', 'previewStore')->name('previewStore');
        Route::get('/preview', 'preview')->name('preview');

        Route::get('/revision/{id}', 'listRevision')->name('listRevision');
    });

    Route::group(['middleware' => ['can:Role & Permission.List']], function () {
        Route::resource('role-permission', RolePermissionController::class);
        Route::post('role-permission/dataTable', [RolePermissionController::class, 'dataTable'])->name('role-permission.dataTable');
        Route::post('role-permission/dataTable/permission', [RolePermissionController::class, 'dataTablePermission'])->name('role-permission.dataTablePermission');
        Route::post('role-permission/store-permission', [RolePermissionController::class, 'storePermission'])->name('role-permission.store-permission')->middleware('can:Role & Permission.Create');
        Route::delete('role-permission/{id}/permission', [RolePermissionController::class, 'destroyPermission'])->name('role-permission.destroy-permission')->middleware('can:Role & Permission.Delete');
    });

    Route::group(['prefix' => 'pengaturan', 'as' => 'pengaturan.'], function () {
        Route::group(['prefix' => 'default', 'as' => 'default.', 'controller' => DefaultSettingController::class], function () {
            Route::get('/', 'index')->name('index');
            Route::post('/', 'store')->name('store');
        });
        Route::group(['prefix' => 'e-sign', 'as' => 'e-sign.', 'controller' => EsignerController::class], function () {
            Route::get('/', 'index')->name('index');
            Route::post('/', 'store')->name('store');
            Route::post('/try-connect-esign', 'tryConnectEsign')->name('try-connect-esign');
        });
    });


    Route::group(['controller' => NotificationController::class, 'prefix' => 'notification', 'as' => 'notification.'], function () {
        Route::get('/list', 'list')->name('list');
        Route::get('/count-no-read', 'countNoRead')->name('countNoRead');
        Route::post('make-as-read', 'makeAsRead')->name('makeAsRead');
        Route::get('/', 'index')->name('index');
    });


    Route::group(['controller' => ContactController::class, 'prefix' => 'contact', 'as' => 'contact.'], function () {
        Route::post('/', 'store')->name('store');

    });
});

Route::get('/surat/signature/{id}', [SuratMasukController::class, 'readSurat'])->name('checkSignature');
Route::get('/document/{id}', [UploadFileEsignController::class, 'documentEsign'])->name('documentEsign');
Route::get('/signature', [UploadFileEsignController::class, 'signature'])->name('signature');

Route::get('/generate-signature', function (Request $request) {
    $name = "REIHAN ANDIKA A";
    $signatureId = "03499315-3D7C-41BA-9DE2-23F1B5DF70ED";

    // Generate QR Code
    // $qrCode = QrCode::format('png')->size(200)->generate($signatureId);
    $qrCode = public_path('uploads/barcodes/9d8ed0aa-4b30-48a2-b5a9-7b8e7f2dc563.png');
    $qrImage = Image::make($qrCode);

    // Buat Canvas Utama
    $width = 600;
    $height = 250;
    $image = Image::canvas($width, $height, '#FFFFFF');

    // Tambahkan Border Kiri & Atas
    $borderColor = '#06a594';
    $image->rectangle(0, 0, 10, $height, function ($draw) use ($borderColor) {
        $draw->background($borderColor);
    });
    $image->rectangle(0, 0, $width, 10, function ($draw) use ($borderColor) {
        $draw->background($borderColor);
    });

    // Tambahkan Icon Check ✅
    $checkmark = Image::make(public_path('assets/images/logo-transparent.png'))->resize(20, 20);
    $image->insert($checkmark, 'top-left', 20, 20);

    // Tambahkan Teks "Signed by:"
    $image->text('Signed by:', 50, 40, function ($font) {
        // $font->file(public_path('fonts/Arial.ttf'));
        // $font->size(5);
        $font->color('#000');
    });

    // Tambahkan QR Code
    $image->insert($qrImage, 'top-left', 50, 60);

    // Tambahkan Nama di Sebelah QR Code
    $image->text($name, 270, 120, function ($font) {
        // $font->file(public_path('fonts/Arial-Bold.ttf'));
        // $font->size(5);
        $font->color('#1B1B1B');
    });

    // Tambahkan ID di Bawah QR Code
    $image->text($signatureId, 50, 230, function ($font) {
        // $font->file(public_path('fonts/Arial.ttf'));
        // $font->size(5);
        $font->color('#000');
    });

    return $image->response('png');
});

require __DIR__ . '/auth.php';

Route::get('/test-db', function () {
    try {
        DB::connection()->getPdo();
        return "Database connection is successful.";
    } catch (\Exception $e) {
        return "Could not connect to the database. Please check your configuration. Error: " . $e->getMessage();
    }
});
