<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\EsignerController;
use App\Http\Controllers\KopSuratController;
use App\Http\Controllers\PenggunaController;
use App\Http\Controllers\JenisSuratController;
use App\Http\Controllers\SuratKeluarController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});


Route::group(['controller' => PenggunaController::class, 'prefix' => 'pengguna', 'as' => 'pengguna.'], function () {
    Route::get('/list-group', 'listGroup')->name('list');
    Route::get('/list', 'list')->name('list');
});

Route::group(['controller' => KopSuratController::class, 'prefix' => 'kop-surat', 'as' => 'kop-surat.'], function () {
    Route::get('/list', 'list')->name('list');
});

Route::group(['controller' => JenisSuratController::class, 'prefix' => 'jenis-surat', 'as' => 'jenis-surat.'], function () {
    Route::get('/list', 'list')->name('list');
});

Route::group(['controller' => ContactController::class, 'prefix' => 'contact', 'as' => 'contact.'], function () {
    Route::get('/list', 'list')->name('list');
});

Route::group(['controller' => EsignerController::class, 'prefix' => 'e-sign', 'as' => 'e-sign.'], function () {
    Route::get('/webhook', 'webhook')->name('webhook');
    Route::post('/webhook/signing', 'webhookSigning')->name('webhook-signing');
});


// Route::group(['controller' => SuratKeluarController::class, 'prefix' => 'surat-keluar', 'as' => 'surat-keluar.'], function () {
//     Route::post('/store', 'store')->name('store');
// });

